/* @import must be at top of file, otherwise CSS will not work */
@import url('https://cdn.fonts.net/t/1.css?apiType=css&projectid=794487f0-0800-11ed-a01a-02d848b7f97a');

@import './variables.css';

@tailwind base;
@import './overrides.css';

@tailwind components;
@import './components.css';

@tailwind utilities;
@import './utilities.css';

@import './override/gigya-override.css';

@import './override/bazaarvoice-override.css';
/*
  Base styles for the project
  ----------------------------
  * Styles for body and html
  * Styles for headings
  * Font definitions
 */
@layer base {
    *,
    *:before,
    *:after {
        box-sizing: inherit;
    }

    /*
     ========== Elgato Fonts Start: ==========
    */
    @font-face {
        font-family: 'Univers55Roman';
        font-style: normal;
        font-stretch: normal;
        font-display: swap;
        src: url('/fonts/Univers/Univers55Roman_normal_normal.woff2')
                format('woff2'),
            url('/fonts/Univers/Univers55Roman_normal_normal.woff')
                format('woff');
    }
    @font-face {
        font-family: 'Univers67BoldCondensed';
        font-style: normal;
        font-stretch: condensed;
        font-display: swap;
        src: url('/fonts/Univers/Univers67BoldCondensed_normal_condensed.woff2')
                format('woff2'),
            url('/fonts/Univers/Univers67BoldCondensed_normal_condensed.woff')
                format('woff');
    }
    @font-face {
        font-family: 'Univers65Bold';
        font-style: normal;
        font-stretch: normal;
        font-display: swap;
        src: url('/fonts/Univers/Univers65Bold_normal_normal.woff2')
                format('woff2'),
            url('/fonts/Univers/Univers65Bold_normal_normal.woff')
                format('woff');
    }
    @font-face {
        font-family: 'Univers55Oblique';
        font-style: italic;
        font-stretch: normal;
        font-display: swap;
        src: url('/fonts/Univers/Univers55Oblique_italic_normal.woff2')
                format('woff2'),
            url('/fonts/Univers/Univers55Oblique_italic_normal.woff')
                format('woff');
    }
    @font-face {
        font-family: 'UniversLTStd';
        font-style: normal;
        font-stretch: normal;
        font-display: swap;
        src: url('/fonts/UniversLT/UniversLTStd.woff2') format('woff2'),
            url('/fonts/UniversLT/UniversLTStd.woff') format('woff');
    }
    @font-face {
        font-family: 'UniversLTStdBold';
        font-style: normal;
        font-stretch: normal;
        font-display: swap;
        src: url('/fonts/UniversLT/UniversLTStd-Bold.woff2') format('woff2'),
            url('/fonts/UniversLT/UniversLTStd-Bold.woff') format('woff');
    }
    @font-face {
        font-family: 'SignPainter';
        font-style: normal;
        font-stretch: normal;
        font-display: swap;
        src: url('/fonts/SignPainter/SignPainter-regular.woff') format('woff');
    }

    @font-face {
        font-family: 'SourceCodePro';
        font-style: normal;
        font-stretch: normal;
        font-display: swap;
        src: url('/fonts/SourceCodePro/SourceCodePro-Regular.ttf.woff2')
                format('woff2'),
            url('/fonts/SourceCodePro/SourceCodePro-Regular.ttf.woff')
                format('woff');
    }

    /*
     ========== Elgato Fonts End ==========
    */

    html {
        height: 100%;
        touch-action: manipulation;
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        -webkit-tap-highlight-color: transparent;

        &.push-menu-active,
        &.push-menu-active body {
            @screen md-max {
                @apply overflow-hidden h-full box-border;
            }
        }
    }

    html,
    body {
        font-family: var(--base-font-family);
        font-size: var(--base-font);
        text-rendering: optimizeLegibility;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        @apply bg-primary font-primary box-border;
        scroll-behavior: smooth;
    }

    body {
        @apply min-h-full m-0 overflow-x-hidden text-base font-univers55Roman;
    }

    body.noscroll {
        overflow: hidden !important;
    }

    a {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    h1.hero-h1 {
        @apply font-secondary text-7.5xl font-semibold tracking-tight;
    }

    .gigya-input-checkbox {
        appearance: auto !important;
    }

    .gigantic-title {
        @apply uppercase;
        @apply text-gigantic;
        @apply leading-tight-md-max;

        @screen md-max {
            @apply text-gigantic-md-max;
            @apply leading-extra-tight-md-max;
        }
    }

    h1,
    .h1 {
        /* use tailwind selectors in order to make sure that all of the sizes and classes
        are the same in every place, meaning that any jsx with className="text-2xl"
        & has the same size as h1 headings */
        @apply text-h1;
        @apply leading-extra-tight;
        @apply uppercase;

        @screen md-max {
            @apply text-h1-md-max;
            @apply leading-tight-md-max;
        }
    }

    h1:lang(ru),
    .h1:lang(ru),
    h1:lang(ja),
    .h1:lang(ja),
    h1:lang(ko),
    .h1:lang(ko),
    h1:lang(zh),
    .h1:lang(zh) {
        font-style: normal;
        font-weight: 700;
        font-size: 48px;
        line-height: 53px;

        @screen md-max {
            font-style: normal;
            font-weight: 700;
            font-size: 32px;
            line-height: 38px;
        }
    }

    h2,
    .h2 {
        @apply text-h2;
        @apply leading-tight-md-max;
        @apply uppercase;

        @screen md-max {
            @apply text-h2-md-max;
        }
    }

    h2:lang(ru),
    .h2:lang(ru),
    h2:lang(ja),
    .h2:lang(ja),
    h2:lang(ko),
    .h2:lang(ko),
    h2:lang(zh),
    .h2:lang(zh) {
        font-style: normal;
        font-weight: 700;
        font-size: 32px;
        line-height: 38px;

        @screen md-max {
            font-style: normal;
            font-weight: 700;
            font-size: 26px;
            line-height: 31px;
        }
    }

    h3,
    .h3 {
        @apply text-h3;
        @apply leading-tight-md-max;
        @apply uppercase;

        @screen md-max {
            @apply text-h3-md-max;
        }
    }

    h3:lang(ru),
    .h3:lang(ru),
    h3:lang(ja),
    .h3:lang(ja),
    h3:lang(ko),
    .h3:lang(ko),
    h3:lang(zh),
    .h3:lang(zh) {
        font-style: normal;
        font-weight: 700;
        font-size: 24px;
        line-height: 29px;

        @screen md-max {
            font-style: normal;
            font-weight: 700;
            font-size: 22px;
            line-height: 26px;
        }
    }

    h4,
    .h4 {
        @apply text-h4;
        @apply leading-tight-md-max;
        @apply uppercase;

        @screen md-max {
            @apply text-h4-md-max;
        }
    }

    h4:lang(ru),
    .h4:lang(ru),
    h4:lang(zh),
    .h4:lang(zh),
    h4:lang(ko),
    .h4:lang(ko),
    h4:lang(ja),
    .h4:lang(ja) {
        font-style: normal;
        font-weight: 700;
        font-size: 20px;
        line-height: 24px;

        @screen md-max {
            font-style: normal;
            font-weight: 700;
            font-size: 18px;
            line-height: 22px;
        }
    }

    h5,
    .h5,
    .form-label {
        @apply text-h5;
        @apply leading-tight-md-max;
        @apply uppercase;

        @screen md-max {
            @apply text-h5-md-max;
        }
    }

    h5:lang(ru),
    .h5:lang(ru),
    .form-label:lang(ru),
    h5:lang(ja),
    .h5:lang(ja),
    .form-label:lang(ja),
    h5:lang(ko),
    .h5:lang(ko),
    .form-label:lang(ko),
    h5:lang(zh),
    .h5:lang(zh),
    .form-label:lang(zh) {
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 19px;
    }

    h6,
    .h6 {
        @apply text-h6;
        @apply leading-tight-md-max;
        @apply uppercase;

        @screen md-max {
            @apply text-h6-md-max;
        }
    }

    h6:lang(ru),
    .h6:lang(ru),
    h6:lang(ja),
    .h6:lang(ja),
    h6:lang(ko),
    .h6:lang(ko),
    h6:lang(zh),
    .h6:lang(zh) {
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        line-height: 14px;
    }

    h1,
    .h1,
    h2,
    .h2,
    h3,
    .h3,
    h4,
    .h4,
    h5,
    .h5,
    h6,
    .h6,
    .gigantic-title,
    .form-label {
        @apply font-univers67BoldCondensed;
    }

    h1:lang(ru),
    .h1:lang(ru),
    h2:lang(ru),
    .h2:lang(ru),
    h3:lang(ru),
    .h3:lang(ru),
    h4:lang(ru),
    .h4:lang(ru),
    h5:lang(ru),
    .h5:lang(ru),
    h6:lang(ru),
    .h6:lang(ru),
    .gigantic-title:lang(ru),
    .form-label:lang(ru),
    h1:lang(ja),
    .h1:lang(ja),
    h2:lang(ja),
    .h2:lang(ja),
    h3:lang(ja),
    .h3:lang(ja),
    h4:lang(ja),
    .h4:lang(ja),
    h5:lang(ja),
    .h5:lang(ja),
    h6:lang(ja),
    .h6:lang(ja),
    .gigantic-title:lang(ja),
    .form-label:lang(ja),
    h1:lang(ko),
    .h1:lang(ko),
    h2:lang(ko),
    .h2:lang(ko),
    h3:lang(ko),
    .h3:lang(ko),
    h4:lang(ko),
    .h4:lang(ko),
    h5:lang(ko),
    .h5:lang(ko),
    h6:lang(ko),
    .h6:lang(ko),
    .gigantic-title:lang(ko),
    .form-label:lang(ko),
    h1:lang(zh),
    .h1:lang(zh),
    h2:lang(zh),
    .h2:lang(zh),
    h3:lang(zh),
    .h3:lang(zh),
    h4:lang(zh),
    .h4:lang(zh),
    h5:lang(zh),
    .h5:lang(zh),
    h6:lang(zh),
    .h6:lang(zh),
    .gigantic-title:lang(zh),
    .form-label:lang(zh) {
        font-style: normal;
        font-family: 'Arial', sans-serif;
        font-weight: 700;
    }

    .h1-neo,
    .h2-neo,
    .h3-neo,
    .h4-neo {
        @apply normal-case;
        @apply font-universLtStd;
        /*font-family: "UniversLTStd";*/
    }

    .h1-neo {
        @apply text-h1-neo;
        line-height: 150%;

        @screen md-max {
            @apply text-h1-neo-md-max;
        }
    }

    .h2-neo {
        @apply text-h2-neo;
        line-height: 150%;

        @screen md-max {
            @apply text-h2-neo-md-max;
        }
    }

    .h3-neo {
        @apply text-h3-neo;
        line-height: 150%;

        @screen md-max {
            @apply text-h3-neo-md-max;
        }
    }

    .h4-neo {
        @apply text-h4-neo;
        line-height: 150%;

        @screen md-max {
            @apply text-h4-neo-md-max;
        }
    }

    .sub-headline {
        @apply font-univers55Roman;
        @apply text-sub-headline;
        @apply leading-tight-md-max;

        @screen md-max {
            @apply text-sub-headline-md-max;
        }
    }

    .btn,
    .button-text,
    .button-text-underlined,
    .button-text-bold {
        @apply font-univers55Roman;
        @apply text-button;
        @apply leading-none;
        letter-spacing: 0.025rem;

        @screen md-max {
            @apply text-button-md-max;
        }
    }

    .button-text-bold {
        @apply font-univers65Bold;
    }

    .btn-underlined,
    .button-text-underlined {
        @apply underline;
    }

    .button-text-underlined-bold {
        @apply underline font-univers65Bold;
    }

    .btn-tertiary.btn-sm,
    .button-text-small {
        @apply font-univers55Roman;
        @apply text-button-small;
        line-height: 14px;
        letter-spacing: 0.025rem;
    }

    .button-text-large,
    .button-text-large:lang(ru),
    .button-text-large:lang(ja),
    .button-text-large:lang(ko),
    .button-text-large:lang(zh) {
        @apply font-univers55Roman;
        @apply text-button-large;
        @apply leading-none;
        letter-spacing: 0.025rem;

        @screen md-max {
            @apply text-button-large-md-max;
            @apply tracking-wider;
        }
    }

    .body-copy,
    .body-copy:lang(ru),
    .body-copy:lang(ja),
    .body-copy:lang(ko),
    .body-copy:lang(zh) {
        @apply font-univers55Roman;
        @apply text-body-copy-md-max;
        @apply leading-tight;
        @screen md {
            @apply text-body-copy;
            @apply leading-extra-tight-md-max;
        }
    }

    .small-copy,
    .small-copy:lang(ru),
    .small-copy:lang(ja),
    .small-copy:lang(ko),
    .small-copy:lang(zh) {
        @apply font-univers55Roman;
        @apply text-small-copy-md-max;
        @apply leading-tight;
        @screen md {
            @apply text-small-copy;
            @apply leading-extra-tight-md-max;
        }
    }

    .small-copy-mobile {
        @apply font-univers55Roman;
        @apply text-small-copy-md-max;
        @apply leading-tight;
    }

    .xs-copy {
        @apply text-xs-copy-md-max;
        @apply leading-tight;
        @screen md {
            @apply text-xs-copy;
            @apply leading-extra-tight-md-max;
        }
    }
    .body-copy.bold,
    .small-copy.bold,
    .xs-copy.bold,
    .bold {
        @apply font-univers65Bold;
    }
    .body-copy.em,
    .small-copy.em,
    .xs-copy.em,
    .em {
        @apply font-univers55Oblique;
    }

    .body-copy-neo {
        @apply font-universLtStd;
        @apply text-body-copy-neo-md-max;
        line-height: 150%;
        @screen md {
            @apply text-body-copy-neo;
        }
    }

    .small-copy-neo {
        @apply font-universLtStd;
        @apply text-small-copy-neo-md-max;
        line-height: 150%;
        @screen md {
            @apply text-small-copy-neo;
        }
    }

    .atf-text-bold,
    .atf-text-bold-underlined {
        @apply font-univers65Bold;
        font-size: 14px;
        line-height: 14px;
        letter-spacing: 0.025rem;
    }

    .atf-text-bold.atf-text-bold--neo {
        @apply font-universLtStd;
        @apply text-button-small-neo-md-max;
        letter-spacing: normal;
        line-height: 100%;
        @screen lg {
            @apply text-button-small-neo;
        }
    }

    .atf-text-bold:lang(ru),
    .atf-text-bold-underlined:lang(ru),
    .atf-text-bold:lang(ja),
    .atf-text-bold-underlined:lang(ja),
    .atf-text-bold:lang(ko),
    .atf-text-bold-underlined:lang(ko),
    .atf-text-bold:lang(zh),
    .atf-text-bold-underlined:lang(zh) {
        font-style: normal;
        font-family: 'Arial', sans-serif;
        font-weight: 700;
    }

    .atf-text-bold-underlined {
        text-decoration-line: underline;
    }

    .rich-text ul {
        @apply list-disc list-outside ml-8;
    }
    .rich-text ul li {
        @apply pl-1 text-left;
    }
    .rich-text ol li {
        @apply text-left;
    }
    .rich-text ol {
        @apply list-decimal list-inside ml-6px;
    }
    .rich-text * + ul,
    .rich-text * + p {
        @apply mt-4px;
    }
    .rich-text p + p {
        @apply mt-16px;

        @screen md {
            @apply mt-24px;
        }
    }
    .rich-text * + h2,
    .rich-text * + h3,
    .rich-text * + h4,
    .rich-text * + h5,
    .rich-text * + h6 {
        @apply mt-24px;
    }

    .rich-text h1,
    .rich-text h2,
    .rich-text h3,
    .rich-text h4,
    .rich-text h5,
    .rich-text h6 {
        @apply mb-8px;
    }

    .bg-dark {
        @apply bg-primary text-primary;
    }
    .mobileMenuOpen {
        height: 100vh;
        overflow-y: hidden;
    }
    .zoom-open,
    .zoom-open body {
        @apply overflow-hidden;
    }
    .modal-open-mob {
        @apply overflow-hidden md:overflow-auto;
    }
    .modal-open-mob .nav-sticky,
    .zoom-open .nav-sticky {
        @apply z-0 !important;
    }
    .zoom-open .full-screen-mode {
        z-index: 9999999999;
    }
    .link {
        @apply underline;
        &.disabled {
            @apply select-none opacity-50 pointer-events-none;
        }
    }
    input,
    optgroup,
    select,
    textarea,
    .form-input {
        @apply font-univers55Roman text-base font-normal;
    }
    .form-checkbox,
    .form-radio {
        padding-bottom: 0 !important;
    }

    .lock-y {
        @screen md-max {
            @apply overflow-y-hidden;
        }
    }

    /* Forms */
    input,
    input.form-input,
    .form-select,
    select.form-select,
    .form-text {
        @apply w-full border border-mid-grey-2 rounded-lg focus:shadow-none focus:border-content-blue;
        @apply text-small-copy leading-none;
        @apply px-8 py-4;
    }
    .gigya-input-checkbox {
        appearance: auto;
    }

    .form-text {
        @apply leading-extra-tight-md-max;
    }

    input.form-input--invalid,
    .form-input--invalid {
        @apply border-radical-red-1;
    }

    .input-wrapper + .input-wrapper {
        @apply mt-14;
    }

    .form-label + .form-input,
    .form-label + .form-select,
    .form-label + .form-text {
        @apply mt-8px;
    }

    .form-checkbox,
    .form-radio {
        @apply w-16px h-16px;
        @apply text-content-blue;
        @apply rounded-full border-charcoal;
    }

    .checkbox-label,
    .radio-label {
        @apply cursor-pointer relative;
    }

    .checkbox-label {
        @apply inline-flex;
    }

    [type='checkbox'].form-check {
        @apply cursor-pointer box-content;
    }

    [type='checkbox'].form-check.rounded-full {
        @apply box-border;
    }

    [type='checkbox'].form-check:checked {
        @apply text-white bg-content-blue border-content-blue;
        background-image: url("data:image/svg+xml,%3csvg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M11.1869 0.813118C11.3821 1.00838 11.3821 1.32496 11.1869 1.52023L4.85355 7.85356C4.65829 8.04882 4.34171 8.04882 4.14645 7.85356L0.813113 4.52023C0.61785 4.32496 0.61785 4.00838 0.813113 3.81312C1.00837 3.61786 1.32496 3.61786 1.52022 3.81312L4.5 6.7929L10.4798 0.813118C10.675 0.617856 10.9916 0.617856 11.1869 0.813118Z' stroke='white' /%3e%3c/svg%3e");
        background-size: 60%;
        background-position: center center;
        background-repeat: no-repeat;
    }

    .pinned {
        overflow: hidden;
        position: sticky !important;
        top: 0;
        bottom: 0;
        left: 0;
        width: 100%;
    }

    .neo-label-text {
        @apply font-univers55Roman;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
    }
}

/* This Code should not be optimized as it mostly comes from third party libraries */

.text-gamer-rush {
    background: var(--gamer-rush-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-streamlined {
    background: var(--streamlined-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.text-podcasted {
    background: var(--podcasted-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

#bv-mboxzone-lightbox.bv-mbox-opened #bv-mbox-lightbox-list {
    max-height: calc(100vh - 100px) !important;
    overflow-y: auto !important;
}

#bv-mboxzone-lightbox
    .bv-cv2-cleanslate
    .bv-submission
    .bv-fieldsets
    .bv-form-actions
    .bv-submission-button-submit,
#bv-mboxzone-lightbox .bv-form-actions-submit,
#bv-mboxzone-lightbox .bv-ask-question,
.bv_modal_component_container button.bv_button_buttonFull,
.bv_modal_outer_content {
    border-radius: 6px !important;
}

.gigya-screen-dialog-mobile .gigya-screen-dialog-close {
    display: flex;
}

/* Hide the closed OneTrust button */
.ot-floating-button {
    display: none !important;
}

/* Set correct color Accept All Onetrust button  */
#onetrust-consent-sdk #onetrust-accept-btn-handler {
    opacity: 1;
}
#onetrust-consent-sdk #onetrust-accept-btn-handler:hover {
    opacity: 0.7;
}

#bv_components_histogram .bv_histogram_row_prefix {
    display: flex !important;
    align-items: center !important;
}
