.root {
    :global {
        .table-responsive {
            @apply block w-full overflow-x-auto;
        }

        .font-bold, strong {
            @apply font-normal
        }
    }


    table.order-items-body-table, table {
        @apply w-full bg-primary overflow-hidden;
        thead {
            :global {
                @screen md {
                    tr {
                        &.bg-accents-8 {
                            @apply border border-white-smoke;
                            background: var(--white-smoke);

                            th {
                                border-bottom: none;
                            }

                            th:first-child {
                                width: 12rem;
                            }

                            th:last-child {
                                width: 12rem;
                            }
                        }

                        @apply sm:table-row mb-2 sm:mb-0;
                    }
                }
            }

            @apply text-p-1;
            display: table-header-group;
            @screen md-max {
                display: none;
            }
            th {
                @apply text-left border-b font-normal font-univers65Bold;
                padding-bottom: 5px;
                font-size: 12px;
                line-height: 150%;
            }

            :global {
                .action {
                    @apply w-40;
                }
            }
        }

        tbody {
            tr {
                :global {
                    .col {
                        border-top-width: 0 !important;
                        border-bottom-width: 0 !important;
                        span {
                            @apply text-xs-copy-md-max;
                            @apply leading-tight;
                            @screen md {
                                @apply text-xs-copy;
                                @apply leading-extra-tight-md-max;
                            }
                            &.content {
                                @screen md-max {
                                    @apply font-univers65Bold;
                                }
                            }
                        }

                        &:first-child {
                            @apply flex justify-center items-center py-12px;
                            @screen md-max {
                                padding-top: 0;
                            }
                        }
                    }

                    /* Desktop/Copy/Extra Small Copy/Extra Small Copy (Bold) */
                    .product-item-name, .col.status {
                        @apply text-xs-copy-md-max;
                        @apply leading-tight;
                        @apply font-univers65Bold font-normal;

                        @screen md {
                            @apply text-xs-copy;
                            @apply leading-extra-tight-md-max;
                        }
                    }

                    td {
                        //&.subtotal {
                        //    @apply font-univers65Bold;
                        //    .font-bold {
                        //        @apply font-normal;
                        //    }
                        //}
                        //.cart-price {
                        //    @apply font-univers65Bold;
                        //}
                        select {
                            @apply block appearance-none w-full max-w-xs md:w-1/3 border-2  text-p-1 py-3 px-4 pr-8 focus:shadow-none focus:border-mid-grey-1
                            font-univers55Roman;
                            &.qtyreturn {
                                width: 59px !important;
                            }

                            width: 105px !important;
                            height: 44px;
                            @apply border-mid-grey-1;
                            border-radius: 4px !important;
                        }

                        textarea {
                            @apply font-univers55Roman border rounded-lg mt-3.5 font-normal;
                            border-color: var(--white-smoke);
                            color: inherit;
                            width: 105px;
                            height: 44px;

                            @screen md-max {
                                margin-left: -80px;
                                width: calc(100% + 80px)
                            }
                        }

                        &:first-child:not(.order-id) {
                            @apply flex justify-center items-center
                        }

                        @apply border-t border-b px-0 py-5;
                        font-size: 12px;
                        line-height: 150%;
                    }

                }
                @apply sm:table-row mb-2 sm:mb-0 border-t md:border-t-0;
                border-style: solid;

                &:nth-child(1) {
                    @apply border-t-0;
                    @screen md-max {
                        border-top-width: 1px !important;
                    }
                }
            }
            :global {
                tr.relative {
                    td:not(:first-child ) {
                        padding-left: 5px;
                        padding-right: 5px;
                        .product-item-name {
                                @apply font-univers65Bold
                        }
                    }
                }
            }
            @screen md-max {
                tr {
                    display: grid !important;
                    border-style: solid;
                    border-color: var(--light-grey-1);
                    border-width: 1px;
                    grid-gap: 7px;
                    @apply pt-3 pb-2 mb-4;
                    @screen md-max {
                        grid-template-columns: 100px min-content auto;
                    }
                    td {
                        display: inline-grid;
                        padding: 0;
                        text-align: left;
                        img {
                            height: unset !important;
                            min-height: unset !important;
                            margin: unset !important;
                        }
                        &:first-child {
                            @apply col-start-1 col-end-2 row-start-1 row-end-6;
                            @screen md-max {
                                align-items: flex-start !important;
                                padding-top: 0;
                            }
                        }

                        &:nth-child(2) {
                            @apply col-start-2 col-end-4 row-start-1 row-end-2;
                        }

                        &:nth-child(3) {
                            @apply col-start-2 col-end-4 row-start-2 row-end-3;
                            text-align: left;
                        }

                        &:nth-child(4) {
                            @apply col-start-2 col-end-3 row-start-3 row-end-4;
                        }

                        &:nth-child(5) {
                            @apply col-start-2 col-end-4 row-start-4 row-end-5;
                        }

                        &:nth-child(6) {
                            @apply col-start-2 col-end-3 row-start-5 row-end-6;
                            text-align: left;
                        }

                        &:nth-child(7) {
                            @apply col-start-2 col-end-3 row-start-6 row-end-7 py-4;
                            padding-right: 5px;
                        }

                        &:nth-child(8) {
                            @apply col-start-3 col-end-4 row-start-6 row-end-7 py-4;
                        }

                        li {
                            text-align: left;
                        }

                        span {
                            .content {
                                text-align: left;
                            }
                        }
                    }
                }
            }
            @apply text-p-1;
            :global {
                .order-id {
                    @apply font-univers65Bold;
                }

                .action {
                    @apply w-40;
                    .actions {
                        @apply flex space-x-1 justify-end md:justify-start;
                    }

                    a,
                    .link {
                        &:not(.no-link) {
                            @apply cursor-pointer underline inline-block;
                            font-weight: normal;
                        }
                    }
                    a:nth-child(2)::before{
                        content: " | ";
                    }
                }
            }
        }
    }

}
