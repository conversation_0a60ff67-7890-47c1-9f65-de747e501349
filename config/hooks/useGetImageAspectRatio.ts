import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'

export const getImageAspectRatio = ({
    imageOrVideo,
    posterImage
}: {
    imageOrVideo?: ImageType | VideoType
    posterImage?: ImageType
}): number | undefined => {
    const isVideo = imageOrVideo?.file?.contentType?.includes('video') ?? false
    const image = !isVideo ? (imageOrVideo as ImageType) : undefined
    const imageWidth = image?.file.details.image.width
    const imageHeight = image?.file.details.image.height
    if (imageWidth && imageHeight) {
        return imageWidth / imageHeight
    }
    const posterImageWidth = posterImage?.file.details.image.width
    const posterImageHeight = posterImage?.file.details.image.height
    if (posterImageWidth && posterImageHeight) {
        return posterImageWidth / posterImageHeight
    }
}

export const getCloudinaryImageAspectRatio = ({
    cloudinaryImageOrVideo,
    cloudinaryPosterImage
}: {
    cloudinaryImageOrVideo?: CloudinaryMedia
    cloudinaryPosterImage?: CloudinaryMedia
}): number | undefined => {
    const imageWidth = cloudinaryImageOrVideo?.width ?? 0
    const imageHeight = cloudinaryImageOrVideo?.height ?? 0
    if (imageWidth && imageHeight) {
        return imageWidth / imageHeight
    }
    const posterImageWidth = cloudinaryPosterImage?.width ?? 0
    const posterImageHeight = cloudinaryPosterImage?.height ?? 0
    if (posterImageWidth && posterImageHeight) {
        return posterImageWidth / posterImageHeight
    }
}
