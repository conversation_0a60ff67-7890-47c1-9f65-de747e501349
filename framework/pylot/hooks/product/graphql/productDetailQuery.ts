import { onConfigurableProductFragment } from './Fragments/onConfigurableProductFragment'
import { ProductItemFragment } from './Fragments/ProductItemFragment'
import { onGiftCardProductFragment } from './Fragments/onGiftCardProductFragment'

export const productDetailQuery = /* GraphQL */ `
    query productDetail($searchCriteria: [SearchCriteriaInput!]!, $pageSize: Int = 12, $currentPage: Int = 1) {
        productDetail: products(
            searchCriteria: $searchCriteria
            pageSize: $pageSize
            currentPage: $currentPage
        ) {
            items {
                uid
                ${ProductItemFragment}
                bundle_products {
                    image
                    name
                    sku
                    qty
                    not_sellable
                    price_msrp {
                        code
                        price
                        __typename
                    }
                    price_bundled {
                        code
                        price
                        __typename
                    }
                    product_id
                    variant_id
                    backorder {
                        available
                        date
                        expected_delivery_date
                    }
                    deliverBy
                    excludedDeliveryDate
                }
                short_description {
                    __typename
                    html
                }
                description {
                    __typename
                    html
                }
                media_gallery {
                    __typename
                    url
                    disabled
                    label
                    position
                    type
                    ... on ProductVideo {
                        video_content {
                            media_type
                            video_provider
                            video_url
                            video_title
                            video_description
                            video_metadata
                        }
                    }
                }
                categories {
                    __typename
                    name
                    url_path
                    breadcrumbs {
                        __typename
                        category_uid
                        category_name
                    }
                }
                ${onConfigurableProductFragment}
                ${onGiftCardProductFragment}
                related_products {
                    ${ProductItemFragment}
                    ${onConfigurableProductFragment}
                }
                meta_title
                meta_keyword
                meta_description
                canonical_url
                cross_sell_skus
                related_accessories_skus
                customers_also_bought_skus
                customers_may_also_like_skus
                bundle_and_save_skus
                elgato_bundle_and_save_skus
                elgato_bundle_and_save_informations {
                    bundle_item_sku
                    price {
                        c
                        p
                        fp
                        d
                        sp
                    }
                    discount_percent
                    stackable
                }
                show_third_party_promo
                promo_campaign_message_map
                promo_campaigns {
                    campaign
                }
                deliverBy
                excludedDeliveryDate
                not_sellable
                backend_status
                available_countries
                inventory
                backorder {
                    available
                    date
                    expected_delivery_date
                }
                show_variants
                free_shipping
                tech_specs {
                    code
                    value
                    is_comparable
                }
                gtin
                pimId
                max_allowed_quantity
            }
            queryID
        }
    }
`

export const relatedProductDetailQuery = /* GraphQL */ `
    query productDetail($searchCriteria: [SearchCriteriaInput!]!, $pageSize: Int = 12, $currentPage: Int = 1) {
        productDetail: products(
            searchCriteria: $searchCriteria
            pageSize: $pageSize
            currentPage: $currentPage
        ) {
            items {
                __typename
                uid
                name
                sku
                url_key
                stock_status
                max_allowed_quantity
                image {
                    url
                }
                media_gallery {
                    __typename
                    url
                    type
                }
                bundle_products {
                    image
                    name
                    sku
                    qty
                    not_sellable
                    price_msrp {
                        code
                        price
                        __typename
                    }
                    price_bundled {
                        code
                        price
                        __typename
                    }
                    product_id
                    variant_id
                    backorder {
                        available
                        date
                    }
                    deliverBy
                    excludedDeliveryDate
                }
                short_description {
                    __typename
                    html
                }
                description {
                    __typename
                    html
                }
                ${onConfigurableProductFragment}
                price_range {
                    __typename
                    minimum_price {
                        __typename
                        regular_price {
                            __typename
                            value
                            currency
                        }
                        final_price {
                            __typename
                            value
                            currency
                        }
                        discount {
                            __typename
                            amount_off
                        }
                    }
                    maximum_price {
                        __typename
                        final_price {
                            __typename
                            value
                            currency
                        }
                    }
                }
                show_third_party_promo
                promo_campaign_message_map
                promo_campaigns {
                    __typename
                    campaign
                }
                deliverBy
                excludedDeliveryDate
                elgato_bundle_and_save_skus
                not_sellable
                backend_status
                inventory
                backorder {
                    available
                    date
                }
                gtin
                pimId
            }
            queryID
        }
    }
`
