export const onConfigurableProductFragment = /* GraphQL */ `
    ... on ConfigurableProduct {
        configurable_options {
            __typename
            attribute_code
            attribute_id
            id
            uid
            label
            values {
                __typename
                default_label
                label
                store_label
                use_default_value
                value_index
                uid
                swatch_data {
                    __typename
                    value
                    ... on ImageSwatchData {
                        thumbnail
                        __typename
                    }
                }
            }
        }
        variants {
            __typename
            attributes {
                __typename
                uid
                code
                value_index
                label
            }
            product {
                __typename
                id
                uid
                name
                url_key
                badge
                tech_specs {
                    code
                    value
                    is_comparable
                }
                thumbnail {
                    url
                }
                image {
                   url
                }
                price_range {
                    __typename
                    minimum_price {
                        __typename
                        regular_price {
                            __typename
                            value
                            currency
                        }
                        final_price {
                            __typename
                            value
                            currency
                        }
                        discount {
                            __typename
                            amount_off
                        }
                    }
                }
                media_gallery {
                    __typename
                    disabled
                    label
                    position
                    url
                    type
                    ... on ProductVideo {
                        video_content {
                            media_type
                            video_provider
                            video_url
                            video_title
                            video_description
                            video_metadata
                            __typename
                        }
                        __typename
                    }
                }
                deliverBy
                excludedDeliveryDate
                sku
                stock_status
                not_sellable
                child_sort_order
                show_third_party_promo
                promo_campaign_message_map
                promo_campaigns {
                    __typename
                    campaign
                }
                elgato_bundle_and_save_skus
                max_allowed_quantity
            }
        }
    }
`
