import { CountryCodeEnum } from '@pylot-data/enums/CountryCodeEnum'
import { useCallback } from 'react'
import { useQuery } from '../use-query'
import { graphqlFetch } from '@pylot-data/graphqlFetch'
import { validateAddressQuery } from './graphql/validateAddressQuery'
import { useCountries } from '@corsairitshopify/pylot-utils'
import { useBaseUrl } from '@config/hooks/useBaseUrl'

export interface AddressInputVariableInterface {
    'street[0]': string
    'street[1]': string
    city: string
    company: string
    country_code: CountryCodeEnum
    default_billing: boolean
    default_shipping: boolean
    firstname: string
    lastname: string
    postcode: string
    region: string
    street: string[]
    telephone: string
    isMelissaData?: boolean
}

export type MelissaData = {
    RecordID: string
    Results: string
    FormattedAddress: string
    Organization: string
    AddressLine1: string
    AddressLine2?: string
    AddressLine3?: string
    AddressLine4?: string
    AddressLine5?: string
    AddressLine6?: string
    AddressLine7?: string
    AddressLine8?: string
    SubPremises: string
    DoubleDependentLocality: string
    DependentLocality: string
    Locality: string
    SubAdministrativeArea: string
    AdministrativeArea: string
    PostalCode: string
    PostalCodeType: 'P' | 'M' | 'U' | 'Empty'
    AddressType: string
    AddressKey: string
    SubNationalArea: string
    CountryName: string
    CountryISO3166_1_Alpha2: string
    CountryISO3166_1_Alpha3: string
    CountryISO3166_1_Numeric: string
    CountrySubdivisionCode: string
    Thoroughfare: string
    ThoroughfarePreDirection: string
    ThoroughfareLeadingType: string
    ThoroughfareName: string
    ThoroughfareTrailingType: string
    ThoroughfarePostDirection: string
    DependentThoroughfare: string
    DependentThoroughfarePreDirection: string
    DependentThoroughfareLeadingType: string
    DependentThoroughfareName: string
    DependentThoroughfareTrailingType: string
    DependentThoroughfarePostDirection: string
    Building: string
    PremisesType: string
    PremisesNumber: '711-2880'
    SubPremisesType: string
    SubPremisesNumber: string
    PostBox: string
    Latitude: string
    Longitude: string
    DeliveryIndicator: 'R' | 'B' | 'U'
    MelissaAddressKey: string
    MelissaAddressKeyBase: string
    PostOfficeLocation: string
    SubPremiseLevel: string
    SubPremiseLevelType: string
    SubPremiseLevelNumber: string
    SubBuilding: string
    SubBuildingType: string
    SubBuildingNumber: string
    UTC: string
    DST: string
    DeliveryPointSuffix: string
    CensusKey: string
    Extras: any
}

export type MelissaQueryVariables = {
    queryParams: string
}
export type MelissaResponse = {
    validateAddress: {
        Version: string
        TransmissionReference: string
        TransmissionResults: string
        TotalRecords: string
        Records: MelissaData[]
        isAddressAccuracy: boolean
        isValidAddress: boolean
    }
}

type UseAddresValidationReturn = {
    requestMelissaData: (
        input: AddressInputVariableInterface
    ) => Promise<{
        address: MelissaData
        isAddressAccuracy: boolean
        isValidAddress: boolean
    }>
    transformMelissaData: (
        melissaData: MelissaData,
        input: AddressInputVariableInterface,
        region: string
    ) => AddressInputVariableInterface
}

export const addressLine2Keywords = [
    'apartment',
    'apt',
    '#',
    'suite',
    'ste',
    'unit',
    'floor',
    'fl',
    'building',
    'bldg',
    'room',
    'rm',
    'lot'
]

export const addressLine2Examples = [
    'Apartment: Apartment 22, Apt 12, #12, ...',
    'Suite: Suite 202, Suite A, Ste B1, ...',
    'Unit: Unit 5B, Unit 301, ...',
    'Floor: Floor 10, Fl 3, ...',
    'Building: Building A, Bldg 2, ...',
    'Room: Room 101, Rm 12, ...',
    'Lot: Lot 5, Lot 12A, ...'
]

export const isUSAddressLine2MistakenInput = (
    region: string,
    ar2: string
): boolean => {
    if (!ar2) {
        return false
    }

    const [prefix, ...restPartOfAddress] = ar2.trim().split(' ')

    return (
        region === 'US' &&
        !addressLine2Keywords.some((kw) => kw === prefix.toLowerCase())
    )
}

const formatUrlParameter = (value: string) =>
    encodeURIComponent(value).replace(/\s/g, '+')

export const useAddressValidation = (): UseAddresValidationReturn => {
    /**
     * Melissa Rquest
     * @description request sent to Melissa API to get suggested addresses
     * @param input type AddressInputVariableInterface response from address update
     * @returns Melissa JSON address data
     */
    const { countries } = useCountries({
        revalidateOnFocus: false
    })
    const { region: currentRegion } = useBaseUrl(
        typeof window !== 'undefined' ? window.location.href : ''
    )
    const requestMelissaData = async (
        input: AddressInputVariableInterface
    ): Promise<{
        address: MelissaData
        isAddressAccuracy: boolean
        isValidAddress: boolean
    }> => {
        const {
            country_code: countryCode,
            postcode,
            street,
            city,
            region
        } = input

        let melissaParamsUrl = ``

        if (countryCode)
            melissaParamsUrl += `&ctry=${formatUrlParameter(countryCode)}`

        if (street && street?.[0])
            melissaParamsUrl += `&a1=${formatUrlParameter(street[0])}`
        if (street && street?.[1])
            melissaParamsUrl += `&a2=${formatUrlParameter(
                isUSAddressLine2MistakenInput(currentRegion, street[1])
                    ? `#${street[1]}`
                    : street[1]
            )}`
        if (city) melissaParamsUrl += `&loc=${formatUrlParameter(city)}`

        if (countries && region) {
            const state = countries
                ?.find(
                    (country) =>
                        country?.two_letter_abbreviation === countryCode
                )
                ?.available_regions?.find(
                    (regionData) => regionData?.id === Number(region)
                )?.name

            if (state)
                melissaParamsUrl += `&admarea=${formatUrlParameter(state)}`
        }
        if (postcode)
            melissaParamsUrl += `&postal=${formatUrlParameter(postcode)}`

        melissaParamsUrl += '&format=JSON'

        const { data } = await graphqlFetch<
            MelissaQueryVariables,
            MelissaResponse
        >({
            query: validateAddressQuery,
            variables: {
                queryParams: melissaParamsUrl
            },
            fetchOptions: {
                headers: {
                    'x-pylot-query': 'validateAddress'
                }
            }
        })
        const address = data?.validateAddress?.Records?.[0]
        const { isAddressAccuracy = false, isValidAddress = false } =
            data?.validateAddress || {}

        return { address, isAddressAccuracy, isValidAddress }
    }

    /**
     * Adds / Updates necessary Melissa data to update address
     * @param melissaData Melissa API response
     * @param input User input from address edit form
     * @returns updatedInput object
     */
    const transformMelissaData = (
        melissaData: MelissaData,
        input: AddressInputVariableInterface,
        region: string
    ) => {
        const updatedInput = { ...input }

        updatedInput.street[0] = melissaData.AddressLine1
        if (isUSAddressLine2MistakenInput(region, updatedInput?.street[1])) {
            updatedInput.street[1] = `#${updatedInput.street[1]}`
        }
        updatedInput.postcode = melissaData.PostalCode
        updatedInput.city = melissaData.Locality
        updatedInput.region = melissaData.AdministrativeArea
        updatedInput.isMelissaData = true

        return updatedInput
    }

    return {
        requestMelissaData,
        transformMelissaData
    }
}
