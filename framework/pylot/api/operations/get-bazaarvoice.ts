import { PylotFrontendConfig } from '@config/config'
import { getFirstNotEmpty } from '@corsairitshopify/pylot-utils'

const PATH =
    '/data/display/0.2alpha/product/summary?contentType=reviews,questions&reviewDistribution=primaryRating,recommended&rev=0&contentlocale=nl*,en*,fr*,de*,it*,no*,pl*,pt*,es*,sv*,en_U'

type ValueType = string | boolean | null | number | undefined

type BazaarvoiceSummaryType = {
    reviewCount?: number
    ratingValue?: number
    bestRatingValue?: ValueType
}

export const getBazaarvoiceSummary = async (
    config: PylotFrontendConfig,
    pimId?: number | undefined | null
): Promise<BazaarvoiceSummaryType> => {
    if (!pimId) return {}
    const BV_BASE_URL =
        config?.base?.bazaarvoice?.baseUrl || 'https://stg.api.bazaarvoice.com'
    const bazaarvoicePassKey = config?.base?.bazaarvoice?.passKey

    if (!bazaarvoicePassKey) return {}

    const res = await fetch(
        `${BV_BASE_URL}${PATH}&passKey=${bazaarvoicePassKey}&productid=${pimId}`
    )
    const bvSummary = await res.json()
    const reviewCount = bvSummary?.reviewSummary?.numReviews
    const ratingValue = bvSummary?.reviewSummary?.primaryRating?.average

    return {
        reviewCount,
        ratingValue: Math.round(ratingValue * 10) / 10,
        bestRatingValue: getFirstNotEmpty(
            [config.seo.product?.bestRating, Math.ceil(Number(ratingValue))],
            false
        )
    }
}
