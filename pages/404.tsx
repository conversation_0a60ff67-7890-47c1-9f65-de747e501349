import { NotFound } from '@components/404'
import { MainLayout } from '@components/layouts/MainLayout/MainLayout'
import { serverSideTranslations } from '@pylot-data/serverSideTranslations'
import type { GetStaticProps, GetStaticPropsContext } from 'next'

export const getStaticProps: GetStaticProps = async ({
    locale
}: GetStaticPropsContext) => {
    return {
        props: {
            ...(await serverSideTranslations(locale!, ['common', '404']))
        },
        revalidate: 14400
    }
}

export default function Custom404(): JSX.Element {
    return <NotFound />
}

Custom404.Layout = MainLayout
