/* eslint-disable prettier/prettier */
import { Maybe } from '@pylot-data/pylotschema'
import dayjs from 'dayjs'
import isoWeek from 'dayjs/plugin/isoWeek'

dayjs.extend(isoWeek)

type GetDeliverByParams = {
    dateFormatByRegion?: Maybe<Record<string, string>>
    deliverBy?: any
    excludedDeliveryDate?: any
    orderCreatedDate?: any
    sku?: any
    withYear?: boolean
}

const dateSeparatorRegex = /[/,.\-_|#\\]/

export const DEFAULT_FORMAT_DATE_BY_REGION = {
    'MM/DD/YYYY': ['US', 'CA'],
    'DD/MM/YYYY': [
        'UK',
        'FR',
        'IT',
        'ES',
        'PT',
        'EU',
        'CN',
        'WW',
        'JP',
        'KR',
        'LM',
        'RU',
        'TW'
    ],
    'DD.MM.YYYY': ['DE', 'PL']
}

//special estimated delivery date, only uses for displaying in PDP ATC
export const DELIVERYBY_DATE_FORMAT_BY_REGION = {
    'DD.MM.': 'DE,PL',
    'DD/MM': 'FR,IT,ES,PT',
    'MM/DD': 'US,CA',
    'YYYY-MM-DD': 'SE',
    'YYYY.MM.DD': 'KR',
    'YYYY年MM月DD日': 'CN,TW',
    'MM月DD日': 'JP',
    'DD.MM.YYYY': 'CH',
    'DD/MM/YYYY': 'UK,EU,CN,WW,LM,RU'
}

// const DEFAULT_FORMAT_DATE_BY_REGION_NO_YEAR = {
//     'MM/DD': ['US', 'CA'],
//     'DD/MM': [
//         'UK',
//         'FR',
//         'IT',
//         'ES',
//         'PT',
//         'EU',
//         'CN',
//         'WW',
//         'JP',
//         'KR',
//         'LM',
//         'RU',
//         'TW'
//     ],
//     'DD.MM': ['DE', 'PL']
// }

export const getFinalDateFormatByRegion = (
    dateFormatByRegion?: Maybe<Record<string, string>>,
    withYear?: boolean
): Record<string, string[]> => {
    if (dateFormatByRegion) {
        const finalDateFormatByRegion: Record<string, string[]> = {}
        for (const [format, region] of Object.entries(dateFormatByRegion)) {
            const dateSeparator = format.match(dateSeparatorRegex)?.[0] ?? '/'
            const finalFormat = withYear
                ? format
                      .split(dateSeparator)
                      .filter((digit) => digit !== 'YYYY')
                      .join(dateSeparator)
                : format

            finalDateFormatByRegion[finalFormat] = region.split(',')
        }
        return finalDateFormatByRegion
    }

    return DEFAULT_FORMAT_DATE_BY_REGION
}

export const getFormattedDate = (
    timeStamp: number,
    dateFormatByRegion: Record<string, string[]>,
    twoDigitYear?: boolean
): string => {
    const date = dayjs(timeStamp)
    const region =
        typeof window !== 'undefined'
            ? window.location.pathname.split('/')[1]
            : ''
    let formatDate = 'MM/DD/YYYY'
    if (region) {
        for (const [formatDateByRegion, regions] of Object.entries(
            dateFormatByRegion
        )) {
            if (regions.includes(region?.toLocaleUpperCase())) {
                formatDate = formatDateByRegion
            }
        }
    }
    if (twoDigitYear) {
        formatDate = formatDate.replace('YYYY', 'YY')
    }
    return date.format(formatDate)
}

export const calculateDeliveryDate = (
    dateFormatByRegion: Record<string, string[]>,
    deliverBy: any,
    excludeDeliverDates: string[],
    orderCreatedDate?: string
): string => {
    if (!deliverBy || isNaN(deliverBy)) return ''
    let businessDayShipping = orderCreatedDate
        ? dayjs(orderCreatedDate).startOf('day')
        : dayjs().startOf('day')
    const now = orderCreatedDate ? dayjs(orderCreatedDate) : dayjs()
    const isAfternoon = now.hour() >= 13

    if (!isAfternoon) {
        businessDayShipping = businessDayShipping.subtract(1, 'days')
    }

    const excludeDates = excludeDeliverDates.map((date) => {
        if (!isNaN(+date)) return dayjs(+date)
        return dayjs(date)
    })

    while (deliverBy > 0) {
        businessDayShipping = businessDayShipping.add(1, 'days')
        if (
            businessDayShipping.isoWeekday() < 6 &&
            !excludeDates.some((date) =>
                date.isSame(businessDayShipping, 'day')
            )
        ) {
            deliverBy--
        }
    }

    return getFormattedDate(
        businessDayShipping.valueOf(),
        dateFormatByRegion,
        false
    )
}

export const getDeliveryByDate = ({
    dateFormatByRegion,
    deliverBy,
    excludedDeliveryDate,
    sku,
    orderCreatedDate,
    withYear
}: GetDeliverByParams): string | null => {
    if (
        !deliverBy ||
        sku?.toLocaleLowerCase()?.startsWith('k70_') ||
        sku?.toLocaleLowerCase()?.startsWith('p6_') ||
        typeof deliverBy !== 'string'
    )
        return null
    let deliverByDate: string | null = null

    const deliverByFrom = parseInt(deliverBy?.split('-')?.[0] || '0')
    const deliverByTo = parseInt(deliverBy?.split('-')?.[1] || '0')

    if (deliverByFrom && deliverByTo) {
        deliverByDate = `${calculateDeliveryDate(
            getFinalDateFormatByRegion(dateFormatByRegion, withYear),
            deliverByFrom,
            excludedDeliveryDate || [],
            orderCreatedDate
        )}-${calculateDeliveryDate(
            getFinalDateFormatByRegion(dateFormatByRegion, withYear),
            deliverByTo,
            excludedDeliveryDate || [],
            orderCreatedDate
        )}`
    }
    return deliverByDate
}

// this is a copy of "calculateDeliveryDateLocal" function, but it uses another format.
// We cannot fix the "calculateDeliveryDateLocal" function because it is actually referenced by modules from node_modules.
// We use this function until the "calculateDeliveryDateLocal" function is fixed.
export const calculateDeliveryDateLocal = (
    dateFormatByRegion: Record<string, string[]>,
    deliverBy: any,
    excludeDeliverDates: string[],
    orderCreatedDate?: string
): string => {
    if (!deliverBy || isNaN(deliverBy)) return ''
    let businessDayShipping = orderCreatedDate
        ? dayjs(orderCreatedDate).startOf('day')
        : dayjs().startOf('day')
    const now = orderCreatedDate ? dayjs(orderCreatedDate) : dayjs()
    const isAfternoon = now.hour() >= 13

    if (!isAfternoon) {
        businessDayShipping = businessDayShipping.subtract(1, 'days')
    }

    const excludeDates = excludeDeliverDates.map((date) => {
        if (!isNaN(+date)) return dayjs(+date)
        return dayjs(date)
    })

    while (deliverBy > 0) {
        businessDayShipping = businessDayShipping.add(1, 'days')
        if (
            businessDayShipping.isoWeekday() < 6 &&
            !excludeDates.some((date) =>
                date.isSame(businessDayShipping, 'day')
            )
        ) {
            deliverBy--
        }
    }

    return getFormattedDate(
        businessDayShipping.valueOf(),
        dateFormatByRegion,
        false
    )
}

// this is a copy of "getDeliveryByDate" function, but it uses another format.
// We cannot fix the "getDeliveryByDate" function because it is actually referenced by modules from node_modules.
// We use this function until the "getDeliveryByDate" function is fixed.
export const getDeliveryByDateLocal = ({
    dateFormatByRegion,
    deliverBy,
    excludedDeliveryDate,
    sku,
    orderCreatedDate,
    withYear
}: GetDeliverByParams): string | null => {
    if (
        !deliverBy ||
        sku?.toLocaleLowerCase()?.startsWith('k70_') ||
        sku?.toLocaleLowerCase()?.startsWith('p6_') ||
        typeof deliverBy !== 'string'
    )
        return null
    let deliverByDate: string | null = null

    const deliverByFrom = parseInt(deliverBy?.split('-')?.[0] || '0')
    const deliverByTo = parseInt(deliverBy?.split('-')?.[1] || '0')

    if (deliverByFrom && deliverByTo) {
        deliverByDate = `${calculateDeliveryDateLocal(
            getFinalDateFormatByRegion(dateFormatByRegion, withYear),
            deliverByFrom,
            excludedDeliveryDate || [],
            orderCreatedDate
        )}-${calculateDeliveryDateLocal(
            getFinalDateFormatByRegion(dateFormatByRegion, withYear),
            deliverByTo,
            excludedDeliveryDate || [],
            orderCreatedDate
        )}`
    }
    return deliverByDate
}
