export interface ShortIOreturn {
    originalURL: string
    DomainId: number
    archived: boolean
    lcpath: string
    expiredURL: string
    source: string
    cloaking: boolean
    createdAt: Date
    updatedAt: Date
    OwnerId: number
    tags: Array<any>
    path: string
    idString: string
    shortURL: string
    secureShortURL: string
    duplicate: boolean
}
import type { PylotFrontendConfig } from '@config/config'

function randomIntFromInterval(min: number, max: number) {
    // min and max included
    return Math.random() * (max - min + 1) + min
}

export const shortUrl = async (
    originalURL: string,
    retryCount: number,
    maxRetry: number
): Promise<string> => {
    try {
        const region =
            window.location.pathname.split('/')?.[1].toUpperCase() || 'US'
        const { config } = (await import(`@config/stores/${region}`)) as {
            config: PylotFrontendConfig
        }
        const { token, domain } = config?.base?.shortIo || {}

        if (!token || !domain) return ''

        const response = await fetch('https://api.short.io/links/public', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: token
            },
            body: JSON.stringify({
                domain,
                originalURL,
                expiredURL: window?.location.origin,
                expiresAt: getFutureTimestamp(90),
                ttl: getFutureTimestamp(120)
            })
        })
        if (response) {
            //case not retry
            if (response.ok) {
                const { shortURL } = (await response.json()) as ShortIOreturn
                return shortURL || ''
            } else {
                if (retryCount > maxRetry) return ''
                await waitFor(randomIntFromInterval(1, 4))
                return await shortUrl(originalURL, ++retryCount, maxRetry)
            }
        }
        return ''
    } catch (error) {
        return ''
    }
}

export const getFutureTimestamp = (day = 1) => {
    const currentDate = new Date()

    const futureDate = new Date(
        currentDate.getTime() + day * 24 * 60 * 60 * 1000
    )

    // Convert the future date to a timestamp (in milliseconds since Unix epoch)
    const futureTimestamp = futureDate.getTime()
    return futureTimestamp
}

const waitFor = (second = 1) => {
    return new Promise((resolve) => setTimeout(resolve, second * 1000))
}
