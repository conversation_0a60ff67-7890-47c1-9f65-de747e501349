import {
    CartItemInput,
    CartItemInterface,
    ConfigurableCartItem,
    ConfigurableProduct,
    ConfigurableVariant,
    Maybe,
    ProductInterface
} from '@pylot-data/pylotschema'
import { processItemPrice } from './processItemPrice'

export const isConfigItem = (
    item: CartItemInterface
): item is ConfigurableCartItem => {
    return 'variants' in item.product
}

const isConfigProduct = (
    product: ProductInterface
): product is ConfigurableProduct => {
    return 'variants' in product
}

export const processConfigurableItem = (
    input: CartItemInput,
    item: CartItemInterface,
    product: ProductInterface
): void => {
    if (!isConfigItem(item) || !isConfigProduct(product)) {
        return
    }

    const variant = product.variants?.find(
        (variantItem: Maybe<ConfigurableVariant>) => {
            if (!item || !Array.isArray(variantItem?.attributes)) {
                return false
            }
            const matchingAttributes = variantItem?.attributes.filter(
                (att) => att && input.selected_options?.includes(att.uid)
            )
            return input.selected_options?.length === matchingAttributes?.length
        }
    )
    if (!variant || !variant.attributes) {
        return
    }
    item['configurable_options'] = []
    variant.attributes.forEach((attribute) => {
        // The values here don't have to match the backend data precisely,
        // just having unique values as UID should do, and IDs can be ignored, since they are deprecated
        item['configurable_options'].push({
            configurable_product_option_uid: String(attribute?.value_index),
            configurable_product_option_value_uid: String(attribute?.uid),
            option_label: String(attribute?.code),
            value_label: String(attribute?.label),
            id: 0, // TODO: delete, once this field is removed from the schema
            value_id: 0 // TODO: delete, once this field is removed from the schema
        })
    })
    if (variant.product?.price_range) {
        processItemPrice(item, variant.product?.price_range.minimum_price)
    }
    item['__typename'] = 'ConfigurableCartItem'
}
