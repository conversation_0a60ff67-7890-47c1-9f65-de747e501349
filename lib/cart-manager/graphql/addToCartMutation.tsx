import { cartFields } from '@pylot-data/core-queries/cart'

export const addToCartMutation = /* GraphQL */ `
    mutation addToCart($cartId: String!, $cartItems: [CartItemInput!]!, $isSignedIn: Boolean = false) {
        addProductsToCart(cartId: $cartId, cartItems: $cartItems){
            cart {
                ${cartFields}
            }
            user_errors {
                code
                message
            }
        }
    }
`
