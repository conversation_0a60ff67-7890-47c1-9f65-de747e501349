import { useResizeObserver } from '@lib/hooks/useResizeObserver'
import debounce from 'lodash.debounce'
import { RefObject, useCallback, useEffect, useRef, useState } from 'react'

export const useJumpToId = (
    ref: RefObject<HTMLElement>,
    callback: () => void,
    id?: string
): void => {
    const bodyRef = useRef(
        typeof document !== 'undefined' && document.body ? document.body : null
    )
    const [executed, setIsExecuted] = useState(false)

    const afterExecuting = debounce(() => {
        setIsExecuted(true)
    }, 3000)

    const scrollIntoView = useCallback(() => {
        if (
            id &&
            window?.location.href.includes(`#${id}`) &&
            ref.current &&
            !executed
        ) {
            ref?.current?.scrollIntoView?.({
                behavior: 'smooth'
            })
            // prevent the page from being scrolled to the top
            history.pushState(null, '', `#${id}`)
            callback()
            afterExecuting()
        }
    }, [id, ref, executed, callback, afterExecuting])

    useEffect(() => {
        scrollIntoView()
    }, [scrollIntoView])

    useResizeObserver({
        ref: bodyRef,
        onResize: scrollIntoView
    })
}
