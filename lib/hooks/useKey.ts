import { useEffect, useCallback } from 'react'

type KeyCombo = {
    key: string
    ctrl?: boolean
    alt?: boolean
    shift?: boolean
}

type KeyHandler = (event: KeyboardEvent) => void

export const useKey = (
    keyCombo: KeyCombo | string,
    handler: KeyHandler
): void => {
    const handleKeyPress = useCallback(
        (event: KeyboardEvent) => {
            // If keyCombo is just a string, only check the key
            if (typeof keyCombo === 'string') {
                if (event.key.toLowerCase() === keyCombo.toLowerCase()) {
                    handler(event)
                }
                return
            }

            // Check if the pressed key matches the combo
            const keyMatches =
                event.key.toLowerCase() === keyCombo.key.toLowerCase()
            const ctrlMatches = keyCombo.ctrl ? event.ctrlKey : !event.ctrlKey
            const altMatches = keyCombo.alt ? event.altKey : !event.altKey
            const shiftMatches = keyCombo.shift
                ? event.shiftKey
                : !event.shiftKey

            if (keyMatches && ctrlMatches && altMatches && shiftMatches) {
                handler(event)
            }
        },
        [keyCombo, handler]
    )

    useEffect(() => {
        window.addEventListener('keydown', handleKeyPress)
        return () => {
            window.removeEventListener('keydown', handleKeyPress)
        }
    }, [handleKeyPress])
}
