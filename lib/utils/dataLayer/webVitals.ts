import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const sendWebVitalsCallback = ({
    name,
    delta,
    value,
    id,
    attribution
}: any): void => {
    const eventPayload = {
        event_category: 'Web Vitals',
        value: delta,
        event_label: id,
        event_value: value,
        debug_target: ''
    }
    switch (name) {
        case 'CLS':
            eventPayload.debug_target = attribution?.largestShiftTarget
            break
        case 'INP':
            eventPayload.debug_target = attribution?.interactionTarget
            break
        case 'LCP':
            eventPayload.debug_target = attribution?.element
            break
    }
    pushToDataLayer({ event: name, web_vitals: eventPayload })
}

export const sendWebVitalsToGoogleAnalytics = (): void => {
    import('web-vitals/attribution').then(
        ({ onCLS, onINP, onLCP, onFCP, onTTFB }) => {
            onCLS(sendWebVitalsCallback)
            onINP(sendWebVitalsCallback)
            onLCP(sendWebVitalsCallback)
            onFCP(sendWebVitalsCallback)
            onTTFB(sendWebVitalsCallback)
        }
    )
}
