// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const getDownloadSoftwareEvent = (
    userAction: 'Signup Newsletter' | 'Skip Newsletter Signup' | 'Close Modal',
    downloadUrl: string,
    softwareTitle?: string,
    softwareVersion?: string,
    softwareSystem?: string
) => {
    return {
        event: 'downloadSoftware',
        downloadSoftware: {
            userAction: userAction,
            downloadUrl: downloadUrl,
            softwareTitle: softwareTitle,
            softwareVersion: softwareVersion,
            softwareSystem: softwareSystem
        }
    }
}
