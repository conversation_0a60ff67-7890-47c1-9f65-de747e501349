import Cookies from 'js-cookie'

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
export const getLocation = () => {
    const siteCountry = window?.location?.pathname.substring(1, 3)
    const siteLanguage = window?.location?.pathname.substring(4, 6)
    const getUserLanguage = window.navigator.language
    const userLanguage =
        getUserLanguage && getUserLanguage.includes('-')
            ? getUserLanguage.split('-')[0]
            : getUserLanguage
    return {
        siteLanguage: siteLanguage,
        siteCountry: siteCountry,
        userLanguage: userLanguage,
        userCountry: Cookies.get('Country_Code')?.toLowerCase()
    }
}
