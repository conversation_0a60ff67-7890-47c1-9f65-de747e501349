.wave-dx-animation {
    @apply relative;

    &__content {
        @apply relative z-1;
        padding-top: var(--spacing--lg);
        padding-bottom: var(--spacing--lg);

        @screen md {
            padding-top: 150px;
            padding-bottom: 150px;
        }
    }

    &__features {
        @apply grid gap-y-32px;
        margin-top: 80px;
        @apply grid-cols-2;
        column-gap: 16px;

        @screen md {
            column-gap: 32px;
        }

        @screen lg {
            @apply grid-cols-4;
            column-gap: 80px;
        }
    }

    &__feature-item {
        @apply flex flex-col gap-4px items-center text-center;
    }

    .wave-dx-animation__video {
        @apply absolute z-0 inset-0;
        @apply bg-black;
        @apply h-full w-full;

        video {
            @apply h-full w-full object-cover
        }
    }
}
