import { LinkResponse } from '@components/molecules/Link/Link'
import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { FlippableGalleryCard } from '@components/organisms/FlippableGalleryCard/FlippableGalleryCard'
import { GalleryCardProps } from '@components/organisms/GalleryCard/GalleryCard'
import { VideoOverlayProps } from '@components/organisms/VideoOverlay/VideoOverlay'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import { ProductInterface } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { useRouter } from 'next/router'
import { FC, useEffect, useRef, useState } from 'react'
import s from './FlippableGallery.module.scss'

export type FlippableGalleryProps = {
    title?: string
    textPanel?: PrimaryTextProps
    items: GalleryCardProps[]
    backgroundColor?: SectionBgColor
    id?: string
    noPaddingBottom?: boolean
    fullScreen?: boolean
    additionalOptions?: Record<string, unknown>
}

export type GalleryItemWithProduct = GalleryCardProps & {
    product?: null | ProductInterface
}

export const FlippableGallery: FC<FlippableGalleryProps> = ({
    textPanel,
    items,
    id,
    backgroundColor = SectionBgColor.BG_GREY,
    noPaddingBottom,
    fullScreen,
    additionalOptions
}) => {
    const { locale } = useRouter()

    const textColor = SectionThemeDarkBgColors.includes(backgroundColor)
        ? 'light'
        : 'dark'

    const [flippedIndexes, setFlippedIndexes] = useState<number[]>([])
    const [itemsWithProduct, setItemsWithProduct] = useState<
        GalleryItemWithProduct[]
    >(items)
    const fetched = useRef(false)
    useEffect(() => {
        if (items.length > 0 && !fetched.current) {
            const skus = items
                .map((item) => item.sku ?? '')
                .filter((sku) => sku != '')
            if (skus.length) {
                getProductsBySkus(skus, locale ?? '').then((products) => {
                    fetched.current = true
                    const cardsWithProduct: GalleryItemWithProduct[] = []
                    if (products && products.length) {
                        items?.forEach((card) => {
                            const product = products.find(
                                (product) => product.sku === card.sku
                            ) as ProductInterface
                            cardsWithProduct.push({
                                ...card,
                                product: product
                            })
                        })
                        setItemsWithProduct(cardsWithProduct)
                    }
                })
            }
        }
    }, [items, locale])

    const onCardClick = (index: number) => {
        const foundIndex = flippedIndexes[0] === index
        if (foundIndex) {
            setFlippedIndexes([])
        } else {
            if (flippedIndexes.length) {
                setTimeout(() => {
                    setFlippedIndexes([index])
                }, 500)
            }
            setFlippedIndexes((prevFlippedIndex) => [
                index,
                ...prevFlippedIndex
            ])
        }
    }
    return (
        <div className={backgroundColor} id={id}>
            {textPanel && (
                <div className="pt-16 md:pt-32">
                    <Container size={ContainerSize.MEDIUM}>
                        <PrimaryText
                            calloutTitle={textPanel.calloutTitle}
                            headline={textPanel.headline}
                            bodyCopy={textPanel.bodyCopy}
                            textColor={textColor}
                            textAlignment={HorizontalAlignmentEnum.CENTER}
                        />
                    </Container>
                </div>
            )}
            {itemsWithProduct && itemsWithProduct.length > 0 && (
                <div
                    className={cn(
                        s['flippable-gallery'],
                        'mx-auto py-16 px-16px gap-16px flex-col md:grid md:py-32 md-max:flex',
                        {
                            [s['reduced']]: noPaddingBottom,
                            [s['flippable-gallery--fullscreen']]: fullScreen
                        }
                    )}
                >
                    {itemsWithProduct.map((item, i) => (
                        <FlippableGalleryCard
                            key={`${item.headline}${i}`}
                            textPanel={item.textPanel}
                            cloudinaryMedia={item.cloudinaryMedia}
                            cloudinaryMobileMedia={item.cloudinaryMobileMedia}
                            animationImage={item.animationImage}
                            // posterImage={item.posterImage}
                            cloudinaryPosterImage={item.cloudinaryPosterImage}
                            // posterImageMobile={item.posterImageMobile}
                            cloudinaryPosterImageMobile={
                                item.cloudinaryPosterImageMobile
                            }
                            id={item.id}
                            size={item.size}
                            textColor={item.textColor}
                            mobileTextColor={
                                item.mobileTextColor
                                    ? item.mobileTextColor
                                    : textColor
                            }
                            backgroundColor={item.backgroundColor}
                            textVerticalAlignment={item.textVerticalAlignment}
                            first={i === 0}
                            flipped={flippedIndexes.some(
                                (index) => index === i
                            )}
                            flipCard={() => onCardClick(i)}
                            product={item.product}
                            videoOverlay={
                                item.children &&
                                item.children[0] &&
                                item.children[0].meta?.contentType ===
                                    'organismVideoOverlay'
                                    ? {
                                          ...(item
                                              .children[0] as VideoOverlayProps),
                                          mediaEmbedded: item.mediaEmbedded
                                      }
                                    : undefined
                            }
                            url={item.link}
                            link={
                                item.children &&
                                item.children[0] &&
                                item.children[0].meta?.contentType ===
                                    'moleculeLink'
                                    ? (item.children[0] as LinkResponse)
                                    : undefined
                            }
                            customOptions={item.customOptions}
                            onlyPlayOnHover={
                                additionalOptions?.onlyPlayOnHover as boolean
                            }
                        />
                    ))}
                </div>
            )}
        </div>
    )
}

export default FlippableGallery
