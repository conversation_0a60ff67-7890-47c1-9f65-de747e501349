.flippable-gallery {
    grid-template-columns: repeat(8, 1fr);
    max-width: 100vw;

    &.reduced {
        padding-bottom: 16px;
    }

    @screen md {
        padding-left: var(--container-padding--md);
        padding-right: var(--container-padding--md);
        max-width: 1190px + 32px + 32px;
    }

    @screen lg {
        max-width: 1190px + 64px + 64px;
        padding-left: var(--container-padding--lg);
        padding-right: var(--container-padding--lg);
    }

    &--fullscreen {
        max-width: 100vw;

        @screen md {
            padding-left: 64px;
            padding-right: 64px;
        }
    }
}
