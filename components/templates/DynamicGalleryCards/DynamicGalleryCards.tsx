import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { DynamicGalleryCard } from '@components/organisms/DynamicGalleryCard/DynamicGalleryCard'
import { GalleryCardSize } from '@components/organisms/GalleryCard/GalleryCard'
import { VideoOverlayProps } from '@components/organisms/VideoOverlay/VideoOverlay'
import { CardProps } from '@components/templates/CardList/CardList'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import { FC, useMemo } from 'react'
import s from './DynamicGalleryCards.module.scss'

export type DynamicGalleryCardsProps = {
    title?: string
    textPanel?: PrimaryTextProps
    cards: CardProps[]
    backgroundColor?: SectionBgColor
    id?: string
}

export const DynamicGalleryCards: FC<DynamicGalleryCardsProps> = ({
    textPanel,
    cards,
    backgroundColor = SectionBgColor.TRANSPARENT,
    id
}) => {
    let textColor = textPanel?.textColor
    if (!textColor) {
        textColor = SectionThemeDarkBgColors.includes(backgroundColor)
            ? 'light'
            : 'dark'
    }

    const leftGallery = useMemo(() => {
        return cards.filter(
            (card, i) =>
                (card.cloudinaryMedia?.[0] || card.textPanel) && i % 2 === 0
        )
    }, [cards])
    const rightGallery = useMemo(() => {
        return cards.filter(
            (card, i) =>
                (card.cloudinaryMedia?.[0] || card.textPanel) && i % 2 === 1
        )
    }, [cards])

    const galleries = useMemo(() => {
        return [leftGallery, rightGallery]
    }, [leftGallery, rightGallery])
    return (
        <div className={backgroundColor} id={id}>
            {textPanel && (
                <div className="pt-16 md:pt-32">
                    <Container size={ContainerSize.SMALL}>
                        <PrimaryText
                            headline={textPanel.headline}
                            bodyCopy={textPanel.bodyCopy}
                            textAlignment={HorizontalAlignmentEnum.CENTER}
                            textColor={textColor}
                        />
                    </Container>
                </div>
            )}
            {cards && cards.length > 0 && (
                <Container size={ContainerSize.LARGE}>
                    <div className={s['dynamic-gallery-cards']}>
                        {galleries &&
                            galleries.length > 0 &&
                            galleries.map((gallery, i) => (
                                <div
                                    key={`dynamic-gallery-card-gallery-${i}`}
                                    className={
                                        s['dynamic-gallery-cards__gallery']
                                    }
                                >
                                    {gallery.map((card, i) => (
                                        <DynamicGalleryCard
                                            className={
                                                s['dynamic-gallery-cards__card']
                                            }
                                            key={`dynamic-gallery-card-${card.textPanel?.headline}${i}`}
                                            text={card.textPanel?.bodyCopy}
                                            headline={card.textPanel?.headline}
                                            calloutTitle={
                                                card.textPanel?.calloutTitle
                                            }
                                            link={card.textPanel?.link}
                                            cloudinaryMedia={
                                                card.cloudinaryMedia
                                            }
                                            cloudinaryMobileMedia={
                                                card.cloudinaryMobileMedia
                                            }
                                            size={
                                                card.size
                                                    ? (card.size as GalleryCardSize)
                                                    : GalleryCardSize.FOUR_COLS
                                            }
                                            textColor={textColor}
                                            videoOverlay={
                                                card.children &&
                                                card.children[0]
                                                    ? {
                                                          ...(card
                                                              .children[0] as VideoOverlayProps),
                                                          mediaEmbedded:
                                                              card.mediaEmbedded
                                                      }
                                                    : undefined
                                            }
                                            cloudinaryPosterImage={
                                                card.cloudinaryPosterImage
                                            }
                                            videoDescription={
                                                card?.customOptions
                                                    ?.videoDescription
                                            }
                                        />
                                    ))}
                                </div>
                            ))}
                    </div>
                </Container>
            )}
        </div>
    )
}

export default DynamicGalleryCards
