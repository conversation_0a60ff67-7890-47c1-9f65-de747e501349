.hardware-hotspot-content {
    transition: opacity 0.3s ease-out 0s;

    @screen md-max {
        @apply -translate-y-32 top-32;
    }

    &__text, &__toggle {
        & + .hardware-hotspot-content__icon {
            @apply mt-24px md:mt-4;
        }

        & + .hardware-hotspot-content__popup {
            @apply mt-24px md:mt-16;
        }

        & + .hardware-hotspot-content__link {
            @apply mt-24px md:mt-16;
        }
    }

    &__popup {
        & + .hardware-hotspot-content__icon {
            @apply mt-24px md:mt-4;
        }

        @screen md-max {
            min-height: 100%;
        }
    }

    & &__callout-popup {
        @screen md {
            max-width: 100%;
            height: fit-content;
            top: 50%;
            transform: translateY(-50%);
        }
        @screen md-max {
            min-height: 100%;
            height: calc(100vh - var(--sticky-nav-height--sm));
        }
    }

    &__link {
        @apply md-max:mx-auto md:mr-auto;
    }

    * + &__media {
        @apply mt-24px;

        video, img {
            border-radius: 12px;
        }
    }

    &__video {
        opacity: 0;
        will-change: opacity;

        &--active {
            opacity: 1;
        }
    }

    &__icon {
        @screen md-max {
            @apply mx-auto;
        }
    }

    &__lottie {
        width: 72px;
        height: 72px;
        margin-top: 8px !important;
        @screen md {
            margin-left: 0 !important;
            margin-top: 16px !important;
        }
        &__big{
            width: 100%;
            height: 100%;
        }
    }

    &__inner {
        transition: transform 0.5s ease-out 0s;
    }

    &--active {
        @apply opacity-100 pointer-events-auto;
        z-index: 1;

        .hardware-hotspot-content__inner {
            @apply transform translate-y-0;

        }
    }

    ul {
        text-align: left;
    }

    .hardware-hotspot-content__toggle-text {
        @apply flex flex-col;

        @screen md-max {
            margin-top: 12px;
            margin-left: auto;
            margin-right: auto;
            transition: height 0.3s ease-out;
            overflow: hidden;
        }

        @screen md {
            height: auto !important;
        }

        i {
            @apply font-univers55Oblique;
            @apply text-small-copy-md-max;
            @apply leading-tight;
            @screen md {
                @apply text-small-copy;
                @apply leading-extra-tight-md-max;
            }
        }
        &--inactive {
            @screen md-max {
                height: 0
            }
        }
    }

    .autofocus-icon {
        display: none;
        @screen md-max {
            margin-left: auto;
            margin-right: auto;
            display: block;
            outline: none !important;
        }
        &--active {
            rotate: 180deg;
        }
    }

    .divider {
        &--active {
            @apply mt-2.5 border-solid border border-light-grey-2;

            @screen md {
                @apply hidden
            }
        }
    }
}
