import React, { FC, useMemo, useState } from 'react'
import { Button } from '../../molecules/Button/Button'
import { FormInput } from '../../molecules/FormInput/FormInput'
import s from './SignUpForm.module.scss'
import { useTranslation } from 'next-i18next'
import { nanoid } from 'nanoid'
import cn from 'classnames'

export interface SignUpFormProps {
    email: {
        placeholder: string
        name: string
        errorText: string
    }
    submitLabel: string
    formAction?: string
    onSubmit?: (form: FormData) => void
    id?: string
    backgroundColor?: string
}

export const SignUpForm: FC<SignUpFormProps> = ({
    email,
    submitLabel,
    formAction = '',
    onSubmit,
    id,
    backgroundColor = 'bg-white-smoke'
}) => {
    // eslint-disable-next-line i18next/no-literal-string
    const { t, i18n } = useTranslation(['common'])
    const isUSA = /-US$/.test(i18n.language)
    const [emailValue, setEmailValue] = useState('')
    const [isSubmitted, setIsSubmitted] = useState(false)
    const stringNotEmpty = (value: string | null | undefined): boolean => {
        if (!value || value.trim() === '') {
            return false
        }
        return true
    }
    const pristineErrorTrigger = (name: string) => {
        document.getElementsByName(name).forEach((el) =>
            // eslint-disable-next-line i18next/no-literal-string
            el.dispatchEvent(new FocusEvent('focusout'))
        )
    }
    const inputValidation: {
        [key: string]: {
            fn: (val: string | undefined) => boolean
            onErr: (name: string) => void
        }
    } = {
        [email.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        }
    }

    const formId = useMemo(() => nanoid(), [])

    const _onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        const currentData = new FormData(event.currentTarget)
        let valid = true
        Object.entries(inputValidation).forEach(([key, validation]) => {
            const inputValid = validation.fn(currentData.get(key)?.toString())
            valid = valid && inputValid
            if (!inputValid) {
                validation.onErr(key)
            }
        })
        if (!valid) {
            event.preventDefault()
        } else if (onSubmit) {
            event.preventDefault()
            onSubmit(currentData)
            setIsSubmitted(true)
        }
    }

    /*eslint i18next/no-literal-string: ["error", { "ignoreAttribute": ["autoComplete", "variant"] }]*/
    return (
        <div
            className={cn(
                `${backgroundColor}`,
                'flex justify-center pb-16 md:pb-32 px-16px'
            )}
            id={id}
        >
            <div>
                <form
                    className="flex w-full gap-x-8px md:gap-x-16px max-w-7xl items-start"
                    onSubmit={_onSubmit}
                    action={formAction}
                >
                    <FormInput
                        name={email.name}
                        className="w-full"
                        autoComplete="email"
                        value={emailValue}
                        type="email"
                        placeholder={email.placeholder}
                        valid={inputValidation[email.name].fn(emailValue)}
                        onInput={(ev) => setEmailValue(ev.target.value)}
                        errorText={email.errorText}
                        /* eslint-disable-next-line i18next/no-literal-string */
                        pattern="[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,99}$"
                        id={`signup-email-${id ? id : formId}`}
                    />
                    <Button
                        type="submit"
                        className={`flex-shrink-0 md-max:mt-px ${
                            isSubmitted ? s['signUpForm--btn-disabled'] : ''
                        }`}
                        disabled={isSubmitted}
                        label={submitLabel}
                    >
                        {submitLabel}
                    </Button>
                </form>
                {isUSA && isSubmitted && (
                    <b>
                        {t(
                            "forms|⚠️ You're officially subscribed to our newsletter."
                        )}
                    </b>
                )}
                {!isUSA && isSubmitted && (
                    <b>
                        {t(
                            'forms|⚠️ Remember to check your inbox to confirm your subscription.'
                        )}
                    </b>
                )}
            </div>
        </div>
    )
}

export default SignUpForm
