
@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-150px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(150px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes fadeInOuter {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.audio-hero {
    &__scroll-down {
        position: absolute;
        left: 50%;
        bottom: 50px;
        transform: translate(-50%, 0);
        color: var(--white);
        z-index: 20;
    }
    gap: 167px;
    display: flex;
    justify-content: center;
    height: calc(100vh - 88px);
    @screen md-max {
        height: 100%;
    }
    &__items {
        position: relative;
        display: flex;
        align-items: center;
        flex-direction: row;
        width: 100%;
        max-width: 100vw;
        @screen md-max {
            flex-direction: column;
        }
    }

    &__header {
        max-width: 586px;
        width: 100%;
        position: absolute;
        top: 37%;
        left: 4%;
        z-index: 2;
        opacity: 0;
        animation: fadeInOuter 1.2s ease-in-out;
        animation-fill-mode: forwards;
        animation-delay: 1.5s;
        @apply px-4;
        h1 {
            top: 20%;
            opacity: 0;
            animation: fadeOut 1.2s linear;
            animation-fill-mode: forwards;
        }

        @screen lg {
            left: 8%;
        }
        @screen md-max {
            top: 10%;
            left: 0;
            text-align: center;
        }
    }

    &__active {
        &.audio-hero__header {
            h1 {
                animation: fadeIn 2.3s linear;
                animation-fill-mode: forwards;
                opacity: 1;
            }
        }
    }

    &__wrapper {
        @screen md {
            max-width: 100%;
            max-height: 100vh;
            height: 100%;
            width: 100%;
        }
        &__image {
            height: 100%;
            img {
                @screen md {
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                }
            }
        }
    }

}
