// AudioHero.js

import s from './AudioHero.module.scss'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import ChevronDownIcon from '@components/atoms/Icon/general/ChevronDownIcon'

export interface AudioHeroProps {
    views?: Array<{
        title: string
        images: Array<ImageType>
        textPanels: any[]
        animation: string
        mobileImage: Array<ImageType>
        meta: {
            contentType: string
        }
    }>
    scrollDownArrow?: boolean
}

export interface AudioHeroContent {
    content: AudioHeroProps
}

const AudioHero: FC<AudioHeroContent> = ({ content }) => {
    const { views, scrollDownArrow = false } = content
    const [currentViewIndex, setCurrentViewIndex] = useState(0)
    const heroRef = useRef<HTMLDivElement>(null)
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentViewIndex(
                (prevIndex) =>
                    (prevIndex + 1) %
                    (typeof views !== 'undefined' ? views.length : 1)
            )
        }, 4000)

        return () => clearInterval(timer)
    }, [views])
    const scrollDown = useCallback(() => {
        if (heroRef.current) {
            window.scrollTo({
                top:
                    heroRef.current.getBoundingClientRect().top +
                    heroRef.current.getBoundingClientRect().height +
                    window.pageYOffset,
                behavior: 'auto'
            })
        }
    }, [heroRef])
    if (!views || views.length === 0) {
        return null
    }
    const currentView = views[currentViewIndex]

    return (
        <div className={cn(s['audio-hero'], 'relative')} ref={heroRef}>
            {!scrollDownArrow && (
                <button
                    className={s['audio-hero__scroll-down']}
                    onClick={scrollDown}
                    onKeyPress={scrollDown}
                >
                    <ChevronDownIcon className="w-32px h-32px" />
                </button>
            )}
            <div
                className={cn(
                    'flex flex-row items-center',
                    s['audio-hero__items']
                )}
            >
                {!!views &&
                    views.map((currentView, viewIndex) => (
                        <div
                            key={viewIndex}
                            className={cn(s['audio-hero__header'], {
                                [s['audio-hero__active']]:
                                    viewIndex === currentViewIndex
                            })}
                        >
                            {currentView.textPanels?.map((text, index) => (
                                <div key={index}>
                                    <h1 className={cn('text-white')}>
                                        {text.headline}
                                    </h1>
                                    <h1
                                        className={`text${text.headlineClasses}`}
                                    >
                                        {text.subheader}
                                    </h1>
                                </div>
                            ))}
                        </div>
                    ))}
                <div className={cn(s['audio-hero__wrapper'])}>
                    {currentView.images.map((image, index) => (
                        <div
                            key={index}
                            className={cn(
                                s['audio-hero__wrapper__image'],
                                'hidden md:block'
                            )}
                        >
                            <img src={image.file.url} alt={image.title} />
                        </div>
                    ))}
                    {currentView.mobileImage.map((image, index) => (
                        <div
                            key={index}
                            className={cn(
                                s['audio-hero__wrapper__mobile-image'],
                                'md:hidden'
                            )}
                        >
                            <img src={image.file.url} alt={image.title} />
                        </div>
                    ))}
                </div>

                <div className="text-primitive-green-40 text-primitive-blue-50 text-primitive-purple-50 text-greek-blue-3 text-abbey-green-3 text-electric-lemon-3" />
            </div>
        </div>
    )
}

export default AudioHero
