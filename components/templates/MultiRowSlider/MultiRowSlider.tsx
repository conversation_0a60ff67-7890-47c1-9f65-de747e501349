import ElgatoImage from '@components/common/ElgatoImage'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { CardProps, SliderContent } from '@components/templates/Slider/Slider'
import { Splide, SplideSlide } from '@splidejs/react-splide'
import { AutoScroll } from '@splidejs/splide-extension-auto-scroll'
import cn from 'classnames'
import { FC } from 'react'

import s from './MultiRowSlider.module.scss'

export const MultiRowSlider: FC<SliderContent> = ({ content }) => {
    const { textPanel, sliderItems, bgColor, id } = content
    return (
        <div id={id}>
            {textPanel && (
                <TextPanel
                    content={{
                        ...textPanel,

                        textColor: bgColor === 'bg-black' ? 'light' : 'dark'
                    }}
                />
            )}
            <div className="flex flex-col gap-16 md:gap-40 md:py-48">
                {sliderItems.map((item: CardProps, index: number) => {
                    const alternatingSpeed = 0.5 * (index % 2 === 0 ? 1 : -1)
                    return (
                        <Splide
                            key={index}
                            options={{
                                type: 'loop',
                                drag: 'free',
                                focus: 'center',
                                gap: '15rem',
                                perPage: 7,
                                breakpoints: {
                                    1920: {
                                        perPage: 5,
                                        gap: '12rem'
                                    },
                                    1440: { perPage: 4, gap: '10rem' },
                                    1024: { perPage: 3, gap: '8rem' },
                                    768: { perPage: 3, gap: '4rem' }
                                },
                                arrows: false,
                                autoScroll: {
                                    speed: alternatingSpeed,
                                    pauseOnHover: false
                                }
                            }}
                            extensions={{ AutoScroll }}
                        >
                            {item.cloudinaryMedia?.map((media, i) => {
                                return (
                                    <SplideSlide
                                        key={i}
                                        className="h-16 md:h-36"
                                    >
                                        <ElgatoImage
                                            src={media.secure_url}
                                            alt={media.context?.custom.alt}
                                            className={cn(
                                                s['multirow-slider__image'],
                                                'h-full'
                                            )}
                                        />
                                    </SplideSlide>
                                )
                            })}
                        </Splide>
                    )
                })}
            </div>
        </div>
    )
}
