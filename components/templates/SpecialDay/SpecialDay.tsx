import {
    Dropdown,
    DropdownLabelValue,
    DropdownSection
} from '@components/molecules/Dropdown/Dropdown'
import { LinkResponse } from '@components/molecules/Link/Link'
import PrimaryText, {
    HeadlineStyleEnum,
    HorizontalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { LinkListProps } from '@components/organisms/LinkList/LinkList'
import { CardListProps } from '@components/templates/CardList/CardList'
import HomeHeroSlider from '@components/templates/HomeHeroSlider/HomeHeroSlider'
import { SpecialDaySalesCard } from '@components/templates/SpecialDay/SpecialDaySalesCard'
import { SpecialPageContent } from '@components/templates/SpecialPage/SpecialPage'
import cn from 'classnames'
import { TFunction } from 'i18next'
import { useTranslation } from 'next-i18next'
import { createPortal } from 'preact/compat'
import { FC, useEffect, useMemo, useRef, useState } from 'react'
import s from './SpecialDay.module.scss'
import { DealsBanner } from '@components/molecules/DealsBanner/DealsBanner'

const SLIDE_ID = 'special-day-slide'

// key: region identifier
// value: region translatable identifier
const REGIONS: Record<string, string> = {
    au: 'australia',
    nl: 'netherlands',
    us: 'united_states',
    eu: 'rest_of_europe',
    be: 'belgium',
    fr: 'france',
    it: 'italy',
    es: 'spain',
    de: 'germany',
    ca: 'canada',
    pt: 'portugal',
    pl: 'poland',
    uk: 'united_kingdom',
    // se: 'se',
    // Br: 'Br',
    // ww: 'global',
    tw: 'taiwan',
    // lm: 'lm',
    kr: 'korea',
    br: 'brazil',
    jp: 'japan',
    cn: 'china',
    mx: 'mexico',
    // ad: 'andorra',
    // bg: 'bulgaria',
    // hr: 'croatia',
    // cy: 'cyprus',
    // cz: 'czechia',
    // dk: 'denmark',
    // ee: 'estonia',
    // fi: 'finland',
    // gi: 'gibraltar',
    // el: 'greece',
    // hu: 'hungary',
    // ie: 'ireland',
    // lv: 'latvia',
    // li: 'liechtenstein',
    // lt: 'lithuania',
    // lu: 'luxembourg',
    // mt: 'malta',
    // mc: 'monaco',
    // no: 'norway',
    // ro: 'romania',
    // sm: 'san_marino',
    // sk: 'slovakia',
    // sl: 'slovenia',
    // se: 'sweden',
    ch: 'switzerland'
}

const getRegionParentFromRegionIdentifier = (
    regionIdentifier: string,
    t: TFunction
) => {
    if (['us', 'ca', 'br', 'mx'].includes(regionIdentifier)) {
        return t('regions|north_&_south_america')
    }
    if (['au', 'tw', 'kr', 'jp', 'cn'].includes(regionIdentifier)) {
        return t('regions|asia_&_oceania')
    }
    if (
        [
            'be',
            'fr',
            'de',
            'it',
            'pl',
            'pt',
            'ru',
            'es',
            'uk',
            'nl',
            'eu'
        ].includes(regionIdentifier)
    ) {
        return t('regions|europe')
    }
    // if (['ad', 'be', 'bg', 'hr', 'cy', 'dk', 'fi', 'gi', 'el', 'hu', 'ie', 'lv', 'li', 'lt', 'lu', 'mt', 'mc', 'no', 'ro', 'ro', 'sm', 'sk', 'sl', 'se', 'ch'].includes(regionIdentifier)) {
    //     return t('regions|rest_of_europe')
    // }
    return t('regions|rest_of_the_world')
}

export interface CardProductLargeProps {
    links: (LinkListProps | LinkResponse)[]
}

const getRegionsFromCardLists = (
    cardLists: CardListProps[] | undefined,
    t: TFunction
) => {
    if (!cardLists) {
        return []
    }

    const options: DropdownSection[] = []

    cardLists.forEach((cardList) => {
        cardList.regions?.forEach((cardListRegionToAdd) => {
            const regionName: string | undefined =
                REGIONS[cardListRegionToAdd.identifier]
            if (regionName) {
                const regionToAdd = {
                    value: cardListRegionToAdd.identifier,
                    label: t(`regions|${regionName}`)
                }
                const sectionTitle = getRegionParentFromRegionIdentifier(
                    cardListRegionToAdd.identifier,
                    t
                )
                if (
                    !options.find((section) => section.title === sectionTitle)
                ) {
                    options.push({
                        title: sectionTitle,
                        options: []
                    })
                }
                const dropdownSection = options.find(
                    (section) => section.title === sectionTitle
                )
                const dropdownSectionOptions = dropdownSection?.options as
                    | DropdownLabelValue[]
                    | undefined
                if (
                    dropdownSectionOptions &&
                    !dropdownSectionOptions.some(
                        (regionAlreadyAdded) =>
                            regionAlreadyAdded.value === regionToAdd.value
                    )
                ) {
                    dropdownSectionOptions.push(regionToAdd)
                }
            }
        })
    })

    return options
}

export const SpecialDay: FC<SpecialPageContent> = ({ content }) => {
    const { children = [], heroSlider } = content
    const { t } = useTranslation('common')
    const cardsContainerRef = useRef<HTMLDivElement | null>(null)

    const cardLists = children.filter(
        (child) => child.meta?.contentType === 'templateCardList'
    ) as CardListProps[] | undefined
    const dropdownOptions = useMemo(
        () => getRegionsFromCardLists(cardLists, t),
        [cardLists, t]
    )

    const [selectedRegion, setSelectedRegion] = useState('')
    const [isDropdownOpen, setIsDropdownOpen] = useState(false)

    const selectedCardList = cardLists?.find((cardList) =>
        cardList.regions?.some((region) => region.identifier === selectedRegion)
    )

    const onDropdownChange = (value: string) => {
        setSelectedRegion(value)
    }

    useEffect(() => {
        if (selectedRegion === '') {
            return
        }
        if (cardsContainerRef.current) {
            cardsContainerRef.current.scrollIntoView()
        }
    }, [selectedRegion])

    const onDropdownToggle = (prevIsOpen: boolean) => {
        setIsDropdownOpen(!prevIsOpen)
    }

    const slides = [{ ...heroSlider?.sliderItems[0], id: SLIDE_ID }]
    const dropdownContainerRef = useRef<HTMLElement | null>(null)
    useEffect(() => {
        dropdownContainerRef.current = document.getElementById(
            `hero-slider-item__after-container--${SLIDE_ID}`
        )
    }, [])

    return (
        <div className={cn(s['special-day'], 'bg-primitive-gray-10')}>
            {!!heroSlider && (
                <HomeHeroSlider slides={slides} size="special-day-small" />
            )}
            {!!dropdownOptions &&
                dropdownContainerRef.current &&
                createPortal(
                    <div
                        className={cn(
                            'flex flex-col flex-grow gap-24px pointer-events-auto items-center md:items-start md-max:pt-80px'
                        )}
                    >
                        <PrimaryText
                            headline={t('Get the best deal in your region')}
                            headlineTag={HeadlineStyleEnum.H4}
                            headlineStyle={HeadlineStyleEnum.H4}
                            textAlignment={HorizontalAlignmentEnum.LEFT}
                            textColor={slides[0]?.textColor}
                            className={cn(
                                'md:transition-opacity md:duration-200',
                                {
                                    'md:opacity-0': isDropdownOpen
                                }
                            )}
                        />
                        <Dropdown
                            title={t('Select your country*')}
                            options={dropdownOptions}
                            onChange={onDropdownChange}
                            onToggle={onDropdownToggle}
                            className={s['special-day__dropdown']}
                            // eslint-disable-next-line i18next/no-literal-string
                            dropdownVariant="white"
                        />
                    </div>,
                    dropdownContainerRef.current
                )}
            <Container size={ContainerSize.LARGE}>
                <div
                    className="mx-auto py-16 flex flex-wrap gap-16px"
                    ref={cardsContainerRef}
                >
                    {selectedCardList?.children?.map((card, index) => (
                        <SpecialDaySalesCard card={card} key={index} />
                    ))}
                </div>
            </Container>
        </div>
    )
}
