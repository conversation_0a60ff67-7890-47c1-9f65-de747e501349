import { CardProps } from '@components/templates/CardList/CardList'
import unescape from 'lodash.unescape'
import { parseNoBreakLines } from '@config/hooks/useParseNoBreakLines'
import cn from 'classnames'
import React, { forwardRef, useRef } from 'react'
import s from './StackCards.module.scss'
import IndicatorDots from '@components/molecules/IndicatorDots/IndicatorDots'
import ElgatoImage from '@components/common/ElgatoImage'
import { useMedia } from '@lib/hooks/useMedia'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import { useMobile } from '@pylot-data/hooks/use-mobile'

type StackCardProps = {
    card: CardProps
    numberOfCards: number
    activeIndex: number
    onDotClick: (index: number) => void
}

export const StackCard = forwardRef<HTMLDivElement, StackCardProps>(
    ({ card, numberOfCards, activeIndex, onDotClick }, ref) => {
        const { isMobile, isTablet } = useMobile()
        const videoRef = useRef<HTMLDivElement>(null)
        const {
            src: tabletSrc,
            alt: tabletAlt,
            posterImageSrc: tabletPosterImageSrc,
            type: tabletType,
            description: tabletDescription,
            aspectRatio: tabletAspectRatio
        } = useMedia({
            media: card?.tabletMedia,
            posterImage: card?.tabletMediaPoster
        })

        const {
            src,
            alt,
            posterImageSrc,
            type,
            description,
            aspectRatio
        } = useMedia({
            media: card?.media,
            cloudinaryMedia: card?.cloudinaryMedia,
            mobileMedia: card?.mobileMedia,
            cloudinaryMobileMedia: card?.cloudinaryMobileMedia,
            posterImage: card?.posterImage,
            cloudinaryPosterImage: card?.cloudinaryPosterImage
        })
        const background = card?.customOptions?.background as string
        const color = card?.customOptions?.color as string
        const textColor =
            card?.textColor === 'light' ? 'text-white' : 'text-black'

        return (
            <div
                key={card.title}
                ref={ref}
                className={s['stack-cards__card-container']}
            >
                <div
                    className={cn(s['stack-card'], textColor)}
                    style={
                        background
                            ? {
                                  background: background
                              }
                            : undefined
                    }
                >
                    <IndicatorDots
                        length={numberOfCards}
                        activeIndex={activeIndex}
                        onDotClick={onDotClick}
                    />
                    <div className={s['stack-card__text-container']}>
                        {card.textPanel?.headline && (
                            <h3
                                className={
                                    s['stack-card__text-container-headline']
                                }
                                dangerouslySetInnerHTML={{
                                    __html: unescape(
                                        parseNoBreakLines(
                                            card.textPanel.headline
                                        )
                                    )
                                }}
                                style={color ? { color: color } : undefined}
                            />
                        )}
                        {card.textPanel?.bodyCopy && (
                            <div
                                className={cn(
                                    s['stack-card__text-container-body-copy'],
                                    'small-copy whitespace-pre-line rich-text'
                                )}
                                dangerouslySetInnerHTML={{
                                    __html: unescape(
                                        parseNoBreakLines(
                                            card.textPanel.bodyCopy
                                        )
                                    )
                                }}
                            />
                        )}
                    </div>
                    <div className={s['stack-card__media-container']}>
                        {src && type === 'image' && (
                            <div
                                className={cn({
                                    [s['stack-card__image']]:
                                        !isMobile && !isTablet,
                                    [s['stack-card__mobile-image']]: isTablet
                                })}
                                style={{ aspectRatio: `${aspectRatio}` }}
                            >
                                <ElgatoImage
                                    src={src}
                                    alt={alt || ''}
                                    layout="fill"
                                    objectFit="cover"
                                />
                            </div>
                        )}
                        {src && type === 'video' && (
                            <div ref={videoRef}>
                                <ElgatoVideo
                                    className={cn({
                                        [s['stack-card__video']]:
                                            !isMobile && !isTablet
                                    })}
                                    secure_url={src}
                                    options={{
                                        autoPlay: true,
                                        preload: 'true',
                                        muted: true,
                                        loop: true
                                    }}
                                    visibleThreshold={0.9}
                                    fallbackImgUrl={posterImageSrc}
                                    videoDescription={description}
                                />
                            </div>
                        )}
                        {tabletSrc && tabletType === 'image' && (
                            <div
                                className={s['stack-card__tablet-image']}
                                style={{ aspectRatio: `${tabletAspectRatio}` }}
                            >
                                <ElgatoImage
                                    src={tabletSrc}
                                    alt={tabletAlt || ''}
                                    layout="fill"
                                    objectFit="cover"
                                />
                            </div>
                        )}
                        {tabletSrc && tabletType === 'video' && (
                            <ElgatoVideo
                                className={s['stack-card__tablet-video']}
                                secure_url={tabletSrc}
                                options={{
                                    autoPlay: false,
                                    preload: 'auto',
                                    muted: true,
                                    loop: true
                                }}
                                fallbackImgUrl={tabletPosterImageSrc}
                                videoDescription={tabletDescription}
                                visibleThreshold={0.9}
                            />
                        )}
                    </div>
                </div>
            </div>
        )
    }
)
