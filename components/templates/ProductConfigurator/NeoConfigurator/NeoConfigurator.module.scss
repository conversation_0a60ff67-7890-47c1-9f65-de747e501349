.neo-configurator {
    :global(.bg-purple-plum-1) {
        background-color: #f6e34e;
        color: #000000;
    }

    @media screen and (max-width: 1920px) {
        height: unset !important;
    }

    @media screen and (max-width: 1200px) {
        height: 1500px !important;
    }

    @media screen and (max-width: 1100px) {
        height: 1450px !important;
    }

    @media screen and (max-width: 1024px) {
        height: 1390px !important;
    }

    @media screen and (max-width: 960px) {
        height: 1325px !important;
    }

    @media screen and (max-width: 880px) {
        height: 1260px !important;
    }

    @media screen and (max-width: 780px) {
        height: 1245px !important;
    }

    @media screen and (max-width: 710px) {
        height: 1100px !important;
    }

    @media screen and (max-width: 550px) {
        height: 1045px !important;
    }

    @media screen and (max-width: 450px) {
        height: 925px !important;
    }

    @media screen and (min-width: 1200px) {
        padding-right: 32px;
        padding-left: 32px;
    }

    &__parent-selector > :first-child {
        height: 92px;
        padding-top: 6px;
    }

    //@media screen and (max-width: 1440px) {
    //    width: 100%;
    //    margin: auto;
    //}

    &__selector-selected {
        border: 2px solid #83aaf5;
    }

    &__selector-selected-0 {
        height: 192px !important;
        display: flex;
        transition-delay: 500ms;
        transition-duration: 800ms;

        //@media screen and (max-width: 1440px) {
        //    height: 180px !important;
        //}
    }

    &__selector {
        min-height: 56px;
        margin-bottom: 8px;
        border-radius: 16px;

        @media screen and (max-width: 1200px) {
            margin-bottom: 0px;
        }

        input[type='checkbox'] {
            border-color: #0e2587 !important;
        }

        input[type='checkbox']:checked {
            border-color: #0e2587 !important;
            background-color: #0e2587 !important;
        }
    }

    &__image {
        opacity: 0;
        transition: opacity 2s linear;
        display: none;
        visibility: hidden;

        &--current {
            opacity: 1;
            visibility: visible;
            transition: visibility 0s 2s, opacity 2s linear;
            display: block;
            height: 100%;
            width: 100%;
        }
    }

    &__bundlepack {
        border-color: #eaeaea;
        min-height: 59px;
        margin-bottom: 8px;
        padding-top: 10px;
        padding-bottom: 10px;

        input[type='checkbox'] {
            border-color: #151515;
        }

        input[type='checkbox']:checked {
            border-color: #151515;
            background-color: #151515;
        }

        &__title {
            font-size: 14px;
            line-height: 16.8px;
            font-weight: 700;
            text-transform: uppercase;
        }
    }

    &__calloutmessage {
        display: inline-block;
        height: 17px;
        padding: 2px;
        padding-left: 10px;
        padding-right: 10px;
        border-radius: 2px 0px 0px 0px;
        background: #f6e34e;
        font-size: 11px;
        font-weight: 700;
        line-height: 14.4px;
        margin-bottom: 5px;
    }

    & .ConfiguratorProductSelector_conf-product-selector--selected {
        border-color: var(#204cfe);
    }

    &__hgg-customization {
        position: relative;
        z-index: 1;

        margin-bottom: 0;

        @media screen and (max-width: 1200px) {
            margin-bottom: 100px !important;
        }

        @media screen and (max-width: 960px) {
            margin-bottom: 120px !important;
        }
        @screen md-max {
            margin-bottom: 50px !important;
        }

        @screen md {
            margin: 0 auto;
            max-width: 1490px;
            padding-left: 0;
            padding-right: 0;
        }
    }

    &__hgg-customization &__inner {
        border-radius: 12px !important;
        background-color: white;

        > div {
            padding: 0;
            @screen lg {
                padding: 32px;
            }
        }
    }

    &__hgg-customization &__img-wrapper {
        padding: 0 !important;
    }

    &__hgg-customization &__img-container {
        padding: 0 !important;
    }

    &__hgg-customization &__img {
        border-radius: 8px !important;
    }

    &__hgg-customization &__selector-wrapper {
        h3 {
            @apply font-univers67BoldCondensed;
        }
    }

    &__hgg-customization &__footer {
        background-color: var(--primitive-blue-120);
        color: white;
        border-radius: 12px;
        padding: 14px;
    }

    &__hgg-customization &__saving {
        @apply text-black;
    }

    &__hgg-customization &__discount {
        @apply text-h4 text-black;
    }

    &__hgg-customization &__footer button {
        color: var(--primitive-blue-130) !important;
        background-color: white !important;
        border-radius: 6px !important;
    }

    &__between-wrapper {
        height: 100%;
        width: 100%;

        @media screen and (max-width: 1340px) {
            height: unset;
            width: unset;
        }
    }

    &__inner {
        max-width: 1792px;
        margin-left: auto;
        margin-right: auto;
        border-radius: 40px;
        height: 910px;
        max-height: 910px;

        @media screen and (max-width: 1800px) {
            height: 870px;
        }

        @media screen and (max-width: 1700px) {
            height: 800px;
        }

        @media screen and (max-width: 1600px) {
            height: 750px;
        }

        & > div {
            display: flex;
            flex-direction: column;
            gap: 32px;
            padding: 40px;

            @media screen and (max-width: 1200px) {
                gap: 0px;
            }

            @screen md {
                flex-direction: row;
                padding: 40px;
            }
        }

        @screen md {
            background-color: #eaf2ff;
        }
    }

    &__inner {
        @media screen and (max-width: 1200px) {
            height: unset !important;
        }
    }

    @media (max-width: 1440px) {
        &__img-wrapper {
            flex: 1 1;
            justify-content: end;
        }

        &__inner {
            height: 750px;
            margin-left: 25px;
            margin-right: 25px;
        }

        &__selector-wrapper {
            flex: 0.5;
        }
    }

    @media (max-width: 1350px) {
        &__inner {
            height: 750px;
            margin-left: 25px;
            margin-right: 25px;
        }
    }

    @media (max-width: 1250px) {
        &__img-wrapper {
            flex: 1 1;
            justify-content: end;
            width: 100%;
        }
    }

    &__img-container {
        width: 100%;
        overflow: hidden;
        height: 100%;
        aspect-ratio: 1;

        @media screen and (max-width: 1200px) {
            aspect-ratio: unset;
            max-width: 1180px !important;
            width: 100%;
            padding-left: 16px;
            padding-right: 16px;
            height: unset;
        }
    }

    &__img {
        height: 100%;
        width: auto;
        border-radius: 20px;

        @media screen and (max-width: 1500px) {
            max-width: 1180px !important;
            height: auto;
            width: 100%;
            border-radius: 40px;
        }

        @media screen and (max-width: 1200px) {
            max-width: 1180px !important;
            height: auto;
            width: 100%;
            border-radius: 40px;
        }

        @media screen and (max-width: 800px) {
            width: 100% !important;
            max-width: 800px !important;
            border-radius: 40px;
        }
    }

    &__footer button {
        color: white !important;
        background-color: black !important;
        border-radius: 40px !important;
        justify-content: center;
    }

    &__footer {
        max-height: 88px;
        height: 88px;
        padding: 10px;
        padding-left: 25px;
        padding-right: 25px;
        border-radius: 33px;
        grid-row-gap: 12px;
        row-gap: 12px;
        background-color: #c2d8ff;
        color: black !important;

        @media screen and (max-width: 1200px) {
            bottom: -16px !important;
            max-width: 1200px;
            max-height: 161px;
            height: 161px;
            margin-top: 36px;
        }

        @media screen and (max-width: 800px) {
            bottom: -16px !important;
            max-height: 161px;
            height: 161px;
        }

        @screen md-max {
            margin-left: -16px;
            margin-right: -16px;
            border-radius: 12px 12px 0 0;
        }
    }

    &__price-display {
        height: 55px;
    }

    &__saving {
        display: flex;
        flex-direction: column;
        color: #0c2588;
        border-radius: 11px;
        padding: 8px;
        margin-right: 32px;
        background-color: white;

        @screen md {
            margin-right: 12px;
        }
    }

    &__discount {
        background-color: #c2d8ff;
        color: #0c2588;
    }

    &__saving-wrapper {
        max-width: 0;
        opacity: 0;
        transition: max-width 0.3s ease-in-out, opacity 0.3s ease-in-out;

        &--visible {
            max-width: calc(50% + 16px);
            opacity: 1;

            @screen md {
                max-width: 50%;
            }
        }
    }

    &__img-wrapper {
        flex-grow: 1;

        @media screen and (max-width: 1200px) {
            justify-content: center !important;
            background: white;
            padding-bottom: 16px;
        }
    }

    &__selector-wrapper {

        flex-grow: 1;
        max-width: 592px;

        @media screen and (max-width: 1200px) {
            height: 840px;
            margin-bottom: 34px;
        }
    }

    @media (min-width: 1025px) and (max-width: 1140px) {
        &__img-wrapper {
            flex: 0.5 !important;
        }

        &__selector-wrapper {
            flex: 0.5;
        }
    }

    &__imageselector {
        display: none !important;
    }

    @media (max-width: 1200px) {
        padding-top: 15px;


        &__inner {
            margin-top: 16px;
            margin-bottom: 16px;
            margin-left: unset;
            margin-right: unset;
        }

        & > div:first-child {
            color: white;
        }

        &__footer {
            margin-left: 0px;
            margin-right: 0px;

            border-radius: 12px 12px 0 0;

            @media screen and (max-width: 1200px) {
                border-radius: 32px 32px 32px 32px;
            }
        }

        &__inner > div {
            background-color: #f6f6f6;
            flex-direction: column;
        }

        &__inner > div:first-child {
            padding: unset !important;
        }

        &__selector-wrapper {
            color: black !important;
            padding-left: 16px;
            padding-right: 16px;
            max-width: 128rem;

            & > div {
                margin: unset;
                padding-top: unset;
            }
        }

        &__imageselector {
            display: none !important;
        }

        &__img-wrapper > div {
            border-radius: unset;
        }
    }
}
