import { Badge } from '@components/atoms/Badge/Badge'
import { Price } from '@components/atoms/Price/Price'
import { Button } from '@components/molecules/Button/Button'
import { ConfiguratorProductSelector } from '@components/molecules/ConfiguratorProductSelector/ConfiguratorProductSelector'
import { ConfiguratorSelectors } from '@components/templates/ProductConfigurator/ConfiguratorSelectors'
import { KeyLightMk2ConfiguratorAddToCart } from '@components/templates/ProductConfigurator/KeyLightMk2Configurator/KeyLightMk2ConfiguratorAddToCart'
import { ProductConfiguratorContent } from '@components/templates/ProductConfigurator/ProductConfigurator'
import {
    KEY_LIGHT_MK2_2x_SKU,
    KEY_LIGHT_MK2_SKU,
    KEY_LIGHT_REMOTE_SKU
} from '@components/templates/ProductConfigurator/skus'
import {
    BundleTypeData,
    BundlesType
} from '@components/templates/ProductConfigurator/types'
import { usePrice } from '@corsairitshopify/pylot-price/index'
import { formatPriceSymbol } from '@lib/utils/priceUtils'
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import { useProductUI } from '@pylot-data/hooks/product/use-product-ui'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import React, { FC, useEffect, useState } from 'react'
import s from './KeyLightMk2Configurator.module.scss'
import {
    KEY_LIGHT_MK2_BUNDLES_SKUS_DICT,
    KEY_LIGHT_MK2_BUNDLE_IMAGE,
    cameraItems,
    controllerItems,
    mountItems,
    productsSkus,
    streamdeckItems
} from './config'
import { useStoreConfig } from '@config/index'
import { chunkArray } from '@lib/utils/arrayUtils'
import { decode } from 'he'
import { useMobile } from '@pylot-data/hooks/use-mobile'

function stringToDecimal(str: any) {
    const num = parseFloat(str.replace(/[^0-9.-]+/g, ''))
    return num
}

export const KeyLightMk2Configurator: FC<ProductConfiguratorContent> = ({
    content
}) => {
    const { locale } = useRouter()
    const { isMobile } = useMobile()
    const { t } = useTranslation('common')
    const [savingsInPercent, setSavingsInPercent] = useState(0)
    const {
        base: { currencyConfig }
    } = useStoreConfig()
    const currencyMap = currencyConfig?.elgato?.currencyMap

    const additionalTextPanel = content.additionalTextPanels?.[0]
    const linkOverride = additionalTextPanel?.link

    const [productsData, setProductsData] = useState<
        (SimpleProduct | ConfigurableProduct)[]
    >([])
    const [selectedMountSku, setSelectedMountSku] = useState('')
    const [selectedCameraSku, setSelectedCameraSku] = useState('')
    const [selectedControllerSku, setSelectedControllerSku] = useState(
        KEY_LIGHT_REMOTE_SKU
    )
    const [selectedStreamdeckSku, setSelectedStreamdeckSku] = useState('')
    const [bundleSkusDict, setBundleSkusDict] = useState<BundlesType>({})
    const [isDoubleKeyLightSelected, setIsDoubleKeyLightSelected] = useState(
        false
    )

    const keyLightProduct = productsData.find(
        (p) => p.sku === KEY_LIGHT_MK2_SKU
    )

    const keyLight2xProduct = productsData.find(
        (p) => p.sku === KEY_LIGHT_MK2_2x_SKU
    )

    const selectedSkus = [
        KEY_LIGHT_MK2_SKU,
        selectedMountSku,
        selectedCameraSku,
        selectedControllerSku,
        selectedStreamdeckSku
    ]

    const getSelectedBundle = () => {
        if (!productsData.length || !Object.keys(bundleSkusDict).length) {
            // Default fallback - ensure this bundle key exists
            const fallbackBundleKey = 'key-light-remote'
            if (KEY_LIGHT_MK2_BUNDLES_SKUS_DICT[fallbackBundleKey]) {
                return KEY_LIGHT_MK2_BUNDLES_SKUS_DICT[fallbackBundleKey]
            } else {
                // If fallback doesn't exist, return the first available bundle
                const firstBundleKey = Object.keys(
                    KEY_LIGHT_MK2_BUNDLES_SKUS_DICT
                )[0]
                return KEY_LIGHT_MK2_BUNDLES_SKUS_DICT[firstBundleKey]
            }
        }

        // Only Key Light Mk2 selected (no accessories)
        if (
            !isDoubleKeyLightSelected &&
            !selectedMountSku &&
            !selectedCameraSku &&
            !selectedControllerSku &&
            !selectedStreamdeckSku
        ) {
            return {
                product: keyLightProduct,
                image: KEY_LIGHT_MK2_BUNDLE_IMAGE
            } as BundleTypeData
        }

        // Filter non-empty SKUs and create a normalized string for comparison
        const filteredSkus = selectedSkus.filter((sku) => !!sku)
        const normalizedSelectedSkus = filteredSkus.sort().join('|')

        // Look for matching bundle
        const foundBundle = Object.entries(bundleSkusDict).find(
            ([sku, bundle]) => {
                // Normalize bundle SKUs the same way
                const normalizedBundleSkus = bundle.bundleProductSkus
                    .filter((sku) => !!sku)
                    .sort()
                    .join('|')

                if (isDoubleKeyLightSelected) {
                    return (
                        normalizedBundleSkus === normalizedSelectedSkus &&
                        sku.startsWith('key-light-2x-')
                    )
                }
                return (
                    normalizedBundleSkus === normalizedSelectedSkus &&
                    !sku.startsWith('key-light-2x-')
                )
            }
        )

        if (foundBundle) {
            return foundBundle[1]
        }

        // If no exact match, log for debugging
        console.warn(
            `No matching bundle found for selected SKUs: ${normalizedSelectedSkus}`
        )

        // Return default single key light if no match found
        return {
            product: keyLightProduct,
            image: KEY_LIGHT_MK2_BUNDLE_IMAGE
        } as BundleTypeData
    }

    const selectedBundle = getSelectedBundle()
    console.log(
        'Selected Bundle:',
        selectedBundle?.product?.sku,
        selectedBundle?.product?.price_range
    )
    const { isOutOfStock, notSellable } = useProductUI(selectedBundle?.product)
    const { total, subtotal, discount, currency } = usePrice(
        selectedBundle?.product?.price_range
    )

    useEffect(() => {
        let savings = 0

        // Calculate percentage savings when we have valid subtotal and discount
        if (subtotal && discount && stringToDecimal(subtotal) > 0) {
            // Calculate discount percentage: (discount amount / original price) * 100
            savings = Math.round(
                (stringToDecimal(discount) / stringToDecimal(subtotal)) * 100
            )
        } else {
            // Fallback to 0 if we don't have valid price data
            savings = 0
        }

        setSavingsInPercent(savings)
    }, [selectedBundle, subtotal, discount, total])

    const onKeyLightSelect = () => {
        const prevIsDoubleKeyLightSelected = isDoubleKeyLightSelected
        if (selectedControllerSku === '') {
            setSelectedControllerSku(KEY_LIGHT_REMOTE_SKU)
        }
        setIsDoubleKeyLightSelected((prev) => !prev)
    }

    const onMountSelect = (sku: string) => {
        if (selectedMountSku === sku) {
            setSelectedMountSku('')
        } else {
            setSelectedMountSku(sku)
        }
    }

    const onCameraSelect = (sku: string) => {
        if (selectedCameraSku === sku) {
            setSelectedCameraSku('')
        } else {
            setSelectedCameraSku(sku)
        }
    }

    const onControllerSelect = (sku: string) => {
        if (selectedControllerSku === sku && !isDoubleKeyLightSelected) {
            // can be deselected only when single keylight is selected
            //setSelectedControllerSku('')
        } else {
            setSelectedControllerSku(sku)
        }
    }

    const onStreamdeckSelect = (sku: string) => {
        if (selectedStreamdeckSku === sku) {
            // can be deselected only when single keylight is selected
            setSelectedStreamdeckSku('')
        } else {
            setSelectedStreamdeckSku(sku)
        }
    }

    useEffect(() => {
        const getProducts = async (skus: string[]) => {
            if (skus.length > 0) {
                const arrListSkusChunked = chunkArray(skus, 29)
                let fetchedProducts: (
                    | ConfigurableProduct
                    | SimpleProduct
                )[] = []

                for (const listSkus of arrListSkusChunked) {
                    const products: (
                        | ConfigurableProduct
                        | SimpleProduct
                    )[] = await getProductsBySkus(listSkus, locale ?? '')
                    fetchedProducts = [...fetchedProducts, ...products]
                }
                return fetchedProducts
            }
        }

        const fetchBundlesAndProducts = async () => {
            await getProducts(productsSkus).then((products) => {
                setProductsData(products ?? [])
            })

            // Chunk bundle SKUs to avoid API limitations
            const bundleSkus = Object.keys(KEY_LIGHT_MK2_BUNDLES_SKUS_DICT)
            const arrBundleSkusChunked = chunkArray(bundleSkus, 29)
            let fetchedBundles: (ConfigurableProduct | SimpleProduct)[] = []

            // Fetch bundles in chunks
            for (const bundleSkusChunk of arrBundleSkusChunked) {
                try {
                    // @ts-ignore
                    const [bundles] = await Promise.all([
                        getProducts(bundleSkusChunk)
                    ])
                    if (bundles) {
                        fetchedBundles = [...fetchedBundles, ...bundles]
                    }
                } catch (error) {
                    console.error(`Error fetching bundle chunk: ${error}`)
                }
            }

            const copyOfBundleSkusDict = {
                ...KEY_LIGHT_MK2_BUNDLES_SKUS_DICT
            }

            fetchedBundles.forEach((bundle) => {
                const sku = bundle.sku
                const bundleProductsSkus: string[] = []
                bundle.bundle_products?.forEach((bundleProduct) => {
                    const bundleProductSku = bundleProduct?.sku
                    if (bundleProductSku) {
                        bundleProductsSkus.push(bundleProductSku)
                    }
                })

                if (sku) {
                    copyOfBundleSkusDict[sku] = {
                        ...copyOfBundleSkusDict[sku],
                        bundleProductSkus:
                            sku === KEY_LIGHT_MK2_SKU
                                ? [KEY_LIGHT_MK2_SKU]
                                : bundleProductsSkus,
                        product: bundle
                    }
                }
            })

            // Log any bundles that weren't fetched for debugging
            const fetchedSkus = fetchedBundles.map((bundle) => bundle.sku)
            const missingBundles = bundleSkus.filter(
                (sku) => !fetchedSkus.includes(sku)
            )
            if (missingBundles.length > 0) {
                console.warn(`Missing bundles: ${missingBundles.join(', ')}`)
            }

            setBundleSkusDict(copyOfBundleSkusDict)
        }

        void fetchBundlesAndProducts()
    }, [locale])

    return (
        <div className={cn(s['key-light-mk2-configurator'])}>
            <div className="px-32px pb-16px md:hidden">
                <div className="flex-col gap-8px items-center justify-start text-center flex">
                    {additionalTextPanel?.calloutTitle && (
                        <Badge
                            className="bg-purple-plum-1"
                            // eslint-disable-next-line i18next/no-literal-string
                            size="small"
                        >
                            {additionalTextPanel.calloutTitle}
                        </Badge>
                    )}
                    <div className="flex flex-col gap-8px">
                        {additionalTextPanel?.headline && (
                            <h3>{additionalTextPanel.headline}</h3>
                        )}
                        {additionalTextPanel?.bodyCopy && (
                            <p className="text-small-copy">
                                {additionalTextPanel.bodyCopy}
                            </p>
                        )}
                    </div>
                </div>
            </div>
            <div className={cn(s['key-light-mk2-configurator__inner'])}>
                <div className="flex flex-col md:flex-row md:items-start md:gap-32px md:px-32px relative">
                    <div
                        className={s['key-light-mk2-configurator__sticky-imgs']}
                    >
                        <div
                            className={
                                s['key-light-mk2-configurator__img-container']
                            }
                        >
                            <img
                                key={KEY_LIGHT_MK2_BUNDLE_IMAGE}
                                src={KEY_LIGHT_MK2_BUNDLE_IMAGE}
                                alt=""
                                className={cn(
                                    s['key-light-mk2-configurator__img'],
                                    KEY_LIGHT_MK2_BUNDLE_IMAGE ===
                                        selectedBundle?.image
                                        ? 'opacity-1 visible'
                                        : 'opacity-0 invisible'
                                )}
                            />
                            {Object.values(KEY_LIGHT_MK2_BUNDLES_SKUS_DICT).map(
                                (bundle, i) => (
                                    <img
                                        key={bundle.image + i}
                                        src={bundle?.image}
                                        alt=""
                                        className={cn(
                                            s[
                                                'key-light-mk2-configurator__img'
                                            ],
                                            bundle.image ===
                                                selectedBundle?.image
                                                ? 'opacity-1 visible'
                                                : 'opacity-0 invisible'
                                        )}
                                    />
                                )
                            )}
                        </div>
                    </div>
                    <div
                        className={
                            s['key-light-mk2-configurator__selectors-container']
                        }
                    >
                        {!!productsData.length && (
                            <>
                                <div className="flex-col gap-16px items-start justify-start hidden md:flex">
                                    {additionalTextPanel?.calloutTitle && (
                                        <Badge
                                            className="bg-purple-plum-1"
                                            // eslint-disable-next-line i18next/no-literal-string
                                            size="small"
                                        >
                                            {additionalTextPanel.calloutTitle}
                                        </Badge>
                                    )}
                                    <div className="flex flex-col gap-8px">
                                        {additionalTextPanel?.headline && (
                                            <h3>
                                                {additionalTextPanel.headline}
                                            </h3>
                                        )}
                                        {additionalTextPanel?.bodyCopy && (
                                            <p className="text-small-copy">
                                                {additionalTextPanel.bodyCopy}
                                            </p>
                                        )}
                                    </div>
                                </div>
                                <div className="flex flex-col gap-16px pt-16px">
                                    <div className="flex flex-col pb-16px">
                                        <div className="flex flex-col gap-8px md-max:overflow-auto md-max:px-16px">
                                            {keyLightProduct &&
                                                keyLight2xProduct && (
                                                    <>
                                                        <ConfiguratorProductSelector
                                                            product={
                                                                keyLightProduct
                                                            }
                                                            Headline={
                                                                <div className="flex gap-4px">
                                                                    {/* eslint-disable-next-line i18next/no-literal-string */}
                                                                    <h6 className="lowercase flex-shrink-0">
                                                                        1 x
                                                                    </h6>
                                                                    <h6>
                                                                        {t(
                                                                            'keyLightMk2Configurator|Key Light Mk.2'
                                                                        )}
                                                                    </h6>
                                                                </div>
                                                            }
                                                            selected={
                                                                !isDoubleKeyLightSelected
                                                            }
                                                            onSelect={
                                                                onKeyLightSelect
                                                            }
                                                            ignoreOutOfStock
                                                            // eslint-disable-next-line i18next/no-literal-string
                                                            color="dark"
                                                        />
                                                        <ConfiguratorProductSelector
                                                            product={
                                                                keyLight2xProduct
                                                            }
                                                            Headline={
                                                                <div className="flex gap-4px">
                                                                    {/* eslint-disable-next-line i18next/no-literal-string */}
                                                                    <h6 className="lowercase flex-shrink-0">
                                                                        2 x
                                                                    </h6>
                                                                    <h6>
                                                                        {t(
                                                                            'keyLightMk2Configurator|Key Light Mk.2'
                                                                        )}
                                                                    </h6>
                                                                </div>
                                                            }
                                                            selected={
                                                                isDoubleKeyLightSelected
                                                            }
                                                            onSelect={
                                                                onKeyLightSelect
                                                            }
                                                            ignoreOutOfStock
                                                            // eslint-disable-next-line i18next/no-literal-string
                                                            color="dark"
                                                        />
                                                    </>
                                                )}
                                        </div>
                                        {additionalTextPanel?.subheader && (
                                            <p
                                                className={
                                                    s[
                                                        'key-light-mk2-configurator__additional-text-2x'
                                                    ]
                                                }
                                            >
                                                {additionalTextPanel.subheader}
                                            </p>
                                        )}
                                    </div>
                                </div>
                                <ConfiguratorSelectors
                                    headline={t(
                                        'keyLightMk2Configurator|Controllers'
                                    )}
                                >
                                    {controllerItems.map((item) => {
                                        const product = productsData?.find(
                                            (p) => p.sku === item.sku
                                        )
                                        if (product) {
                                            return (
                                                <ConfiguratorProductSelector
                                                    key={`product-selector-${item.sku}`}
                                                    product={product}
                                                    headline={
                                                        item.headline_key
                                                            ? t(
                                                                  item.headline_key
                                                              )
                                                            : undefined
                                                    }
                                                    onSelect={() =>
                                                        onControllerSelect(
                                                            item.sku
                                                        )
                                                    }
                                                    selected={
                                                        selectedControllerSku ===
                                                        item.sku
                                                    }
                                                    free
                                                    showFreeWhenNotSelected
                                                    ignoreRadioSelected
                                                    ignoreOutOfStock
                                                    // eslint-disable-next-line i18next/no-literal-string
                                                    variant="radio"
                                                />
                                            )
                                        }
                                    })}
                                </ConfiguratorSelectors>
                                <ConfiguratorSelectors
                                    headline={t(
                                        'keyLightMk2Configurator|Extra mount'
                                    )}
                                    items={mountItems}
                                    products={productsData}
                                    selectedSku={selectedMountSku}
                                    onSelect={onMountSelect}
                                />
                                <ConfiguratorSelectors
                                    headline={t(
                                        'keyLightMk2Configurator|Cameras'
                                    )}
                                    items={cameraItems}
                                    products={productsData}
                                    selectedSku={selectedCameraSku}
                                    onSelect={onCameraSelect}
                                />
                                <ConfiguratorSelectors
                                    headline={t(
                                        'keyLightMk2Configurator|StreamDecks'
                                    )}
                                >
                                    {streamdeckItems.map((item) => {
                                        const product = productsData?.find(
                                            (p) => p.sku === item.sku
                                        )
                                        if (product) {
                                            return (
                                                <ConfiguratorProductSelector
                                                    key={`product-selector-${item.sku}`}
                                                    product={product}
                                                    headline={
                                                        item.headline_key
                                                            ? t(
                                                                  item.headline_key
                                                              )
                                                            : undefined
                                                    }
                                                    onSelect={() =>
                                                        onStreamdeckSelect(
                                                            item.sku
                                                        )
                                                    }
                                                    selected={
                                                        selectedStreamdeckSku ===
                                                        item.sku
                                                    }
                                                    showFreeWhenNotSelected
                                                    ignoreRadioSelected
                                                    ignoreOutOfStock
                                                    // eslint-disable-next-line i18next/no-literal-string
                                                    variant="radio"
                                                />
                                            )
                                        }
                                    })}
                                </ConfiguratorSelectors>
                                {selectedBundle?.product && (
                                    <div
                                        className={cn(
                                            s[
                                                'key-light-mk2-configurator__footer-container'
                                            ]
                                        )}
                                    >
                                        <div
                                            className={cn(
                                                s[
                                                    'key-light-mk2-configurator__footer'
                                                ]
                                            )}
                                        >
                                            {discount && (
                                                <div
                                                    className={cn(
                                                        s[
                                                            'key-light-mk2-configurator__saving-wrapper'
                                                        ],
                                                        {
                                                            [s[
                                                                'key-light-mk2-configurator__saving-wrapper--visible'
                                                            ]]: !!savingsInPercent
                                                        }
                                                    )}
                                                >
                                                    <div
                                                        className={cn(
                                                            s[
                                                                'key-light-mk2-configurator__saving'
                                                            ]
                                                        )}
                                                    >
                                                        <div className="flex flex-row gap-8px">
                                                            <span className="button-text-small button-text-bold font-univers65Bold uppercase">
                                                                {t(
                                                                    'keyLightMk2Configurator|Saving'
                                                                )}
                                                            </span>
                                                            <Badge
                                                                // eslint-disable-next-line i18next/no-literal-string
                                                                size="small"
                                                                className="bg-primitive-purple-100"
                                                            >
                                                                {`${savingsInPercent}%`}
                                                            </Badge>
                                                        </div>
                                                        <Price
                                                            price={formatPriceSymbol(
                                                                discount,
                                                                currency,
                                                                locale,
                                                                currencyMap
                                                            )}
                                                            // eslint-disable-next-line i18next/no-literal-string
                                                            theme="light"
                                                            // eslint-disable-next-line i18next/no-literal-string
                                                            size="big"
                                                        />
                                                    </div>
                                                </div>
                                            )}
                                            <div
                                                className={
                                                    s[
                                                        'key-light-mk2-configurator__price-wrapper'
                                                    ]
                                                }
                                            >
                                                <p className="button-text-small uppercase text-primitive-gray-70 font-univers65Bold">
                                                    {t('Subtotal')}
                                                </p>
                                                <div className="flex flex-row-reverse md:flex-col gap-x-4px md:gap-x-0 content-end items-end md:content-start md:items-start relative">
                                                    <Price
                                                        price={formatPriceSymbol(
                                                            total,
                                                            currency,
                                                            locale,
                                                            currencyMap
                                                        )}
                                                        // eslint-disable-next-line i18next/no-literal-string
                                                        size="big"
                                                        // eslint-disable-next-line i18next/no-literal-string
                                                        theme="light"
                                                    />
                                                    {subtotal !== total && (
                                                        <div
                                                            className={
                                                                s[
                                                                    'key-light-mk2-configurator__price-discount'
                                                                ]
                                                            }
                                                        >
                                                            <Price
                                                                price={formatPriceSymbol(
                                                                    subtotal,
                                                                    currency,
                                                                    locale,
                                                                    currencyMap
                                                                )}
                                                                // eslint-disable-next-line i18next/no-literal-string
                                                                size="xs"
                                                                variant="discount"
                                                                // eslint-disable-next-line i18next/no-literal-string
                                                                themeDiscount="light"
                                                            />
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            {notSellable && linkOverride ? (
                                                <Button
                                                    variant={linkOverride.style}
                                                    color={
                                                        linkOverride.styleColor
                                                    }
                                                    iconAlignment={
                                                        linkOverride.iconAlignment
                                                    }
                                                    href={linkOverride.linkUrl}
                                                    newTab={linkOverride.newTab}
                                                    className="md:self-center"
                                                    label={
                                                        linkOverride.linkTitle
                                                    }
                                                >
                                                    {linkOverride.linkTitle}
                                                </Button>
                                            ) : (
                                                <KeyLightMk2ConfiguratorAddToCart
                                                    id={`keylight-configurator-atc-btn-${selectedBundle.product.uid}`}
                                                    product={
                                                        selectedBundle.product
                                                    }
                                                    className="w-full md:w-auto md:self-center"
                                                    buttonLabel={
                                                        isOutOfStock
                                                            ? t('Out of Stock')
                                                            : t(
                                                                  'keyLightMk2Configurator|Add to Cart'
                                                              )
                                                    }
                                                />
                                            )}
                                        </div>
                                        <div
                                            className={cn('flex')}
                                            style={{
                                                marginBottom: !isMobile
                                                    ? '8px'
                                                    : '0px',
                                                marginTop: !isMobile
                                                    ? '8px'
                                                    : '0px',
                                                backgroundColor: '#FFFFFF',
                                                padding: isMobile
                                                    ? '16px'
                                                    : '0px'
                                            }}
                                        >
                                            <p
                                                className={cn(
                                                    'text-primitive-gray-60 text-1.1xl bold'
                                                )}
                                                dangerouslySetInnerHTML={{
                                                    __html: decode(
                                                        additionalTextPanel?.richText ??
                                                            ''
                                                    )
                                                }}
                                            />
                                        </div>
                                    </div>
                                )}
                            </>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}
