import { getPreSelection } from '@components/common/VariantSelector/utils/getPreSelection'
import { useClickFrom } from '@components/corra/AddToCart/AddToCart'
import { Button } from '@components/molecules/Button/Button'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { processEnteredOptions } from '@pylot-data/hooks/cart/utils/processEnteredOptions'
import { useProductUI } from '@pylot-data/hooks/product/use-product-ui'
import { useCartItemHelper } from 'helpers/cartItemHelper'
import { useTranslation } from 'next-i18next'
import { FC, ReactNode } from 'react'

export type NeoConfiguratorAddToCartProps = {
    product: any
    popFilter?: any
    isOutOfStock?: boolean
    buttonLabel?: string
    addProductHandler?: () => void
    className?: string
    children?: ReactNode
    buttonComponent?: 'a' | 'button'
    labelClassName?: string
    buttonVariant?: 'primary' | 'secondary'
    buttonColor?: 'blue' | 'light' | 'dark'
    addedToCartHandler?: () => void
    clickFromTab?: string
    trigger?: number
    cmsATCLocation?: number
    isHGG?: boolean
}

export const GiftGuideNeoConfiguratorAddToCart: FC<NeoConfiguratorAddToCartProps> = (
    props
) => {
    const {
        buttonLabel,
        product,
        className,
        children,
        buttonComponent = 'button',
        labelClassName = '',
        buttonVariant = 'primary',
        buttonColor,
        clickFromTab = '',
        cmsATCLocation = 1,
        popFilter,
        isHGG
    } = props
    const { t } = useTranslation(['common'])
    const updateCartItemsLocalStorage = useCartItemHelper()
    const clickFrom = useClickFrom(cmsATCLocation)
    const { isAdding, addToCart } = useAddToCart()
    const preSelection = getPreSelection(product.uid, product as any)

    const variant1 = useProductUI(product, {
        preselectedOptions: preSelection ?? {}
    })
    const variant2 = useProductUI(popFilter, {
        preselectedOptions: preSelection ?? {}
    })

    //console.log('popFilter', popFilter, variant1, variant2)

    let label = isAdding ? t('Adding...') : buttonLabel
    if (variant1.isOutOfStock) {
        label = variant1.buttonLabel
    }

    if (variant2.isOutOfStock) {
        label = variant2.buttonLabel
    }

    const addProductToCart = async () => {
        updateCartItemsLocalStorage(variant1.variant, clickFromTab || clickFrom)
        const entered_options = processEnteredOptions(variant1.variant)

        const response = await addToCart(
            [
                {
                    sku: variant1.variant.sku,
                    uid: variant1.variant.uid,
                    ...(variant1.variant.sku !== product.sku && {
                        parent_sku: product.sku
                    }),
                    selected_options: variant1.selectedOptionUIDs,
                    quantity: 1,
                    entered_options,
                    bundle_and_save_items:
                        variant1.variant?.elgato_bundle_and_save_skus || []
                }
            ],
            [product]
        )

        if (popFilter) {
            if (variant2.variant)
                updateCartItemsLocalStorage(
                    variant2.variant,
                    clickFromTab || clickFrom
                )
            const entered_options2 = processEnteredOptions(variant2.variant)
            const response2 = await addToCart(
                [
                    {
                        sku: variant2.variant.sku,
                        uid: variant2.variant.uid,
                        ...(variant2.variant.sku !== popFilter.sku && {
                            parent_sku: popFilter.sku
                        }),
                        entered_options: entered_options2,
                        selected_options: variant2.selectedOptionUIDs,
                        quantity: 1,
                        bundle_and_save_items:
                            variant2.variant?.elgato_bundle_and_save_skus || []
                    }
                ],
                [popFilter]
            )
        }
    }
    return (
        <Button
            onClick={addProductToCart}
            color={buttonColor}
            variant={buttonVariant}
            Component={buttonComponent}
            disabled={variant1.isOutOfStock || variant2.isOutOfStock}
            className={className}
        >
            {!!label && <span className={labelClassName}>{label}</span>}
            {children}
        </Button>
    )
}
