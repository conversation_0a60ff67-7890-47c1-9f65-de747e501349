import s from './MerchConfigurator.module.scss'
import cn from 'classnames'
import React, { FC } from 'react'
import { MerchConfiguratorTileVariant } from '@components/templates/ProductConfigurator/MerchConfigurator/MerchConfiguratorTileVariant'
import { VariantFitOrSize } from '@components/templates/ProductConfigurator/MerchConfigurator/MerchConfiguratorCategorySelection'

export type MerchConfiguratorTileProps = {
    type: 'fit' | 'size'
    className?: string
    variants: VariantFitOrSize[]
    handleClick: (variant: string, type: string) => void
    selected?: string
    availableSizesForFit?: Record<string, string[]>
    selectedFit?: string
    notSellableFits?: Set<string>
}

export const MerchConfiguratorTile: FC<MerchConfiguratorTileProps> = (
    props
) => {
    const {
        className,
        variants,
        handleClick,
        type,
        selected = '',
        availableSizesForFit,
        selectedFit = '',
        notSellableFits
    } = props

    // const isAvailableSize = (size: string) => {
    //     return !!availableSizesForFit?.[selectedFit]?.some(
    //         (availableSize) => availableSize === size
    //     )
    // }

    return (
        <div className={cn(s['merch-config-tile'], className)}>
            {variants.map((variant) => (
                <MerchConfiguratorTileVariant
                    key={`merch-config-tile-${type}-${Math.floor(
                        Math.random() * 100
                    )}-${variant.value}`}
                    label={variant.label}
                    handleClick={() => handleClick(variant.value, type)}
                    variant={type}
                    selected={selected === variant.value}
                    disabled={
                        type === 'size'
                            ? notSellableFits?.has(selectedFit)
                            : false
                    }
                />
            ))}
        </div>
    )
}
