import s from './MerchConfigurator.module.scss'
import cn from 'classnames'
import React, { FC } from 'react'
import { MerchConfiguratorSizeChart } from '@components/templates/ProductConfigurator/MerchConfigurator/MerchConfiguratorSizeChart'
import { useTranslation } from 'next-i18next'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'

export type VariantFitOrSize = {
    value: string
    label: string
    shortLabel: string
    available?: boolean
}

export type SizeConfig = {
    headline?: string
    fits: VariantFitOrSize[]
    sizes: VariantFitOrSize[]
    sizingHelp?: string
    selectedSize?: string
    selectedFit?: string
}

export type MerchConfiguratorCategorySelectionProps = {
    headline?: string
    subHeader?: string
    className?: string
    sizeConfig?: SizeConfig[]
    setConfig: (name: string, type: string) => void
    productsData: (SimpleProduct | ConfigurableProduct)[]
    notSellableFits: Set<string>
}

export const MerchConfiguratorCategorySelection: FC<MerchConfiguratorCategorySelectionProps> = (
    props
) => {
    const {
        className,
        headline,
        subHeader,
        sizeConfig = [],
        setConfig,
        productsData,
        notSellableFits
    } = props
    const { t } = useTranslation(['common'])
    return (
        <div className={cn(s['merch-config-category-selection'], className)}>
            {headline && <h4 className="mt-8px">{headline}</h4>}
            {subHeader && (
                <div className="small-copy mt-8px md:mt-4px">{subHeader}</div>
            )}
            {sizeConfig.length > 0 &&
                sizeConfig.map((config, i) => (
                    <MerchConfiguratorSizeChart
                        key={`merch-size-chart-${i}`}
                        headline={config.headline ?? t('Fits & Sizes')}
                        fits={config.fits}
                        sizes={config.sizes.filter((size) => size.available)}
                        sizingHelp={config.sizingHelp}
                        setConfig={setConfig}
                        selectedFit={config.selectedFit}
                        selectedSize={config.selectedSize}
                        productsData={productsData}
                        notSellableFits={notSellableFits}
                    />
                ))}
        </div>
    )
}
