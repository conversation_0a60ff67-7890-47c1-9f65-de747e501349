.fallout-configurator {

    :global(.bg-purple-plum-1) {
        background-color: #F6E34E;
        color: #000000;
    }

    padding-top: 52px;
    padding-bottom: 80px;

    &__selector {
        border-color: #EAEAEA;
        min-height: 59px;
        margin-bottom: 8px;

        input[type="checkbox"] {
            border-color: #151515 !important;
        }

        input[type="checkbox"]:checked {
            border-color: #151515 !important;
            background-color: #151515 !important;
        }


    }

    &__image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: 0.3s ease-out;

        &--current {
            opacity: 1;
        }
    }

    &__bundlepack {
        border-color: #EAEAEA;
        min-height: 59px;
        margin-bottom: 8px;
        padding-top: 10px;
        padding-bottom: 10px;

        input[type="checkbox"] {
            border-color: #151515;
        }

        input[type="checkbox"]:checked {
            border-color: #151515;
            background-color: #151515;
        }

        &__title {
            font-size: 14px;
            line-height: 16.8px;
            font-weight: 700;
            text-transform: uppercase;
        }
    }

    &__calloutmessage {
        display: inline-block;
        height: 17px;
        padding: 2px;
        padding-left: 10px;
        padding-right: 10px;
        border-radius: 2px 0px 0px 0px;
        background: #F6E34E;
        font-size: 11px;
        font-weight: 700;
        line-height: 14.4px;
        margin-bottom: 5px;
    }

    & .ConfiguratorProductSelector_conf-product-selector--selected {
        border-color: var(#204cfe);
    }


    &__inner {
        max-width: 1490px;
        margin-left: auto;
        margin-right: auto;
        border-radius: 12px;

        @screen md {
            background-color: var(--white);
        }
    }

    &__img-container {
        position: relative;
        aspect-ratio: 1;
        width: 100%;
        max-width: 1024px;
        overflow: hidden;
        border-radius: 12px;
    }

    &__img {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        object-fit: cover;
    }

    &__footer {
        min-height: 94px;
        padding: 16px;
        border-radius: 16px;
        row-gap: 12px;

        @screen md-max {
            margin-left: -16px;
            margin-right: -16px;
            border-radius: 12px 12px 0 0;
        }
    }

    &__saving {
        display: flex;
        flex-direction: column;
        color: var(--black);
        border-radius: 6px;
        padding: 2px 16px;
        margin-right: 32px;
        background-color: #F6DE8B;

        @screen md {
            margin-right: 12px;
        }
    }

    &__discount {
        background-color: #D1AC2A;
        color: var(--black);
    }

    &__saving-wrapper {
        max-width: 0;
        opacity: 0;
        transition: max-width 0.3s ease-in-out, opacity 0.3s ease-in-out;

        &--visible {
            max-width: calc(50% + 16px);
            opacity: 1;

            @screen md {
                max-width: 50%;
            }
        }
    }

    &__img-wrapper {
        flex: 0.9;
    }

    &__selector-wrapper {
        flex: 0.5;
    }

    @media (max-width: 1490px) {
        &__img-wrapper {
            flex: 0.9;
        }

        &__inner {
            // max-width: 940px !important;
            margin-left: 25px;
            margin-right: 25px;
        }

        &__selector-wrapper {
            flex: 0.5;
        }
    }

    @media (min-width: 1025px) and (max-width: 1140px) {
        &__img-wrapper {
            flex: 0.5 !important;
        }

        &__selector-wrapper {
            flex: 0.5;
        }
    }

    @media (max-width: 1024px) {
        padding-top: 15px;
        margin-top: 25px;
        padding-bottom: 0;

        &__inner {
            margin-top: 16px;
            margin-bottom: 16px;
            margin-left: unset;
            margin-right: unset;
        }

        &>div:first-child {
            color: white;
        }

        &__footer {
            margin-left: -16px;
            margin-right: -16px;
            border-radius: 12px 12px 0 0;
        }

        &__inner>div {
            background-color: #F6F6F6;
            flex-direction: column;
        }

        &__inner>div:first-child {
            padding: unset !important;
        }

        &__selector-wrapper {
            color: black !important;
            padding-left: 16px;
            padding-right: 16px;
            max-width: 128rem;

            &>div {
                margin: unset;
                padding-top: unset;
            }
        }

        &__imageselector {
            display: none !important;
        }

        &__img-wrapper>div {
            border-radius: unset;
        }
    }
}