.bundle-toggel-filter {
    &-wrapper {
        display: inline-block;
        border-radius: 24px;
        padding: 8px 0;
        height: 50px;
        background-color: #ffffff7c;
        display: block;

        &-btn {
            color: #000;
            display: inline-block;
            background-color: #fff;
            border-radius: 24px;
            margin: 0 10px;
            padding: 5px 17px;
            cursor: pointer;
            white-space: nowrap;
            &.active{
                color: #fff;
                background-color: #204CFE;
            }
        }
    }
    &-headline{
        color: #000;
        text-transform: uppercase;
        align-items: center;
        margin-right: 40px;
        font-weight: bold;

        @screen lg {
            font-size: 18px;
        }
    }

    &__slider {

        & &__filter-swiper-btn-next {
            position: absolute;
            right: 0;
            background: white !important;

            &::after {
                position: absolute;
                right: 0;
                content: "";
                display: block;
                background: linear-gradient(to left, #fff 20%, transparent 100%);
                height: 42px;
                width: 42px;
                top: 50%;
                transform: translate(-85%, -50%);
            }
        }

        & &__filter-swiper-btn-pre {
            position: absolute;
            left: 0;
            background: white !important;

            &::after {
                position: absolute;
                left: 0;
                content: "";
                display: block;
                background: linear-gradient(to right, #fff 20%, transparent 100%);
                height: 42px;
                width: 42px;
                top: 50%;
                transform: translate(85%, -50%);
            }
        }
    }

    &__swiper-slide {
        width: auto;
    }
}


.bundle-list{
    display: none;
    &.active{
        display: block;
    }
    .swiper-slide{
        &:first-of-type{
            padding-left: 9vw;
        }
    }
    @media only screen and (max-width:767px) {

        :global {
            .swiper,
            .swiper-wrapper,
            .swiper-slide{
                height: 100%;
            }
        }
    }

    &__card{
        width: auto;
        height: 100%;
        border-radius: 24px;
        padding-bottom: 40px;
        border: 1px solid transparent;
        margin: 0 10px;
        @media only screen and (max-width:767px) {
            padding-bottom: 0;
            margin: 0 16px;
            border: none;
            height: 100%;
        }

        &__info__hotspots{
            opacity: 0;
            transition: all 0.3s ease-out;
         &.hover{
            opacity: 1;
            transition: all 0.3s ease-out;
         }
        }

        &__info__hotspots__dot-text-title{
            white-space: nowrap;
            padding: 0 10px;
        }
        &__bottom{
            display: flex;
            justify-content: space-between;
            background-color: #FFF;
            position: absolute;
            width: 100%;
            bottom: 0;
            border-radius: 24px;
            flex-wrap: wrap;
            min-height: 115px;

            @media only screen and(max-width:767px) {
                position: relative;
                margin-top: -25px;
                z-index: 999;
            }
            p{
                color: #000;
            }
            &__accordion{
                padding: 10px;
                padding-top: 15px;
                border-bottom: 1px solid #000;
                padding-bottom: 15px;
                transition: all 0.3s ease-out;
                opacity: 0;
                height: 0;
                transition: opacity 0.5s;
                flex-wrap: wrap;
                overflow: hidden;
                cursor: pointer;
                margin-bottom: 0;

                svg{
                    transition: all 0.3s ease-out;

                }
                &__title{
                    white-space: nowrap;
                }
                &.active{
                    animation: animate 0.5s ease;
                    opacity: 1;
                    height: 100%;
                    svg{
                        transform: rotate(180deg);
                        transition: all 0.3s ease-out;
                    }
                }
                &.hover{
                    padding: 20px;
                    height: 100%;
                    animation: animateheight 0.5s ease;
                    margin-bottom: 30px;
                    opacity: 1;
                    transition: all 0.3s ease-out;
                    @media only screen and (max-width:767px) {
                        padding: 16px;
                        margin-bottom: 0;
                        border: none;
                    }
                }
                &:not(.hover) {
                    padding: 10px;
                    height: 0;
                    animation: animateheightReverse 0.5s ease;
                    margin-bottom: 0;
                    opacity: 0;
                    transition: all 0.3s ease-out;
                }
                &__content{
                    width: 100%;
                    height: 0;
                    opacity: 0;
                    transition: opacity 0.5s;
                    &.active{
                        animation: animate 0.5s ease;
                        opacity: 1;
                        height: 100%;
                        margin-top: 15px;
                    }
                }
                &.no-animation {
                    animation: none !important;
                    transition: none !important;
                }

            }
            &__bundle{
                width: 100%;
                padding: 0 30px 20px 30px;
            }
        }
        img{
            border-radius: 24px;
            width: 100%;
            height: 100%;
            object-fit: cover;
            @media only screen and (max-width:767px) {
                height: 280px;
            }
        }
        &.hover{
            border-color: #000;
        }
    }



    & &__swiper-btn-pre,
    & &__swiper-btn-next {
        top: 45%;

        @screen md-max {
            top: 20%;
        }

        @screen xxl {
            top: 50%;
        }
    }

    & &__swiper-btn-pre {
        left: 2rem;
    }

    & &__swiper-btn-next {
        right: 2rem;
    }
}

@keyframes scale {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes animate {
    from {
      height: 0;
    }

    to {
        height: 100%;
    }
}
@keyframes animateHeight {
    from {
      height: 0;
      margin-bottom: 0;
    }

    to {
        height: 100%;
        margin-bottom: 30px;
    }
}
@keyframes animateHeightReverse {
    from {
      height: 100%;
      margin-bottom: 30px;
    }

    to {
        height: 0;
        margin-bottom: 0;
    }
}
