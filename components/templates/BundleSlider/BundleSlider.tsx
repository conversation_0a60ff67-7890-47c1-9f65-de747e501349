import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react'
import s from './BundleSlider.module.scss'
import { Badge } from '@components/atoms/Badge/Badge'
import ChevronUpIcon from '@components/atoms/Icon/general/ChevronUpIcon'
import { SwiperSlide } from 'swiper/react'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import { A11y, Mousewheel, Navigation, Keyboard } from 'swiper'
import MediaWithTextOverlay from '@components/templates/MediaWithTextOverlay/MediaWithTextOverlay'
import { OverlayCloseButton } from '@components/organisms/Overlay/OverlayCloseButton'
import dynamic from 'next/dynamic'
import { Swiper as SwiperClass } from 'swiper/types'

import CardList from '@components/templates/CardList/CardList'
import { useStorePrototypeOverlayClose } from '@components/organisms/StorePrototypeCard/StorePrototypeContext'
import { useRouter } from 'next/router'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { HolidayGiftGuideBadge } from '@components/atoms/Badge/HolidayGiftGuideBadge'

import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
export interface BundleSliderProps {
    items: any
    activeIndex?: number
}

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)
export const BundleSlider: FC<BundleSliderProps> = ({ items }) => {
    const { t } = useTranslation(['common'])
    const { events } = useRouter()
    const initPanel = -1
    const [panels, setPanels] = useState(initPanel)
    const [activeIndex, setActiveIndex] = useState(-1)
    const [swiper, setSwiper] = useState<SwiperClass | null>(null)
    const { isOverlayOpen } = useStorePrototypeOverlayClose()
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    const swiperSettings = useMemo(
        () => ({
            modules: [A11y, Navigation, Keyboard, Mousewheel],
            loop: false,
            spaceBetween: 24,
            centeredSlides: false,
            slidesPerView: 1,
            simulateTouch: false,
            initialSlide: isOverlayOpen ? -1 : 0,
            mousewheel: {
                forceToAxis: true
            },
            speed: 500,
            freeMode: true,
            keyboard: true,
            allowTouchMove: true,
            breakpoints: {
                1018: {
                    slidesPerView: 2,
                    spaceBetween: 8
                },
                1300: {
                    slidesPerView: 2.5,
                    spaceBetween: 8
                }
            }
        }),
        [isOverlayOpen]
    )

    const sliderSettingsNavi = useMemo(
        () => ({
            slidesPerView: 'auto',
            centeredSlides: false,
            centerInsufficientSlides: true,
            centeredSlidesBounds: false,
            spaceBetween: 8,
            loop: false,
            allowTouchMove: true,
            simulateTouch: true,
            modules: [A11y, Navigation]
        }),
        [isOverlayOpen]
    )

    const { isMobile, isTablet } = useMobile()

    useEffect(() => {
        if (!isOverlayOpen) {
            // this is put just to reset the swiper, set timeout resets it then back to initial value
            setPanels(-1)
            setActiveIndex(-1)
        }
    }, [isOverlayOpen])

    let templateCardItems: any[] = []
    items.forEach(
        (panel: {
            children?: any[]
            meta: { contentType: string }
            templateCardList: any
        }) => {
            if (panel.meta?.contentType === 'templateCardList') {
                // Assuming children is the array you want to extract items from
                const children = panel.children || []

                children.forEach(
                    (child: { meta?: { contentType: string } }) => {
                        if (child.meta?.contentType === 'organismCard') {
                            // Push each templateCard to the new array
                            templateCardItems.push(child)
                        }
                    }
                )
            }
        }
    )

    return (
        isOverlayOpen &&
        items.map(
            (
                panel: {
                    meta: { contentType: string }
                    linkListItems: any[]
                    children: any[]
                    headline: string
                    subHeadline: string
                },
                index: number
            ) => {
                const isFirstIteration = index === 1

                const handleButtonClick = (index: number) => {
                    setActiveIndex(index)
                    templateCardItems = []
                }
                if (panel.meta?.contentType === 'organismLinkList') {
                    return (
                        <div
                            className={cn(
                                s['bundle-toggel-filter'],
                                'relative bottom-7'
                            )}
                        >
                            <div
                                className={cn(
                                    'my-16 items-center ml-6 lg2:ml-24',
                                    {
                                        'flex flex-wrap': isMobile || isTablet
                                    }
                                )}
                            >
                                <div
                                    className={
                                        (s['bundle-toggel-filter-headline'],
                                        'lg:mb-0 lg-max:mb-6 mr-8 text-black bold uppercase')
                                    }
                                >
                                    {panel.headline}
                                </div>
                                {isMobile || isTablet ? (
                                    <SwiperSlider
                                        className={
                                            s['bundle-toggel-filter__slider']
                                        }
                                        settings={sliderSettingsNavi}
                                        loop={false}
                                        navigationTheme={
                                            SwiperSliderTheme.LIGHT
                                        }
                                        buttonClassPre={
                                            s[
                                                'bundle-toggel-filter__slider__filter-swiper-btn-pre'
                                            ]
                                        }
                                        buttonClassNext={
                                            s[
                                                'bundle-toggel-filter__slider__filter-swiper-btn-next'
                                            ]
                                        }
                                    >
                                        <div
                                            className={
                                                s[
                                                    'bundle-toggel-filter-wrapper'
                                                ]
                                            }
                                        >
                                            <SwiperSlide
                                                key={0}
                                                className={
                                                    s[
                                                        'bundle-toggel-filter__swiper-slide'
                                                    ]
                                                }
                                            >
                                                <div
                                                    className={`${
                                                        s[
                                                            'bundle-toggel-filter-wrapper-btn'
                                                        ]
                                                    } ${
                                                        activeIndex === -1
                                                            ? s['active']
                                                            : ''
                                                    } whitespace-nowrap`}
                                                    onClick={() => {
                                                        setPanels(-1) // Set the panel index for 'All bundles'
                                                        handleButtonClick(-1)
                                                    }}
                                                    onKeyPress={() => {
                                                        setPanels(-1) // Set the panel index for 'All bundles'
                                                        handleButtonClick(-1)
                                                    }}
                                                    role="button"
                                                    tabIndex={0}
                                                    // eslint-disable-next-line i18next/no-literal-string
                                                >
                                                    {panel.subHeadline}
                                                </div>
                                            </SwiperSlide>
                                            {panel.linkListItems?.map(
                                                (link: any, index: any) => (
                                                    <SwiperSlide
                                                        key={index + 1}
                                                        className={
                                                            s[
                                                                'bundle-toggel-filter__swiper-slide'
                                                            ]
                                                        }
                                                    >
                                                        <div
                                                            className={`${
                                                                s[
                                                                    'bundle-toggel-filter-wrapper-btn'
                                                                ]
                                                            } ${
                                                                index ===
                                                                activeIndex
                                                                    ? s[
                                                                          'active'
                                                                      ]
                                                                    : ''
                                                            } whitespace-nowrap`}
                                                            key={index + 1}
                                                            onClick={() => {
                                                                setPanels(
                                                                    (index as number) +
                                                                        1
                                                                )
                                                                handleButtonClick(
                                                                    index
                                                                )
                                                            }}
                                                            onKeyPress={() => {
                                                                setPanels(
                                                                    (index as number) +
                                                                        1
                                                                )
                                                                handleButtonClick(
                                                                    index
                                                                )
                                                            }}
                                                            role="button"
                                                            tabIndex={0}
                                                        >
                                                            {link.linkTitle}
                                                        </div>
                                                    </SwiperSlide>
                                                )
                                            )}
                                        </div>
                                    </SwiperSlider>
                                ) : (
                                    <div
                                        className={
                                            s['bundle-toggel-filter-wrapper']
                                        }
                                    >
                                        <div
                                            className={`${
                                                s[
                                                    'bundle-toggel-filter-wrapper-btn'
                                                ]
                                            } ${
                                                activeIndex === -1
                                                    ? s['active']
                                                    : ''
                                            } whitespace-nowrap`}
                                            onClick={() => {
                                                setPanels(-1) // Set the panel index for 'All bundles'
                                                handleButtonClick(-1)
                                            }}
                                            onKeyPress={() => {
                                                setPanels(-1) // Set the panel index for 'All bundles'
                                                handleButtonClick(-1)
                                            }}
                                            role="button"
                                            tabIndex={0}
                                            // eslint-disable-next-line i18next/no-literal-string
                                        >
                                            {panel.subHeadline}
                                        </div>
                                        {panel.linkListItems?.map(
                                            (link: any, index: any) => (
                                                <div
                                                    className={`${
                                                        s[
                                                            'bundle-toggel-filter-wrapper-btn'
                                                        ]
                                                    } ${
                                                        index === activeIndex
                                                            ? s['active']
                                                            : ''
                                                    } whitespace-nowrap`}
                                                    key={index + 1}
                                                    onClick={() => {
                                                        setPanels(
                                                            (index as number) +
                                                                1
                                                        )
                                                        handleButtonClick(index)
                                                    }}
                                                    onKeyPress={() => {
                                                        setPanels(
                                                            (index as number) +
                                                                1
                                                        )
                                                        handleButtonClick(index)
                                                    }}
                                                    role="button"
                                                    tabIndex={0}
                                                >
                                                    {link.linkTitle}
                                                </div>
                                            )
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    )
                }
                if (
                    panel.children &&
                    panel.meta?.contentType === 'templateCardList'
                ) {
                    const cards =
                        panels === -1 ? templateCardItems : panel.children

                    if (index !== panels && panels !== -1) {
                        return
                    }

                    return (
                        <div
                            className={cn(
                                s['bundle-list'],
                                'flex',
                                panels === -1 && isFirstIteration
                                    ? s['active']
                                    : '',
                                panels == index ? s['active'] : ''
                            )}
                        >
                            <SwiperSlider
                                className={s['slider']}
                                settings={swiperSettings}
                                loop={false}
                                navigationTheme={SwiperSliderTheme.LIGHT}
                                parentSetSwiper={setSwiper}
                                buttonClassPre={
                                    s['bundle-list__swiper-btn-pre']
                                }
                                buttonClassNext={
                                    s['bundle-list__swiper-btn-next']
                                }
                            >
                                {cards.map((card: any, index: any) => {
                                    // eslint-disable-next-line react-hooks/rules-of-hooks
                                    const [isHovered, setIsHovered] = useState(
                                        false
                                    )
                                    // eslint-disable-next-line react-hooks/rules-of-hooks
                                    const [isOpen, setIsOpen] = useState(false)

                                    const handleMouseEnter = () => {
                                        setIsHovered(true)
                                    }

                                    const handleMouseLeave = () => {
                                        setIsHovered(false)
                                        setIsOpen(false)
                                    }

                                    const handleClick = () => {
                                        setIsOpen(!isOpen)
                                    }
                                    const image = card?.media?.file?.url
                                    const sku = card?.sku
                                    if (isMobile) {
                                        return (
                                            <SwiperSlide key={index}>
                                                <div
                                                    key={index}
                                                    className={cn(
                                                        s['bundle-list__card'],
                                                        'relative m-3',
                                                        {
                                                            [s[
                                                                'hover'
                                                            ]]: isHovered
                                                        }
                                                    )}
                                                    onMouseEnter={
                                                        handleMouseEnter
                                                    }
                                                    onMouseLeave={
                                                        handleMouseLeave
                                                    }
                                                >
                                                    {card &&
                                                        card.children &&
                                                        card.children.map(
                                                            (
                                                                child: {
                                                                    title(
                                                                        title: any
                                                                    ): string
                                                                    fontSize:
                                                                        | string
                                                                        | null
                                                                        | undefined
                                                                    textColor(
                                                                        textColor: any
                                                                    ):
                                                                        | string
                                                                        | undefined
                                                                    backgroundColor:
                                                                        | string
                                                                        | undefined
                                                                    map(
                                                                        arg0: (
                                                                            child: any,
                                                                            index: any
                                                                        ) => JSX.Element | null
                                                                    ): React.ReactNode
                                                                    meta: {
                                                                        contentType: string
                                                                    }
                                                                    hotspotColor: string
                                                                    position: {
                                                                        top: any
                                                                        left: any
                                                                    }
                                                                    bigTitle:
                                                                        | boolean
                                                                        | React.ReactChild
                                                                        | React.ReactFragment
                                                                        | React.ReactPortal
                                                                        | null
                                                                        | undefined
                                                                    children: any
                                                                    regions?: any
                                                                    badgeBackgroundColor?: string
                                                                    badgeTextColor?: string
                                                                    badgeText?: string
                                                                    textPosition: string
                                                                    sku?: string
                                                                },
                                                                i:
                                                                    | React.Key
                                                                    | null
                                                                    | undefined
                                                            ) => {
                                                                if (
                                                                    child.meta
                                                                        .contentType ===
                                                                        'organismHardwareHotspotContent' &&
                                                                    window.innerWidth >
                                                                        767
                                                                ) {
                                                                    const bgOuterRingColorOpacity =
                                                                        child.hotspotColor ===
                                                                        '#000'
                                                                            ? 'rgba(0,0,0,0.3)'
                                                                            : 'rgba(255,255,255,0.3)'
                                                                    const bgColorOpacity =
                                                                        child.hotspotColor ===
                                                                        '#000'
                                                                            ? 'rgba(0,0,0,0.85)'
                                                                            : 'rgba(255,255,255,0.85)'
                                                                    return (
                                                                        // eslint-disable-next-line react/jsx-key
                                                                        <div
                                                                            key={
                                                                                i
                                                                            }
                                                                            className={cn(
                                                                                'absolute pointer-events-auto flex justify-center items-center',
                                                                                s[
                                                                                    'bundle-list__card__info__hotspots'
                                                                                ],
                                                                                {
                                                                                    [s[
                                                                                        'hover'
                                                                                    ]]: !isHovered
                                                                                },
                                                                                {
                                                                                    [s[
                                                                                        'no-animation'
                                                                                    ]]: isAnimationStopped
                                                                                }
                                                                            )}
                                                                            style={{
                                                                                top:
                                                                                    child
                                                                                        .position
                                                                                        ?.top,
                                                                                left:
                                                                                    child
                                                                                        .position
                                                                                        ?.left
                                                                            }}
                                                                        >
                                                                            <div
                                                                                className={cn(
                                                                                    s[
                                                                                        'bundle-list__card__info__hotspots__dot'
                                                                                    ],
                                                                                    s[
                                                                                        'bundle-list__card__info__hotspots__dot__active'
                                                                                    ],
                                                                                    'absolute pointer-events-auto flex justify-center items-center'
                                                                                )}
                                                                                style={{
                                                                                    width: 24,
                                                                                    height: 24,
                                                                                    borderRadius: 99999,
                                                                                    backgroundColor: bgOuterRingColorOpacity
                                                                                }}
                                                                            >
                                                                                <div
                                                                                    className={cn(
                                                                                        s[
                                                                                            'bundle-list__card__info__hotspots__dot__inner'
                                                                                        ]
                                                                                    )}
                                                                                    style={{
                                                                                        width: 16,
                                                                                        height: 16,
                                                                                        borderRadius: 99999,
                                                                                        backgroundColor: bgColorOpacity
                                                                                    }}
                                                                                >
                                                                                    &nbsp;
                                                                                </div>
                                                                            </div>
                                                                            <div
                                                                                className={cn(
                                                                                    s[
                                                                                        'bundle-list__card__info__hotspots__dot-text'
                                                                                    ],
                                                                                    'absolute z-50 h-auto bg-white'
                                                                                )}
                                                                                style={{
                                                                                    [child.textPosition ===
                                                                                    'left'
                                                                                        ? 'right'
                                                                                        : 'left']: 20,
                                                                                    borderRadius: 4
                                                                                }}
                                                                            >
                                                                                <p
                                                                                    className={cn(
                                                                                        s[
                                                                                            'bundle-list__card__info__hotspots__dot-text-title'
                                                                                        ],
                                                                                        'text-black'
                                                                                    )}
                                                                                >
                                                                                    {
                                                                                        child.bigTitle
                                                                                    }
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                } else if (
                                                                    child.meta
                                                                        .contentType ===
                                                                    'templateMediaWithTextOverlay'
                                                                ) {
                                                                    const [
                                                                        overlay,
                                                                        setOverlay
                                                                        // eslint-disable-next-line react-hooks/rules-of-hooks
                                                                    ] = useState(
                                                                        false
                                                                    )
                                                                    return (
                                                                        <div
                                                                            className={cn(
                                                                                s[
                                                                                    'bundle-list__card__overlay'
                                                                                ],
                                                                                'fixed top-0 w-full',
                                                                                {
                                                                                    [s[
                                                                                        'active'
                                                                                    ]]: overlay
                                                                                }
                                                                            )}
                                                                        >
                                                                            <MediaWithTextOverlay
                                                                                content={{
                                                                                    children:
                                                                                        child.children
                                                                                }}
                                                                            />
                                                                            <OverlayCloseButton
                                                                                className={cn(
                                                                                    s[
                                                                                        'bundle-list__card__overlay__close'
                                                                                    ],
                                                                                    'absolute top-8 right-8 bg-whote',
                                                                                    {
                                                                                        [s[
                                                                                            'active'
                                                                                        ]]: overlay
                                                                                    }
                                                                                )}
                                                                                onClick={() => {
                                                                                    setOverlay(
                                                                                        !overlay
                                                                                    )
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    )
                                                                } else if (
                                                                    child.meta
                                                                        .contentType ===
                                                                    'moleculeBanner'
                                                                ) {
                                                                    const banner = (child as unknown) as BannerProps
                                                                    const isHGG =
                                                                        banner
                                                                            ?.customOptions
                                                                            ?.productType ===
                                                                        'hgg'
                                                                    return (
                                                                        <div
                                                                            className={cn(
                                                                                'flex absolute',
                                                                                {
                                                                                    'top-32px left-32px': !isHGG,
                                                                                    'top-0 left-0': isHGG
                                                                                }
                                                                            )}
                                                                        >
                                                                            <div
                                                                                key={
                                                                                    i
                                                                                }
                                                                            >
                                                                                <Badge
                                                                                    className={cn(
                                                                                        banner.backgroundColor,
                                                                                        banner?.badgeBackgroundColor,
                                                                                        banner?.badgeTextColor
                                                                                    )}
                                                                                    textClassName={cn(
                                                                                        banner.textColor
                                                                                    )}
                                                                                    regions={
                                                                                        banner?.regions
                                                                                    }
                                                                                    sku={
                                                                                        sku
                                                                                    }
                                                                                    hasDiscount
                                                                                    /* eslint-disable-next-line i18next/no-literal-string */
                                                                                    size="medium"
                                                                                    theme={
                                                                                        isHGG
                                                                                            ? 'hgg'
                                                                                            : 'default'
                                                                                    }
                                                                                    badgeTextOverwrite={
                                                                                        banner.headline
                                                                                    }
                                                                                    badgePercentageOverwrite={
                                                                                        card?.dealsProductDiscount
                                                                                    }
                                                                                    backgroundHGG={
                                                                                        banner
                                                                                            .customOptions
                                                                                            ?.backgroundColor as string
                                                                                    }
                                                                                >
                                                                                    <h5>
                                                                                        {
                                                                                            banner?.badgeText
                                                                                        }
                                                                                    </h5>
                                                                                </Badge>
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                } else if (
                                                                    child.meta
                                                                        .contentType ===
                                                                    'templateCardList'
                                                                ) {
                                                                    return null
                                                                }
                                                            }
                                                        )}
                                                    <img alt="#" src={image} />
                                                    <div
                                                        className={cn(
                                                            s[
                                                                'bundle-list__card__bottom'
                                                            ]
                                                        )}
                                                    >
                                                        <div
                                                            className={cn(
                                                                s[
                                                                    'bundle-list__card__bottom__accordion'
                                                                ],
                                                                s['hover'],
                                                                s['active'],
                                                                'flex w-full text-black font-bold'
                                                            )}
                                                            role="button"
                                                            tabIndex={0}
                                                        >
                                                            <div>
                                                                <div className="flex items-center text-center">
                                                                    <CardList
                                                                        content={Object.assign(
                                                                            {},
                                                                            card,
                                                                            {
                                                                                variant:
                                                                                    'bundle-product-text'
                                                                            }
                                                                        )}
                                                                    />
                                                                </div>
                                                                {card.children && (
                                                                    <div
                                                                        className={cn(
                                                                            s[
                                                                                'bundle-list__card__bottom__accordion__content'
                                                                            ],
                                                                            s[
                                                                                'active'
                                                                            ]
                                                                        )}
                                                                    >
                                                                        <div
                                                                            className={cn(
                                                                                s[
                                                                                    'bundle-list__card__bottom__accordion__content__line'
                                                                                ]
                                                                            )}
                                                                        >
                                                                            {card.children &&
                                                                                card.children.map(
                                                                                    (
                                                                                        child: any,
                                                                                        i:
                                                                                            | React.Key
                                                                                            | null
                                                                                            | undefined
                                                                                    ) => (
                                                                                        <CardList
                                                                                            key={
                                                                                                i
                                                                                            }
                                                                                            content={Object.assign(
                                                                                                {},
                                                                                                child,
                                                                                                {
                                                                                                    variant:
                                                                                                        'bundle-products'
                                                                                                }
                                                                                            )}
                                                                                        />
                                                                                    )
                                                                                )}
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                        <div
                                                            className={cn(
                                                                s[
                                                                    'bundle-list__card__bottom__bundle'
                                                                ],
                                                                'flex text-black font-bold'
                                                            )}
                                                        >
                                                            <CardList
                                                                content={Object.assign(
                                                                    {},
                                                                    card,
                                                                    {
                                                                        variant:
                                                                            'bundle-product'
                                                                    }
                                                                )}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </SwiperSlide>
                                        )
                                    } else {
                                        return (
                                            <SwiperSlide key={index}>
                                                <div
                                                    key={index}
                                                    className={cn(
                                                        s['bundle-list__card'],
                                                        'relative m-3',
                                                        {
                                                            [s[
                                                                'hover'
                                                            ]]: isHovered
                                                        }
                                                    )}
                                                    onMouseEnter={
                                                        handleMouseEnter
                                                    }
                                                    onMouseLeave={
                                                        handleMouseLeave
                                                    }
                                                >
                                                    {card &&
                                                        card.children &&
                                                        card.children.map(
                                                            (
                                                                child: {
                                                                    title(
                                                                        title: any
                                                                    ): string
                                                                    fontSize:
                                                                        | string
                                                                        | null
                                                                        | undefined
                                                                    textColor(
                                                                        textColor: any
                                                                    ):
                                                                        | string
                                                                        | undefined
                                                                    backgroundColor:
                                                                        | string
                                                                        | undefined
                                                                    map(
                                                                        arg0: (
                                                                            child: any,
                                                                            index: any
                                                                        ) => JSX.Element | null
                                                                    ): React.ReactNode
                                                                    meta: {
                                                                        contentType: string
                                                                    }
                                                                    hotspotColor: string
                                                                    position: {
                                                                        top: any
                                                                        left: any
                                                                    }
                                                                    bigTitle:
                                                                        | boolean
                                                                        | React.ReactChild
                                                                        | React.ReactFragment
                                                                        | React.ReactPortal
                                                                        | null
                                                                        | undefined
                                                                    children: any
                                                                    textPosition: string
                                                                    badgeBackgroundColor?: string
                                                                    badgeTextColor?: string
                                                                    badgeText?: string
                                                                    regions?: any
                                                                    sku?: string
                                                                },
                                                                i:
                                                                    | React.Key
                                                                    | null
                                                                    | undefined
                                                            ) => {
                                                                if (
                                                                    child.meta
                                                                        .contentType ===
                                                                    'organismHardwareHotspotContent'
                                                                ) {
                                                                    const bgOuterRingColorOpacity =
                                                                        child.hotspotColor ===
                                                                        '#000'
                                                                            ? 'rgba(0,0,0,0.3)'
                                                                            : 'rgba(255,255,255,0.3)'
                                                                    const bgColorOpacity =
                                                                        child.hotspotColor ===
                                                                        '#000'
                                                                            ? 'rgba(0,0,0,0.85)'
                                                                            : 'rgba(255,255,255,0.85)'
                                                                    return (
                                                                        // eslint-disable-next-line react/jsx-key
                                                                        <div
                                                                            key={
                                                                                i
                                                                            }
                                                                            className={cn(
                                                                                'absolute pointer-events-auto flex justify-center items-center',
                                                                                s[
                                                                                    'bundle-list__card__info__hotspots'
                                                                                ],
                                                                                {
                                                                                    [s[
                                                                                        'hover'
                                                                                    ]]: !isHovered
                                                                                }
                                                                            )}
                                                                            style={{
                                                                                top:
                                                                                    child
                                                                                        .position
                                                                                        ?.top,
                                                                                left:
                                                                                    child
                                                                                        .position
                                                                                        ?.left
                                                                            }}
                                                                        >
                                                                            <div
                                                                                className={cn(
                                                                                    s[
                                                                                        'bundle-list__card__info__hotspots__dot'
                                                                                    ],
                                                                                    s[
                                                                                        'bundle-list__card__info__hotspots__dot__active'
                                                                                    ],
                                                                                    'absolute pointer-events-auto flex justify-center items-center'
                                                                                )}
                                                                                style={{
                                                                                    width: 24,
                                                                                    height: 24,
                                                                                    borderRadius: 99999,
                                                                                    backgroundColor: bgOuterRingColorOpacity
                                                                                }}
                                                                            >
                                                                                <div
                                                                                    className={cn(
                                                                                        s[
                                                                                            'bundle-list__card__info__hotspots__dot__inner'
                                                                                        ]
                                                                                    )}
                                                                                    style={{
                                                                                        width: 16,
                                                                                        height: 16,
                                                                                        borderRadius: 99999,
                                                                                        backgroundColor: bgColorOpacity
                                                                                    }}
                                                                                >
                                                                                    &nbsp;
                                                                                </div>
                                                                            </div>
                                                                            <div
                                                                                className={cn(
                                                                                    s[
                                                                                        'bundle-list__card__info__hotspots__dot-text'
                                                                                    ],
                                                                                    'absolute z-50 h-auto bg-white'
                                                                                )}
                                                                                style={{
                                                                                    [child.textPosition ===
                                                                                    'left'
                                                                                        ? 'right'
                                                                                        : 'left']: 20,
                                                                                    borderRadius: 4
                                                                                }}
                                                                            >
                                                                                <p
                                                                                    className={cn(
                                                                                        s[
                                                                                            'bundle-list__card__info__hotspots__dot-text-title'
                                                                                        ],
                                                                                        'text-black'
                                                                                    )}
                                                                                >
                                                                                    {
                                                                                        child.bigTitle
                                                                                    }
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                } else if (
                                                                    child.meta
                                                                        .contentType ===
                                                                    'templateMediaWithTextOverlay'
                                                                ) {
                                                                    const [
                                                                        overlay,
                                                                        setOverlay
                                                                        // eslint-disable-next-line react-hooks/rules-of-hooks
                                                                    ] = useState(
                                                                        false
                                                                    )
                                                                    return (
                                                                        <div
                                                                            className={cn(
                                                                                s[
                                                                                    'bundle-list__card__overlay'
                                                                                ],
                                                                                'fixed top-0 w-full',
                                                                                {
                                                                                    [s[
                                                                                        'active'
                                                                                    ]]: overlay
                                                                                }
                                                                            )}
                                                                        >
                                                                            <MediaWithTextOverlay
                                                                                content={{
                                                                                    children:
                                                                                        child.children
                                                                                }}
                                                                            />
                                                                            <OverlayCloseButton
                                                                                className={cn(
                                                                                    s[
                                                                                        'bundle-list__card__overlay__close'
                                                                                    ],
                                                                                    'absolute top-8 right-8 bg-whote',
                                                                                    {
                                                                                        [s[
                                                                                            'active'
                                                                                        ]]: overlay
                                                                                    }
                                                                                )}
                                                                                onClick={() =>
                                                                                    setOverlay(
                                                                                        !overlay
                                                                                    )
                                                                                }
                                                                            />
                                                                        </div>
                                                                    )
                                                                } else if (
                                                                    child.meta
                                                                        .contentType ===
                                                                    'moleculeBanner'
                                                                ) {
                                                                    const banner = (child as unknown) as BannerProps
                                                                    const isHGG =
                                                                        banner
                                                                            .customOptions
                                                                            ?.productType ===
                                                                        'hgg'
                                                                    return (
                                                                        <div
                                                                            className={cn(
                                                                                'flex absolute',
                                                                                {
                                                                                    'top-32px left-32px': !isHGG,
                                                                                    'top-0 left-0': isHGG
                                                                                }
                                                                            )}
                                                                        >
                                                                            <div
                                                                                key={
                                                                                    i
                                                                                }
                                                                            >
                                                                                <Badge
                                                                                    className={cn(
                                                                                        banner.backgroundColor,
                                                                                        banner?.badgeBackgroundColor,
                                                                                        banner?.badgeTextColor
                                                                                    )}
                                                                                    textClassName={cn(
                                                                                        banner.textColor
                                                                                    )}
                                                                                    /* eslint-disable-next-line i18next/no-literal-string */
                                                                                    size="medium"
                                                                                    regions={
                                                                                        banner.regions
                                                                                    }
                                                                                    sku={
                                                                                        sku
                                                                                    }
                                                                                    hasDiscount
                                                                                    theme={
                                                                                        isHGG
                                                                                            ? 'hgg'
                                                                                            : 'default'
                                                                                    }
                                                                                    badgeTextOverwrite={
                                                                                        banner.headline
                                                                                    }
                                                                                    badgePercentageOverwrite={
                                                                                        card?.dealsProductDiscount
                                                                                    }
                                                                                    backgroundHGG={
                                                                                        banner
                                                                                            .customOptions
                                                                                            ?.backgroundColor as string
                                                                                    }
                                                                                >
                                                                                    <h5>
                                                                                        {banner.headline ??
                                                                                            banner.badgeText}
                                                                                    </h5>
                                                                                </Badge>
                                                                            </div>
                                                                        </div>
                                                                    )
                                                                } else if (
                                                                    child.meta
                                                                        .contentType ===
                                                                    'templateCardList'
                                                                ) {
                                                                    return null
                                                                }
                                                            }
                                                        )}
                                                    <img alt="#" src={image} />
                                                    <div
                                                        className={cn(
                                                            s[
                                                                'bundle-list__card__bottom'
                                                            ]
                                                        )}
                                                    >
                                                        {card.children && (
                                                            <div
                                                                className={cn(
                                                                    s[
                                                                        'bundle-list__card__bottom__accordion'
                                                                    ],
                                                                    'flex w-full text-black font-bold',
                                                                    {
                                                                        [s[
                                                                            'hover'
                                                                        ]]: isHovered
                                                                    },
                                                                    {
                                                                        [s[
                                                                            'active'
                                                                        ]]: isHovered
                                                                    }
                                                                )}
                                                                onClick={() =>
                                                                    handleClick()
                                                                }
                                                                onKeyPress={() =>
                                                                    handleClick()
                                                                }
                                                                role="button"
                                                                tabIndex={0}
                                                            >
                                                                <div className="w-full">
                                                                    <div className="flex items-center">
                                                                        <ChevronUpIcon />
                                                                        <div
                                                                            className={cn(
                                                                                s[
                                                                                    'bundle-list__card__bottom__accordion__title'
                                                                                ],
                                                                                'uppercase'
                                                                            )}
                                                                        >
                                                                            {t(
                                                                                'in_the_bundle'
                                                                            )}
                                                                        </div>
                                                                    </div>
                                                                    <div
                                                                        className={cn(
                                                                            s[
                                                                                'bundle-list__card__bottom__accordion__content'
                                                                            ],
                                                                            {
                                                                                [s[
                                                                                    'active'
                                                                                ]]: isHovered
                                                                            }
                                                                        )}
                                                                    >
                                                                        <ul
                                                                            className={cn(
                                                                                s[
                                                                                    'bundle-list__card__bottom__accordion__content__line'
                                                                                ]
                                                                            )}
                                                                        >
                                                                            {card.children &&
                                                                                card.children.map(
                                                                                    (
                                                                                        child: any,
                                                                                        i:
                                                                                            | React.Key
                                                                                            | null
                                                                                            | undefined
                                                                                    ) => (
                                                                                        <CardList
                                                                                            key={
                                                                                                i
                                                                                            }
                                                                                            content={Object.assign(
                                                                                                {},
                                                                                                child,
                                                                                                {
                                                                                                    variant:
                                                                                                        'bundle-products'
                                                                                                }
                                                                                            )}
                                                                                        />
                                                                                    )
                                                                                )}
                                                                        </ul>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        )}
                                                        <div
                                                            className={cn(
                                                                s[
                                                                    'bundle-list__card__bottom__bundle'
                                                                ],
                                                                'flex text-black font-bold'
                                                            )}
                                                        >
                                                            <CardList
                                                                content={Object.assign(
                                                                    {},
                                                                    card,
                                                                    {
                                                                        variant:
                                                                            'bundle-product'
                                                                    }
                                                                )}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </SwiperSlide>
                                        )
                                    }
                                })}
                            </SwiperSlider>
                        </div>
                    )
                }

                return null
            }
        )
    )
}

export default BundleSlider
