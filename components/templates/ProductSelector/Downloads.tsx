import React, { FC } from 'react'
import { ProductCardList } from '@components/organisms/ProductCardList/ProductCardList'
import { ProductCardProps } from '@components/organisms/ProductCard/ProductCard'
import { useContentJson } from '@pylot-data/hooks/contentful/use-content-json'
import ProductCardSkeleton from '@components/organisms/ProductCard/ProductCardSkeleton'

const DOWNLOADS_CONTENT_IDENTIFIER = ['downloads']
const DOWNLOADS_CONTENT_TYPE = 'downloads'

export type DownloadsProps = {
    hasWindowsList: boolean
    hasMacList: boolean
    currentMac: ProductCardProps[]
    currentWindows: ProductCardProps[]
}

export type DownloadCardData = {
    downloadURL: string
    fileURL: string
    enabled: boolean
    minimumOS: string
    version: string
}

export interface DownloadDataContent {
    [key: string]: DownloadCardData
}

interface DownloadsData {
    identifier: string
    title: string
    downloadData: DownloadDataContent
}

export const Downloads: FC<DownloadsProps> = (props) => {
    const { hasWindowsList, hasMacList, currentMac, currentWindows } = props

    const { data, isValidating } = useContentJson<DownloadsData>(
        {
            identifier: DOWNLOADS_CONTENT_IDENTIFIER,
            contentType: DOWNLOADS_CONTENT_TYPE,
            options: {
                level: 1
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true
        }
    )
    if (isValidating || !data || !data.length) {
        return <ProductCardSkeleton numberOfCards={7} />
    }
    const { downloadData } = data[0].parsedEntries
    return (
        <div>
            {hasWindowsList && (
                <div>
                    <ProductCardList
                        cards={currentWindows}
                        downloadData={downloadData}
                        variant="win"
                    />
                </div>
            )}
            {hasMacList && (
                <div>
                    <ProductCardList
                        cards={currentMac}
                        downloadData={downloadData}
                        variant="mac"
                    />
                </div>
            )}
        </div>
    )
}
