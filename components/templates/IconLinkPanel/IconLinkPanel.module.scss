$icon-base-gap: 1.6rem;
$icon-base-size: 8.4rem;

$icon-number-breakpoint-1: 33rem;
$icon-number-breakpoint-2: 58rem;
$icon-number-breakpoint-3: 88rem;
$icon-number-breakpoint-default: 113rem;

.icon {
    width: $icon-base-size;
    height: $icon-base-size;
}

.icon-container {
    max-width: $icon-number-breakpoint-default;

    &-1,
    &-2,
    &-3 {
        max-width: $icon-number-breakpoint-1;
    }
    &-4,
    &-5 {
        max-width: $icon-number-breakpoint-2;
    }
    &-6,
    &-7,
    &-8,
    &-9 {
        max-width: $icon-number-breakpoint-3;
    }

    @screen md-max {
        @apply flex-wrap justify-center;
        gap: $icon-base-gap;
        max-width: $icon-base-size * 3 + $icon-base-gap * 2;

        &-4,
        &-7,
        &-10, {
            .icon-wrapper-helper:nth-child(2) {
                @apply mr-px;
            }
        }
    }
}
