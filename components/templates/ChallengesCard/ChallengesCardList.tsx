import React, { FC } from 'react'
import s from './ChallengesCardList.module.scss'

import { CardProps } from '../../templates/CardList/CardList'
import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import unescape from 'lodash.unescape'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { SectionBgColor } from '@components/templates/Section/Section'
import Video from '@components/molecules/Video/Video'
import { getCloudinaryImageAspectRatio } from '@config/hooks/useGetImageAspectRatio'
import ElgatoImage from '@components/common/ElgatoImage'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'

interface ChallengesCardListProps {
    cards: CardProps[]
    backgroundColor: SectionBgColor
    id?: string
}

export const ChallengesCardList: FC<ChallengesCardListProps> = ({
    cards,
    backgroundColor,
    id
}) => {
    return (
        <div className={backgroundColor} id={id}>
            <Container size={ContainerSize.MEDIUM}>
                <div
                    className={cn(
                        'text-white py-16px md:py-32 flex flex-col justify-center items-center w-full gap-16px'
                    )}
                >
                    {cards.map((card, i) => {
                        const aspectRatio = getCloudinaryImageAspectRatio({
                            cloudinaryImageOrVideo: card.cloudinaryMedia?.[0],
                            cloudinaryPosterImage:
                                card.cloudinaryPosterImage?.[0]
                        })
                        const aspectRatioStyle = aspectRatio
                            ? { aspectRatio: `${aspectRatio}` }
                            : {}
                        const isVideo =
                            card.cloudinaryMedia?.[0]?.resource_type == 'video'

                        const isMobileVideo =
                            card.cloudinaryMobileMedia?.[0]?.resource_type ==
                            'video'

                        return (
                            <div
                                key={`challenges-card-${i}`}
                                className={cn(
                                    'bg-dark-grey-4 rounded-xl',
                                    s['challenges-card']
                                )}
                            >
                                <h6 className="mb-2">
                                    {card.textPanel?.headline}
                                </h6>
                                <div className="flex flex-row md-max:flex-col gap-24px md:gap-32">
                                    <div className="flex flex-col flex-1 gap-16px md:gap-16">
                                        <h4>{card.textPanel?.subheader}</h4>
                                        {card.cloudinaryMedia?.[0]
                                            ?.secure_url &&
                                            !isVideo && (
                                                <div
                                                    className={cn(
                                                        s[
                                                            'challenges-card__image'
                                                        ],
                                                        {
                                                            'hidden md:block':
                                                                card.cloudinaryMobileMedia
                                                        }
                                                    )}
                                                    style={aspectRatioStyle}
                                                >
                                                    <ElgatoImage
                                                        src={
                                                            card
                                                                .cloudinaryMedia?.[0]
                                                                ?.secure_url
                                                        }
                                                        alt={
                                                            card
                                                                .cloudinaryMedia?.[0]
                                                                ?.context
                                                                ?.custom?.alt
                                                        }
                                                        layout="fill"
                                                        objectFit="cover"
                                                    />
                                                </div>
                                            )}
                                        {isVideo && card.cloudinaryMedia && (
                                            <ElgatoVideo
                                                className={cn(
                                                    'rounded-xl overflow-hidden',
                                                    {
                                                        'hidden md:block':
                                                            card
                                                                .cloudinaryMedia?.[0]
                                                                ?.secure_url
                                                    }
                                                )}
                                                secure_url={
                                                    card.cloudinaryMedia?.[0]
                                                        ?.secure_url
                                                }
                                                options={{
                                                    autoPlay: true,
                                                    preload: 'none',
                                                    muted: true,
                                                    loop: true
                                                }}
                                                fallbackImgUrl={
                                                    card.posterImage?.file.url
                                                }
                                                videoDescription={
                                                    card?.customOptions
                                                        ?.videoDescription
                                                }
                                            />
                                        )}
                                        {card.cloudinaryMobileMedia?.[0]
                                            ?.secure_url &&
                                            !isMobileVideo && (
                                                <div
                                                    className={cn(
                                                        s[
                                                            'challenges-card__image'
                                                        ],
                                                        'md:hidden'
                                                    )}
                                                    style={aspectRatioStyle}
                                                >
                                                    <ElgatoImage
                                                        src={
                                                            card
                                                                .cloudinaryMobileMedia?.[0]
                                                                ?.secure_url
                                                        }
                                                        alt={
                                                            card
                                                                .cloudinaryMobileMedia?.[0]
                                                                .context?.custom
                                                                ?.alt
                                                        }
                                                        layout="fill"
                                                        objectFit="cover"
                                                    />
                                                </div>
                                            )}
                                        {isMobileVideo &&
                                            card.cloudinaryMobileMedia && (
                                                <ElgatoVideo
                                                    className={cn(
                                                        'rounded-xl overflow-hidden md:hidden'
                                                    )}
                                                    secure_url={
                                                        card
                                                            .cloudinaryMobileMedia?.[0]
                                                            ?.secure_url
                                                    }
                                                    options={{
                                                        autoPlay: true,
                                                        preload: 'none',
                                                        muted: true,
                                                        loop: true
                                                    }}
                                                    fallbackImgUrl={
                                                        card.posterImage?.file
                                                            .url
                                                    }
                                                    videoDescription={
                                                        card?.customOptions
                                                            ?.videoDescription
                                                    }
                                                />
                                            )}
                                    </div>
                                    <div
                                        className={cn(
                                            'body-copy flex-1 rich-text whitespace-pre-line',
                                            s['challenges-card__text']
                                        )}
                                        dangerouslySetInnerHTML={{
                                            __html: unescape(
                                                card.textPanel?.bodyCopy?.replace(
                                                    /\n/g,
                                                    `</br>`
                                                )
                                            )
                                        }}
                                    />
                                </div>
                            </div>
                        )
                    })}
                </div>
            </Container>
        </div>
    )
}

export default ChallengesCardList
