import AppleLogo from '@components/atoms/Logo/platform/AppleLogo'
import Android from '@components/icons/Android'
import { BREAKPOINT_MOBILE_MAX } from '@components/layouts/MainLayout/breakpoints'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { ButtonProps } from '@components/molecules/Button/Button'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils/pushToDataLayer'
import cn from 'classnames'
import dynamic from 'next/dynamic'
import { FC, useCallback, useEffect, useState } from 'react'
import s from './QRCodeBanner.module.scss'

const QRCode = dynamic(
    () => import('@components/molecules/QRCodeBanner/QRCode'),
    { ssr: false }
)

export interface QRCodeBannerProps extends BannerProps {
    type?: 'app-store' | 'google-play-store'
}

export const QRCodeBanner: FC<QRCodeBannerProps> = (props) => {
    const { className, headline, cloudinaryImage, type, link } = props
    const [state, setState] = useState<'hidden' | 'open' | 'closed'>('hidden')

    useEffect(() => {
        if (typeof window !== undefined) {
            if (window.innerWidth <= BREAKPOINT_MOBILE_MAX) {
                setState((prevState) => {
                    if (prevState === 'hidden' || prevState === 'open') {
                        return 'closed'
                    }
                    return prevState
                })
            } else {
                setTimeout(() => {
                    setState('open')
                }, 300)
            }
        }
    }, [])

    const handleClick = () => {
        toggleOpen()
    }
    const toggleOpen = useCallback(() => {
        if (
            window.innerWidth <= BREAKPOINT_MOBILE_MAX &&
            link &&
            link.linkUrl
        ) {
            window.open(
                link.linkUrl,
                link.newTab ? '_blank' : '_self',
                buttonConfig.dataLayer as string | undefined
            )
        } else {
            setState((prev) => {
                if (prev === 'open') {
                    return 'closed'
                }
                if (prev === 'closed') {
                    return 'open'
                }
                return prev
            })
        }
    }, [link])
    const dataLayer = link?.eventTracking
    interface Data {
        key: string
        value: string
    }

    const trackingEvent:
        | { [key: string]: string; event?: any }
        | undefined = Array.isArray(dataLayer)
        ? dataLayer.reduce(
              (
                  result: { [key: string]: string; event?: any },
                  eventData: any
              ) => {
                  if (
                      eventData &&
                      eventData.event &&
                      Array.isArray(eventData.eventValue) // Check if eventValue is an array
                  ) {
                      eventData.eventValue.forEach((data: Data) => {
                          result[data.key] = data.value
                      })

                      result.event = eventData.event
                  }
                  return result
              },
              {} as { [key: string]: string; event?: any }
          )
        : typeof dataLayer === 'object' && dataLayer !== null
        ? {
              event: (dataLayer as any).event,
              ...(Array.isArray((dataLayer as any).eventValue)
                  ? (dataLayer as any).eventValue.reduce(
                        (acc: { [x: string]: string }, data: Data) => {
                            acc[data.key] = data.value
                            return acc
                        },
                        {}
                    )
                  : {})
          }
        : undefined
    const trackEventClick = () => {
        if (
            trackingEvent !== null &&
            trackingEvent !== undefined &&
            dataLayer?.length !== 0
        ) {
            pushToDataLayer(trackingEvent)
        }
        if (handleClick) {
            handleClick()
        }
    }
    const buttonConfig: ButtonProps = {
        dataLayer: dataLayer
    }
    return (
        <button
            className={cn(
                'gtm-qr-code-banner',
                s['qr-code-banner'],
                {
                    [s['qr-code-banner--open']]: state === 'open',
                    hidden: state === 'hidden'
                },
                className
            )}
            onClick={() => {
                trackEventClick()
            }}
            onKeyPress={toggleOpen}
        >
            {cloudinaryImage?.[0] && (
                <QRCode
                    image={cloudinaryImage[0]}
                    toggled={state === 'open'}
                    initialAnimateHeight={state === 'open' ? 'auto' : 0}
                />
            )}
            <div className="flex flex-row md:flex-col justify-center items-center w-full gap-8px">
                {type === 'app-store' && (
                    <div className="flex gap-4px">
                        <AppleLogo className="flex-none" />
                        <Android />
                    </div>
                )}
                <span className="button-text flex-none">{headline}</span>
            </div>
        </button>
    )
}
