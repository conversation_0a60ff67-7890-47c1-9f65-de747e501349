.pricing-gallery {
    // lg:grid-cols-4 md:grid-cols-2
    grid-template: repeat(2, minmax(300px, 1fr));
    @screen xl {
        grid-template: repeat(4, minmax(300px, 1fr));
    }
    &__card {
        max-width: 360px;
    }

    &__badge {
        // transform: translate(35%, 100%) rotate(45deg);
        transform: translate(28%, 81%) rotate(30deg);

        @screen lg {
            transform: translate(28%, 100%) rotate(30deg);
            // transform: translate(30%, 150%) rotate(45deg);
        }
    }
}
