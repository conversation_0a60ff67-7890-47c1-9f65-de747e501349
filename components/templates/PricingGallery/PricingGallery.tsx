import { FC, useEffect, useState } from 'react'
import { GalleryProps } from '@components/templates/Gallery/Gallery'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { GalleryCardProps } from '@components/organisms/GalleryCard/GalleryCard'
import cn from 'classnames'
import s from './PricingGallery.module.scss'
import { PricingGalleryCard } from '@components/templates/PricingGallery/PricingGalleryCard'
import { V1ElgatoAPIButtonsResponse } from '@components/templates/PricingGallery/pricing'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { Splide, SplideSlide, SplideTrack } from '@splidejs/react-splide'
import { decode } from 'he'
import { CardProps } from '@components/templates/CardList/CardList'

export const PricingGallery: FC<GalleryProps> = ({
    items,
    textPanel,
    bgColor,
    id,
    additionalOptions
}) => {
    const { isMobile } = useMobile()
    const [activeSlide, setActiveSlide] = useState(0)
    const [bodyHeights, setBodyHeights] = useState<number[]>([])
    const [
        apiResponse,
        setApiResponse
    ] = useState<V1ElgatoAPIButtonsResponse | null>(null)

    useEffect(() => {
        if (!additionalOptions?.apiBaseURL) {
            return
        }

        fetch(additionalOptions.apiBaseURL as string).then(async (response) => {
            if (response.status === 200) {
                const json = (await response.json()) as V1ElgatoAPIButtonsResponse
                setApiResponse(json)
            }
        })
    }, [additionalOptions?.apiBaseURL])

    useEffect(() => {
        setBodyHeights([])
    }, [items, isMobile])

    const { disclaimerText, ...restTextPanel } = textPanel || {}

    const maxBodyHeight = Math.max(...bodyHeights)

    return (
        <div
            id={id}
            className={cn(s['pricing-gallery'], 'flex flex-col', {
                'bg-primitive-gray-130': bgColor === 'bg-black'
            })}
        >
            {textPanel && (
                <TextPanel
                    content={{
                        ...restTextPanel,
                        textColor: bgColor === 'bg-black' ? 'light' : 'dark'
                    }}
                />
            )}
            {!isMobile ? (
                <div
                    className={cn(
                        s['pricing-gallery__card-container'],
                        'grid grid-cols-2 xl:grid-cols-4 grid-rows-2 xl:grid-rows-1 gap-40px pb-16 justify-items-center justify-center mx-auto px-16px'
                    )}
                >
                    {items?.map((item: CardProps, index: number) => {
                        if (!item.textPanel) {
                            return null
                        }

                        const tier = apiResponse?.tiers.find(
                            (tier) => tier.name === item.id
                        )

                        const active = index === items.length - 1

                        return (
                            <PricingGalleryCard
                                {...item.textPanel}
                                key={index}
                                active={active}
                                tier={tier}
                                bodyHeight={maxBodyHeight}
                                onMount={(height: number) => {
                                    if (!bodyHeights.includes(height)) {
                                        setBodyHeights((prev) => [
                                            ...prev,
                                            height
                                        ])
                                    }
                                }}
                            />
                        )
                    })}
                </div>
            ) : (
                <Splide
                    options={{
                        arrows: false,
                        focus: 'center',
                        autoWidth: true,
                        gap: '16px',
                        pagination: true,
                        perMove: 1,
                        perPage: 1,
                        start: 0,
                        trimSpace: false
                    }}
                    hasTrack={false}
                    onMoved={(splide) => {
                        setActiveSlide(splide.index)
                    }}
                >
                    <SplideTrack>
                        {items?.map((item: CardProps, index: number) => {
                            if (!item.textPanel) {
                                return null
                            }

                            const active = index === items.length - 1

                            return (
                                <SplideSlide key={index}>
                                    <PricingGalleryCard
                                        {...item.textPanel}
                                        active={active}
                                        tier={apiResponse?.tiers[index]}
                                        bodyHeight={maxBodyHeight}
                                        onMount={(height: number) => {
                                            if (!bodyHeights.includes(height)) {
                                                setBodyHeights((prev) => [
                                                    ...prev,
                                                    height
                                                ])
                                            }
                                        }}
                                    />
                                </SplideSlide>
                            )
                        })}
                    </SplideTrack>
                    <div className="flex justify-center items-center my-16px gap-4">
                        {items?.map((item: CardProps, index: number) => {
                            return (
                                <div
                                    key={item.id}
                                    className={cn('h-4 w-4 rounded-sm', {
                                        'bg-white': activeSlide === index
                                    })}
                                    style={{
                                        border: '1px solid #E5E5E5'
                                    }}
                                />
                            )
                        })}
                    </div>
                </Splide>
            )}
            {disclaimerText && (
                <div
                    className={cn(
                        'small-copy italic text-center pb-32 px-16px',
                        {
                            'text-white': bgColor === 'bg-black'
                        }
                    )}
                    dangerouslySetInnerHTML={{
                        __html: decode(disclaimerText)
                    }}
                />
            )}
        </div>
    )
}
