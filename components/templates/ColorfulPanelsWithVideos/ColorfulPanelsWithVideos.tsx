import {
    StickyNavigationBanner,
    StickyNavigationBannerProps
} from '@components/molecules/StickyNavigationBanner/StickyNavigationBanner'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { CardProps } from '@components/templates/CardList/CardList'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'
import cn from 'classnames'
import { FC, useEffect, useRef, useState } from 'react'
import s from './ColorfulPanelsWithVideos.module.scss'

export const ColorfulPanelsWithVideos: FC<SpecialAnimationContent> = ({
    content
}) => {
    const [windowWidth, setWindowWidth] = useState(0)
    const containerRef = useRef<HTMLDivElement>(null)
    const videosRef = useRef<Array<HTMLDivElement | null>>([])
    const panelsRef = useRef<Array<HTMLDivElement | null>>([])
    const cards = content.children as CardProps[]
    const promotionalBanners: StickyNavigationBannerProps[] = []
    cards.map((item) => {
        if (item.meta?.contentType === 'moleculeStickyNavigationBanner') {
            promotionalBanners.push(item as StickyNavigationBannerProps)
        }
    })

    const timelineRef = useRef<gsap.core.Timeline[]>([])

    // useEffect for setting the height based on resize
    useEffect(() => {
        const handleResize = () => {
            setWindowWidth(window.innerWidth)
        }

        handleResize()

        window.addEventListener('resize', handleResize)
        return () => {
            window.removeEventListener('resize', handleResize)
            timelineRef.current.map((timeline) => timeline.kill())
        }
    }, [cards])

    useEffect(() => {
        const handleScroll = () => {
            if (panelsRef.current.length > 0) {
                // remove active class from video
                videosRef.current.forEach((video) => {
                    video?.classList.remove(s['video-active'])
                    video?.classList.remove(s['video-next'])
                })

                // remove opacity from all panel content (and setting it back later)
                panelsRef.current.forEach((panel) => {
                    const child = panel?.querySelector(
                        `.${s['panel-content']}`
                    ) as HTMLElement
                    child.style.opacity = `1`
                })

                // find active panel (the one that is at the top of the screen)
                const next = panelsRef.current.findIndex(function (panel) {
                    return panel && panel.getBoundingClientRect().top > 0
                })

                const nextPanel = panelsRef.current[next]
                const nextVideo = videosRef.current[next]

                // set active video
                nextVideo?.classList.add(s['video-active'])

                // set previous video
                const prevVideo = videosRef.current[next - 1]
                prevVideo?.classList.add(s['video-prev'])

                const nextPanelRect = nextPanel?.getBoundingClientRect()
                const prevVideoRect = prevVideo?.getBoundingClientRect()

                // calculate the opacity of the text below the video. It should be 1 when nextVideo reaches 50% of the screen and 0 when it reaches the top
                let opacity = 1
                let child = panelsRef.current[next - 1]?.querySelector(
                    `.${s['panel-content']}`
                ) as HTMLElement
                // if the next panel is not defined, the last panel is active
                if (!child) {
                    child = panelsRef.current[
                        panelsRef.current.length - 1
                    ]?.querySelector(`.${s['panel-content']}`) as HTMLElement
                }

                if (
                    child.getBoundingClientRect().top <
                        window.innerHeight / 2 &&
                    window.innerWidth < 1024
                ) {
                    opacity =
                        (child.getBoundingClientRect().top -
                            window.innerHeight / 4) /
                        (window.innerHeight / 2 - window.innerHeight / 4)
                    if (opacity < 0) {
                        opacity = 0
                    } else if (opacity > 1) {
                        opacity = 1
                    }
                    child.style.opacity = `${opacity}`
                } else {
                    child.style.opacity = `1`
                }

                // when the top edge of the panel touches the bottom of the video, the videos need to transform
                if (prevVideo && nextVideo && nextPanelRect && prevVideoRect) {
                    let rate =
                        1 -
                        (nextPanelRect.top - prevVideoRect.top) /
                            prevVideoRect.height
                    if (rate < 0) {
                        rate = 0
                    }
                    if (rate > 1) {
                        rate = 1
                    }
                    nextVideo.style.transform = `scaleY(${
                        Math.round(rate * 10000) / 100
                    }%)`
                    const innerVideo = nextVideo.querySelector('video')
                    innerVideo!.style.transform = `scaleY(${1 / rate})`
                } else if (!nextVideo) {
                    // since there is no next video, show the last video
                    const lastVideo =
                        videosRef.current[panelsRef.current.length - 1]
                    lastVideo?.classList.add(s['video-active'])
                }
            }
        }

        document.addEventListener('scroll', handleScroll)
        return () => {
            document.removeEventListener('scroll', handleScroll)
        }
    }, [cards, windowWidth])

    return (
        <div className={cn(s['colorful-panels-with-videos-wrapper'])}>
            {promotionalBanners?.map((item, index) => {
                return (
                    <StickyNavigationBanner
                        key={index}
                        message={item.message}
                        buttons={item.buttons}
                    />
                )
            })}
            <div
                className={cn(s['colorful-panels-with-videos'])}
                ref={containerRef}
            >
                <div className={cn(s['video-container'])}>
                    <div className={cn(s['video-inner-container'])}>
                        <div className={cn(s['video-camera-overlay'])} />
                        {cards?.map((item, index) => {
                            console.log(item)

                            let videoDescAttributes
                            if (item?.customOptions?.videoDescription) {
                                videoDescAttributes = {
                                    tabIndex: 0,
                                    role: 'img',
                                    'aria-describedby': `video-description-${index}`
                                }
                            }

                            if (item.meta?.contentType !== 'organismCard') {
                                return null
                            }
                            return (
                                item?.cloudinaryMedia && (
                                    <div className={cn(s['video-wrapper'])}>
                                        <img
                                            className={cn(s['video-border'])}
                                            src="https://res.cloudinary.com/elgato-pwa/image/upload/v1709139199/Products/10WAC9901/TopOfScreen_Screen_1.png"
                                            alt=""
                                        />
                                        <div
                                            key={index}
                                            ref={(el) =>
                                                (videosRef.current[index] = el)
                                            }
                                            className={cn(s['video'])}
                                            {...videoDescAttributes}
                                        >
                                            <video
                                                autoPlay
                                                loop
                                                muted
                                                playsInline
                                                preload="auto"
                                                className={s['video-video']}
                                            >
                                                <source
                                                    src={
                                                        item
                                                            .cloudinaryMedia?.[0]
                                                            .secure_url
                                                    }
                                                />
                                            </video>
                                            {item?.customOptions
                                                ?.videoDescription &&
                                                videoDescAttributes && (
                                                    <p
                                                        className="sr-only"
                                                        id={`video-description-${index}`}
                                                    >
                                                        {
                                                            item?.customOptions
                                                                ?.videoDescription
                                                        }
                                                    </p>
                                                )}
                                        </div>
                                    </div>
                                )
                            )
                        })}
                    </div>
                </div>
                <div className={cn(s['panel-container'])}>
                    {cards?.map((item, index) => {
                        if (item.meta?.contentType !== 'organismCard') {
                            return null
                        }
                        return (
                            <div
                                key={index}
                                ref={(el) => (panelsRef.current[index] = el)}
                                className={cn(s['panel'])}
                                style={item.customOptions}
                            >
                                <Container size={ContainerSize.XLARGE}>
                                    <div className={cn(s['panel-content'])}>
                                        {item.textPanel && (
                                            <TextPanel
                                                content={item.textPanel}
                                            />
                                        )}
                                    </div>
                                </Container>
                            </div>
                        )
                    })}
                </div>
            </div>
        </div>
    )
}

export default ColorfulPanelsWithVideos
