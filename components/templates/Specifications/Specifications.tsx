import React, { FC, useCallback } from 'react'
import s from './Specifications.module.scss'
import cn from 'classnames'
import {
    InTheBox,
    InTheBoxProps
} from '@components/organisms/InTheBox/InTheBox'
import {
    SpecsList,
    SpecsListProps
} from '@components/organisms/SpecsList/SpecsList'
import { TextBlock } from '@components/molecules/TextBlock/TextBlock'
import { FeatureListProps } from '@components/templates/FeatureList/FeatureList'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import { Button } from '@components/molecules/Button/Button'
import { Tooltip, TooltipProps } from '@components/organisms/Tooltip/Tooltip'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import { Region } from '@components/molecules/EnergyEfficiencyTooltip/EnergyEfficiencyTooltip'
import { CardProps } from '@components/templates/CardList/CardList'
import { WaveInTheBox } from '@components/organisms/WaveInTheBox/WaveInTheBox'
import unescape from 'lodash.unescape'

export type SpecificationsProps = {
    columns?: (SpecsListProps | InTheBoxProps)[] // deprecated
    className?: string
    features?: FeatureListProps[]
    featuresText?: string
    cards?: CardProps[] | undefined
    neoVariant?: string
}

export const Specifications: FC<SpecificationsProps> = (props) => {
    const { features, className, cards, neoVariant } = props
    const textBlockSize = 'small'
    const { t } = useTranslation(['common'])
    const { locale } = useRouter()
    const currentRegionCode = locale?.substring(3) || ''

    const getLink = (link?: LinkResponse) => {
        if (!link || !link.linkTitle || !link.linkUrl) {
            return undefined
        }

        if (link.style && link.style !== 'link') {
            return (
                <Button
                    href={link.linkUrl}
                    variant={link.style}
                    color={link.styleColor}
                    label={link.linkTitle}
                >
                    {link.linkTitle}
                </Button>
            )
        }

        return (
            <Link
                className={s['specifications__link']}
                link={link}
                aria-label={`${link.linkTitle} - ${t(
                    'ada|Opens in the current Tab'
                )}`}
            >
                {link.linkTitle}
            </Link>
        )
    }

    const hasTooltipsForRegion = useCallback(
        (tooltips) => {
            if (tooltips) {
                const tooltipsForRegion = tooltips.filter(
                    (tooltip: TooltipProps) => {
                        if (!tooltip.regions || !tooltip.regions.length) {
                            return true
                        } else {
                            return tooltip.regions.find((region: Region) => {
                                return region.name === currentRegionCode
                            })
                        }
                    }
                )
                return tooltipsForRegion.length > 0
            }
            return false
        },
        [currentRegionCode]
    )
    return (
        <div
            className={cn(
                'w-full mx-auto max-w-1187px md:grid md:gap-64px md:grid-cols-2',
                {
                    'md:grid-cols-2': features && features?.length === 2,
                    'md:grid-cols-3': features && features?.length === 3,
                    'lg:grid-cols-4 md-max:grid-cols-2 xxl:gap-4':
                        features && features?.length > 3,
                    [s['specifications__in-the-box-container']]:
                        features &&
                        features?.length > 2 &&
                        neoVariant === 'neo-in-the-box',
                    [s['specifications__wave-neo']]:
                        neoVariant === 'neo-in-the-box' ||
                        neoVariant === 'neo-tech-specs' ||
                        neoVariant === 'wave-neo',
                    [s['specifications__wave-neo--tech-specs']]:
                        neoVariant === 'neo-tech-specs',
                    [s['specifications__wave-neo--system-requirements']]:
                        neoVariant === 'tech-specs',
                    [s['specifications__wave-neo--columns']]:
                        neoVariant === 'tech-specs' &&
                        features &&
                        features?.length > 3
                },
                className
            )}
        >
            {features &&
                features.length > 0 &&
                features.map((featureList, i) => {
                    return (
                        <>
                            {featureList.featuresText && (
                                <div
                                    key={i}
                                    className={cn(s['specifications__column'], {
                                        [s[
                                            'specifications__column__wave-neo__tech-specs'
                                        ]]: neoVariant === 'neo-tech-specs'
                                    })}
                                >
                                    <TextBlock
                                        size={textBlockSize}
                                        bodyCopy={featureList.featuresText}
                                        className={cn(s['specs-list__text'])}
                                        disclaimerText={
                                            featureList.featureDisclaimerText
                                        }
                                        id={featureList.id}
                                    />
                                    {getLink(featureList.link)}
                                    {featureList.tooltips &&
                                        featureList.tooltips.length > 0 &&
                                        hasTooltipsForRegion(
                                            featureList.tooltips
                                        ) && (
                                            <div
                                                className={
                                                    s[
                                                        'specifications__tooltips'
                                                    ]
                                                }
                                            >
                                                <h4>
                                                    {t('Energy efficiency')}
                                                </h4>
                                                {featureList.tooltips.map(
                                                    (tooltip) => {
                                                        return (
                                                            <Tooltip
                                                                key={`tooltip-${tooltip.title}`}
                                                                content={
                                                                    tooltip
                                                                }
                                                            />
                                                        )
                                                    }
                                                )}
                                            </div>
                                        )}
                                    {/* used for Conscious Packaging on the left side */}
                                    <InTheBox
                                        consciousPackagingTitle={
                                            featureList.textPanel?.headline
                                        }
                                        consciousPackagingText={
                                            featureList.textPanel?.bodyCopy
                                                ? featureList?.textPanel
                                                      ?.bodyCopy
                                                : featureList?.textPanel
                                                      ?.richText
                                        }
                                    />
                                </div>
                            )}
                            {featureList.features &&
                                featureList.features.length > 0 && (
                                    <div
                                        key={i + 1}
                                        className={cn(
                                            s['specifications__column'],
                                            {
                                                [s[
                                                    'specifications__column__wave-neo__in-the-box'
                                                ]]:
                                                    neoVariant ===
                                                    'neo-in-the-box'
                                            }
                                        )}
                                    >
                                        {featureList.features && (
                                            <SpecsList
                                                features={featureList.features}
                                                id={featureList.id}
                                                variant={neoVariant}
                                            />
                                        )}
                                        {!featureList.featuresText &&
                                            getLink(featureList.link)}
                                    </div>
                                )}
                        </>
                    )
                })}
            {cards &&
                cards.length > 0 &&
                cards.some((card) => card.cmsATCLocation !== undefined) && (
                    //Check for cmsATCLocation condition
                    <div
                        className={cn({
                            [s['specifications__wave-neo-cards']]:
                                neoVariant === 'neo-in-the-box'
                        })}
                    >
                        {cards.map(
                            (card, index) =>
                                card.cmsATCLocation && (
                                    // eslint-disable-next-line react/jsx-no-useless-fragment
                                    <>
                                        <div
                                            key={index}
                                            className={cn(
                                                s[
                                                    'specifications__card-column'
                                                ],
                                                {
                                                    [s[
                                                        'specifications__card-column__wave-neo'
                                                    ]]:
                                                        neoVariant ===
                                                        'neo-in-the-box'
                                                }
                                            )}
                                        >
                                            {card.children?.map((card, i) => {
                                                return (
                                                    <WaveInTheBox
                                                        key={i}
                                                        className={cn(
                                                            s[
                                                                'specifications__cards'
                                                            ]
                                                        )}
                                                        media={card.media}
                                                        mobileMedia={
                                                            card.mobileMedia
                                                        }
                                                        posterImage={
                                                            card.posterImage
                                                        }
                                                        textColor={
                                                            card.textColor as
                                                                | 'dark'
                                                                | 'light'
                                                        }
                                                        icon={card.icon}
                                                        iconText={card.iconText}
                                                        backgroundColor={
                                                            card.backgroundColor
                                                        }
                                                        cloudinaryMedia={
                                                            card.cloudinaryMedia
                                                        }
                                                        cloudinaryPosterImage={
                                                            card.cloudinaryPosterImage
                                                        }
                                                    />
                                                )
                                            })}
                                            {neoVariant ===
                                                'neo-in-the-box' && (
                                                <div
                                                    className={cn(
                                                        s[
                                                            'specifications__card-column__disclaimer-text'
                                                        ],
                                                        'md-max:hidden'
                                                    )}
                                                    dangerouslySetInnerHTML={{
                                                        __html: unescape(
                                                            features &&
                                                                features[0]
                                                                    ?.textPanel
                                                                    ?.disclaimerText
                                                        )
                                                    }}
                                                >
                                                    {features &&
                                                        features.length > 0 &&
                                                        features[0] &&
                                                        features[0]?.textPanel
                                                            ?.disclaimerText}
                                                </div>
                                            )}
                                        </div>
                                    </>
                                )
                        )}
                    </div>
                )}
        </div>
    )
}

export class SpecificationsColumn {}
