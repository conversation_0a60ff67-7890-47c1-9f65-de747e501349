import s from './StreamDeckInteractionPanel.module.scss'
import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import { SectionBgColor } from '@components/templates/Section/Section'
import cn from 'classnames'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import Image from '@corsairitshopify/corsair-image'
import Video from '@components/molecules/Video/Video'
import { useTranslation } from 'next-i18next'
import cursorImage from './cursor.png'
import { StreamDeckInteractionPanelStreamDeckMk2 } from '@components/templates/StreamDeckInteractionPanel/StreamDeckInteractionPanelStreamDeckMk2'
import { StreamDeckInteractionPanelCamLinkPro } from '@components/templates/StreamDeckInteractionPanel/StreamDeckInteractionPanelCamLinkPro'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

export type StreamDeckInteractionPanelProps = {
    title?: string
    textPanel?: PrimaryTextProps
    baseMedia?: ImageType | VideoType
    streamDeckImage?: ImageType
    interactionImages?: ImageType[]
    labels?: string[]
    bgColor?: SectionBgColor
    audio?: string
    videoKey2?: ImageType | VideoType
    videoKey3?: VideoType
    videoKey4?: VideoType
    imageKey1?: ImageType
    imageKey2?: ImageType
    imageKey3?: ImageType
    imageKey4?: ImageType
    imageKey5?: ImageType
    videoDescription?: string
}
export type StreamDeckInteractionPanelContent = {
    content: StreamDeckInteractionPanelProps
    variant: 'cam-link-pro' | 'stream-deck-mk2'
}

export const StreamDeckInteractionPanel: FC<StreamDeckInteractionPanelContent> = ({
    content,
    variant
}) => {
    const {
        textPanel,
        baseMedia,
        streamDeckImage,
        interactionImages = [],
        labels = [],
        bgColor = SectionBgColor.TRANSPARENT,
        audio,
        videoKey3,
        videoKey4,
        imageKey1,
        imageKey2,
        imageKey3,
        imageKey4,
        imageKey5,
        videoDescription
    } = content
    const { t } = useTranslation()
    const loadingLazy = 'lazy'
    const panelRef = useRef<HTMLDivElement>(null)
    const audioRef = useRef<HTMLAudioElement>(null)
    const streamDeckRef = useRef<HTMLDivElement>(null)
    const interactionImageRef = useRef<HTMLDivElement>(null)
    const { isOnScreen } = useOnScreen(streamDeckRef, true, { threshold: 0 })
    const [shownIntro, setShownIntro] = useState(false)
    const [showIntro, setShowIntro] = useState(false)
    const [clicked, setClicked] = useState(false)
    const [lottieAnimationData, setLottieAnimationData] = useState()
    const [playLottieAnimation, setPlayLottieAnimation] = useState(false)
    const [activeInteractionImage, setActiveInteractionImage] = useState<
        null | number
    >(null)
    const [keyPressAnimation, setKeyPressAnimation] = useState(false)
    const isVideo = baseMedia?.file?.contentType?.includes('video')
    const startLottieAnimation = () => {
        setPlayLottieAnimation(true)
        audioRef.current?.play()
    }
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    useEffect(() => {
        if (!showIntro && isOnScreen) {
            import('./sd-mk2-lottie-animation.json').then(
                // @ts-ignore
                setLottieAnimationData
            )
            // show intro animation
            setTimeout(() => {
                setShowIntro(true)
                setTimeout(() => {
                    handleInteractionImageClick(1, false)
                    setTimeout(() => {
                        setShownIntro(true)
                    }, 300)
                }, 500)
            }, 1500)
        }
    }, [isOnScreen])
    const handleInteractionImageClick = useCallback(
        (i: number, clicked = true) => {
            setKeyPressAnimation(true)
            if (interactionImageRef.current) {
                setTimeout(() => {
                    setKeyPressAnimation(false)
                }, 300)
            }
            setActiveInteractionImage(i)
            if (i === 0) {
                startLottieAnimation()
            }
            if (clicked) {
                setClicked(true)
            }
        },
        []
    )
    const isActiveLabel = useCallback(
        (i: number) => {
            switch (i) {
                case 1: {
                    return true
                }
                case 2: {
                    return (
                        activeInteractionImage === 3 ||
                        activeInteractionImage === 4
                    )
                }
                case 3: {
                    return (
                        activeInteractionImage === 1 ||
                        activeInteractionImage === 2 ||
                        activeInteractionImage === 4
                    )
                }
                default: {
                    return false
                }
            }
        },
        [activeInteractionImage]
    )
    return (
        <div
            className={cn(
                s['stream-deck-interaction-panel'],
                s[`stream-deck-interaction-panel--${variant}`],
                {
                    [bgColor]: bgColor !== SectionBgColor.TRANSPARENT
                }
            )}
            ref={panelRef}
        >
            <Container size={ContainerSize.SMALL}>
                <div className={s['stream-deck-interaction-panel__text']}>
                    {textPanel && (
                        <PrimaryText
                            calloutTitle={textPanel.calloutTitle}
                            headline={textPanel.headline}
                            bodyCopy={textPanel.bodyCopy}
                            link={textPanel.link}
                            textAlignment={HorizontalAlignmentEnum.CENTER}
                        />
                    )}
                </div>
            </Container>
            {baseMedia && streamDeckImage && (
                <Container size={ContainerSize.SMALL}>
                    <div className={s['stream-deck-interaction-panel__inner']}>
                        <div
                            className={
                                s['stream-deck-interaction-panel__media']
                            }
                        >
                            {isVideo && (
                                <Video
                                    video={baseMedia}
                                    options={{
                                        autoPlay: true,
                                        preload: 'none',
                                        muted: true,
                                        loop: true
                                    }}
                                    videoDescription={videoDescription}
                                />
                            )}
                            {!isVideo && (
                                <div
                                    className={
                                        s[
                                            'stream-deck-interaction-panel__image-wrapper'
                                        ]
                                    }
                                >
                                    <Image
                                        src={baseMedia?.file?.url}
                                        alt={baseMedia?.description || ''}
                                        layout="fill"
                                        loading={loadingLazy}
                                    />
                                </div>
                            )}
                            {variant === 'stream-deck-mk2' && (
                                <StreamDeckInteractionPanelStreamDeckMk2
                                    imageKey2={imageKey2}
                                    videoKey3={videoKey3}
                                    videoKey4={videoKey4}
                                    activeInteractionImage={
                                        activeInteractionImage
                                    }
                                    playLottieAnimation={playLottieAnimation}
                                    lottieAnimationData={lottieAnimationData}
                                    setPlayLottieAnimation={
                                        setPlayLottieAnimation
                                    }
                                    audio={audio}
                                    audioRef={audioRef}
                                />
                            )}
                            {variant === 'cam-link-pro' && (
                                <StreamDeckInteractionPanelCamLinkPro
                                    activeInteractionImage={
                                        activeInteractionImage
                                    }
                                    images={[
                                        imageKey1,
                                        imageKey2,
                                        imageKey3,
                                        imageKey4,
                                        imageKey5
                                    ]}
                                />
                            )}
                        </div>
                        <div
                            className={
                                s['stream-deck-interaction-panel__interactive']
                            }
                            ref={streamDeckRef}
                        >
                            <div
                                className={
                                    s['stream-deck-interaction-panel__hint']
                                }
                            >
                                {/* eslint-disable-next-line i18next/no-literal-string,jsx-a11y/accessible-emoji */}
                                <span className={s['clickMeIcon']}>⬇️</span>
                                {/* eslint-disable-next-line jsx-a11y/accessible-emoji,i18next/no-literal-string */}
                                <h4>click me</h4>
                                {/*click me ➡️*/}
                            </div>
                            <div
                                className={
                                    s[
                                        'stream-deck-interaction-panel__stream-deck-image'
                                    ]
                                }
                            >
                                <Image
                                    src={streamDeckImage?.file?.url}
                                    alt={streamDeckImage?.description || ''}
                                    layout="fill"
                                    loading={loadingLazy}
                                />
                            </div>
                            {interactionImages &&
                                interactionImages.map((ii, i) => (
                                    <div
                                        ref={
                                            activeInteractionImage === i
                                                ? interactionImageRef
                                                : undefined
                                        }
                                        key={ii?.title}
                                        className={cn(
                                            s[
                                                'stream-deck-interaction-panel__interaction-image'
                                            ],
                                            s[
                                                `stream-deck-interaction-panel__interaction-image--${i}`
                                            ],
                                            {
                                                [s[
                                                    'stream-deck-interaction-panel__interaction-image--active'
                                                ]]:
                                                    activeInteractionImage ===
                                                    i,
                                                [s[
                                                    'stream-deck-interaction-panel__interaction-image--animated'
                                                ]]:
                                                    activeInteractionImage ===
                                                        i && keyPressAnimation
                                            }
                                        )}
                                        role="button"
                                        tabIndex={0}
                                        onClick={() =>
                                            handleInteractionImageClick(i)
                                        }
                                        onKeyPress={() =>
                                            handleInteractionImageClick(i)
                                        }
                                    >
                                        <div
                                            className={cn(
                                                s[
                                                    'stream-deck-interaction-panel__animation-wrapper'
                                                ],
                                                {
                                                    [s[
                                                        'stream-deck-interaction-panel__animation-wrapper--active'
                                                    ]]: shownIntro && !clicked
                                                }
                                            )}
                                        >
                                            <div
                                                className={cn(
                                                    s[
                                                        'stream-deck-interaction-panel__animation'
                                                    ],
                                                    {
                                                        [s[
                                                            'stream-deck-interaction-panel__animation--no-animation'
                                                        ]]: isAnimationStopped
                                                    }
                                                )}
                                            />
                                            <div
                                                className={cn(
                                                    s[
                                                        'stream-deck-interaction-panel__animation'
                                                    ],
                                                    {
                                                        [s[
                                                            'stream-deck-interaction-panel__animation--no-animation'
                                                        ]]: isAnimationStopped
                                                    }
                                                )}
                                            />
                                            <div
                                                className={cn(
                                                    s[
                                                        'stream-deck-interaction-panel__animation'
                                                    ],
                                                    {
                                                        [s[
                                                            'stream-deck-interaction-panel__animation--no-animation'
                                                        ]]: isAnimationStopped
                                                    }
                                                )}
                                            />
                                        </div>
                                        <Image
                                            src={ii?.file?.url}
                                            alt={ii?.description || ''}
                                            layout="fill"
                                            loading={loadingLazy}
                                        />
                                    </div>
                                ))}
                            <div
                                className={cn(
                                    s['stream-deck-interaction-panel__cursor'],
                                    {
                                        [s[
                                            'stream-deck-interaction-panel__cursor--active'
                                        ]]: showIntro,
                                        [s[
                                            'stream-deck-interaction-panel__cursor--inactive'
                                        ]]: shownIntro
                                    }
                                )}
                            >
                                <Image
                                    src={cursorImage}
                                    width={36}
                                    height={44}
                                    layout="responsive"
                                    objectFit="contain"
                                    alt={t('Click me cursor')}
                                />
                            </div>
                        </div>
                        {labels && labels.length > 0 && (
                            <div
                                className={
                                    s[
                                        'stream-deck-interaction-panel__labels-wrapper'
                                    ]
                                }
                            >
                                <ul
                                    className={
                                        s[
                                            'stream-deck-interaction-panel__labels'
                                        ]
                                    }
                                >
                                    {labels.map((label, i) => (
                                        <li
                                            className={cn(
                                                s[
                                                    'stream-deck-interaction-panel__label'
                                                ],
                                                {
                                                    ['h5']: i > 0,
                                                    ['body-copy']: i === 0,
                                                    [s[
                                                        'stream-deck-interaction-panel__label--active'
                                                    ]]: isActiveLabel(i)
                                                }
                                            )}
                                            key={`${label}-${i}`}
                                        >
                                            {label}
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )}
                    </div>
                </Container>
            )}
        </div>
    )
}

export default StreamDeckInteractionPanel
