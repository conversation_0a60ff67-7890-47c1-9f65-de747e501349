import React, { FC } from 'react'
import ComparisonTable, {
    ComparisonTableProps
} from '@components/templates/ComparisonTable/ComparisonTable'

export interface ComparisonTableContentfulContent {
    content: ComparisonTableProps
}

export const ComparisonTableContentful: FC<ComparisonTableContentfulContent> = ({
    content
}) => {
    return (
        <ComparisonTable
            sections={content.sections}
            features={content.features}
            logos={content.logos}
            logoPlacement={content.logoPlacement}
            images={content.images}
            headlines={content.headlines}
            buttons={content.buttons}
            disclaimerTexts={content.disclaimerTexts}
        />
    )
}

export default ComparisonTableContentful
