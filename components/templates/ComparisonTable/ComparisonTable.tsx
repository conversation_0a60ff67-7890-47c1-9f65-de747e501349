import s from './ComparisonTable.module.scss'
import React, { FC, useEffect, useMemo, useRef } from 'react'
import ComparisonSection, {
    ComparisonSectionProps
} from '@components/organisms/ComparisonSection/ComparisonSection'
import { parse } from 'node-html-parser'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { ComparisonContentProps } from '@components/organisms/ComparisonContent/ComparisonContent'
import dynamic from 'next/dynamic'
import { SwiperSlide } from 'swiper/react'
import { A11y, Pagination } from 'swiper'
import { SwiperOptions } from 'swiper/types/swiper-options'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import { LinkResponse } from '@components/molecules/Link/Link'
const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

export interface ComparisonTableProps {
    sections?: ComparisonSectionProps[]
    meta?: { contentType: 'templateComparisonTable' }
    features?: string
    logos?: string[]
    images?: ImageType[]
    headlines?: string[]
    buttons?: LinkResponse[]
    id?: string
    disclaimerTexts?: string
    logoPlacement?: string
}

/* eslint i18next/no-literal-string: ["error", { "ignoreAttribute": ["name"] }] */
export const ComparisonTable: FC<ComparisonTableProps> = (props) => {
    const comparisonTableRef = useRef<HTMLDivElement>(null)
    const { isOnScreen } = useOnScreen(comparisonTableRef, true, {
        threshold: 0
    })
    const {
        sections,
        features,
        logos = [],
        images = [],
        headlines = [],
        buttons = [],
        id,
        disclaimerTexts = '',
        logoPlacement = 'top'
    } = props
    const convertToTableIcon = (content: string) => {
        if (content === 'true') {
            // eslint-disable-next-line i18next/no-literal-string
            return 'checkmark'
        } else if (content === 'false') {
            // eslint-disable-next-line i18next/no-literal-string
            return 'close'
        }
        return null
    }
    const tableProps = useMemo(() => {
        const convertToTableObject = (
            tableHtml: string,
            logos: string[],
            images: ImageType[],
            headlines: string[],
            buttons: LinkResponse[],
            disclaimerTexts: string,
            logoPlacement: string
        ) => {
            const doc = parse(tableHtml)
            // eslint-disable-next-line i18next/no-literal-string
            const trs = doc.querySelectorAll('tr')
            const tableData: ComparisonContentProps[][] = []

            const disclaimers: string[] = []
            if (disclaimerTexts) {
                const disclaimersTable = parse(disclaimerTexts)
                // eslint-disable-next-line i18next/no-literal-string
                const disclaimerCols = disclaimersTable.querySelectorAll('td')
                if (disclaimerCols) {
                    disclaimerCols.forEach((disclaimerCol) => {
                        disclaimers.push(disclaimerCol.innerHTML)
                    })
                }
            }

            // add table header if logos and images are set
            trs.forEach((tr, i) => {
                // eslint-disable-next-line i18next/no-literal-string
                const tds = tr.querySelectorAll('td')
                tds.forEach((td, j) => {
                    if (!tableData[j]) {
                        tableData[j] = []
                    }
                    const icon = convertToTableIcon(td.innerText)
                    if (
                        i === 0 &&
                        (logos.length || images.length || headlines.length)
                    ) {
                        if (j === 0) {
                            tableData[j].push({})
                        } else {
                            tableData[j].push({
                                image: images[j - 1]
                                    ? images[j - 1]
                                    : undefined,
                                logo: logos[j - 1] ? logos[j - 1] : undefined,
                                headline: headlines[j - 1]
                                    ? headlines[j - 1]
                                    : undefined
                            })
                        }
                    }
                    tableData[j].push({
                        rowTitle: j === 0 ? td.innerText : undefined,
                        text: j !== 0 && !icon ? td.innerText : undefined,
                        icon: icon ? icon : undefined
                    })
                    if (
                        i === trs.length - 1 &&
                        (buttons.length || disclaimers.length)
                    ) {
                        // append button row
                        if (buttons.length) {
                            if (j === 0) {
                                tableData[j].push({})
                            } else {
                                tableData[j].push({
                                    button: buttons[j - 1]
                                        ? buttons[j - 1]
                                        : undefined
                                })
                            }
                        }

                        // append disclaimers row
                        if (disclaimers.length) {
                            tableData[j].push({
                                disclaimer: disclaimers[j]
                                    ? disclaimers[j]
                                    : undefined
                            })
                        }
                    }
                })
            })
            return tableData
        }
        let tableContent: ComparisonContentProps[][] = []
        if (features) {
            tableContent = convertToTableObject(
                features,
                logos,
                images,
                headlines,
                buttons,
                disclaimerTexts,
                logoPlacement
            )
        }
        return tableContent
    }, [features])
    const numberOfRows = useMemo(() => {
        if (tableProps && tableProps[0]) {
            return tableProps[0].length
        }
        return 0
    }, [tableProps])

    const sliderSettings: SwiperOptions = {
        // eslint-disable-next-line i18next/no-literal-string
        slidesPerView: 'auto',
        spaceBetween: 0,
        roundLengths: true,
        loopAdditionalSlides: 30,
        modules: [A11y, Pagination],
        pagination: {
            clickable: true
        },
        navigation: false,
        allowTouchMove: true
    }

    useEffect(() => {
        const setColHeight = (row: number) => {
            if (comparisonTableRef.current) {
                const cols = comparisonTableRef.current.querySelectorAll<HTMLElement>(
                    `[data-row="${row}"]`
                )
                let height = 0
                cols.forEach(function (col) {
                    col.style.height = 'auto'
                    height =
                        col.clientHeight > height ? col.clientHeight : height
                })
                cols.forEach(function (col) {
                    col.style.height = `${height}px`
                })
            }
        }
        const handleResize = () => {
            if (comparisonTableRef.current) {
                if (numberOfRows) {
                    for (let i = 0; i < numberOfRows; i++) {
                        setColHeight(i)
                    }
                } else if (sections && sections.length) {
                    sections.forEach((section) => {
                        if (section.rows && section.rows.length) {
                            section.rows.forEach((row, i) => {
                                setColHeight(i)
                            })
                        }
                    })
                }
            }
        }

        if (isOnScreen) {
            handleResize()
            window.addEventListener('resize', handleResize)
            return () => window.removeEventListener('resize', handleResize)
        }
    }, [comparisonTableRef, sections, isOnScreen])

    return (
        <div className={s['comparison-table']} ref={comparisonTableRef} id={id}>
            {sections && sections.length > 0 && (
                <>
                    <ComparisonSection
                        key="comparison-table-section-0"
                        rows={sections[0].rows}
                        bg={false}
                        titleSection
                    />
                    {sections.length > 1 && (
                        <div className={s['comparison-table__section-wrapper']}>
                            <SwiperSlider
                                settings={sliderSettings}
                                className={s['comparison-table__slider']}
                                loop={false}
                                paginationTheme={SwiperSliderTheme.DARK}
                            >
                                {sections.map((section, i) => {
                                    if (i > 0) {
                                        return (
                                            <SwiperSlide
                                                className={
                                                    s[
                                                        'comparison-table-section'
                                                    ]
                                                }
                                                key={`comparison-table-section-${i}`}
                                            >
                                                <ComparisonSection
                                                    rows={section.rows}
                                                    bg={i === 1}
                                                />
                                            </SwiperSlide>
                                        )
                                    }
                                    return null
                                })}
                            </SwiperSlider>
                        </div>
                    )}
                    <ComparisonSection key="comparison-table-section-end" />
                </>
            )}
            {tableProps && tableProps.length > 0 && (
                <>
                    <ComparisonSection
                        key="comparison-table-section-0"
                        rows={tableProps[0]}
                        bg={false}
                    />
                    {tableProps.length > 1 && (
                        <div className={s['comparison-table__section-wrapper']}>
                            <SwiperSlider
                                settings={sliderSettings}
                                className={s['comparison-table__slider']}
                                loop={false}
                                paginationTheme={SwiperSliderTheme.DARK}
                            >
                                {tableProps.map((section, i) => {
                                    if (i > 0) {
                                        return (
                                            <SwiperSlide
                                                className={
                                                    s[
                                                        'comparison-table-section'
                                                    ]
                                                }
                                                key={`comparison-table-section-${i}`}
                                            >
                                                <ComparisonSection
                                                    rows={section}
                                                    bg={i === 1}
                                                />
                                            </SwiperSlide>
                                        )
                                    }
                                    return null
                                })}
                            </SwiperSlider>
                        </div>
                    )}
                    <ComparisonSection
                        className="hidden lg:block"
                        key="comparison-table-section-end"
                    />
                </>
            )}
        </div>
    )
}

export default ComparisonTable
