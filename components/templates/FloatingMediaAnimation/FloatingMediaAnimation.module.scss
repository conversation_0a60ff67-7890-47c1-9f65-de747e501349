.floating-media-animation {
    &__text-panel-container {
        display: flex;
        justify-content: center;
        align-items: center;

        @screen md {
            position: sticky;
            top: 0;
            bottom: 0;
            margin-top: -25vh;
            margin-bottom: -25vh;
            height: 100vh;
        }
    }

    &__cards-container {
        width: 100%;
        @screen md-max {
            display: flex;
            flex-direction: column;
            gap: 40px;
            padding-bottom: 40px;
            padding-left: 16px;
            padding-right: 16px;
        }
        @screen md {
            display: grid;
            grid-template-columns: repeat(12, minmax(0, 1fr));
            gap: 16px;
            padding-left: 68px;
            padding-right: 68px;
            padding-top: 25vh;
            padding-bottom: 50vh;
        }
    }

    &__play-pause-button {
        border-radius: 9999px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        background-color: #000000;
        color: var(--white);
        transition: all 0.2s ease-in-out;
        @screen md-max {
            display: none;
        }

        @screen md {
            position: sticky;
            top: calc(100vh - 48px - 10px);
            left: 0;
        }
    }

    &__text {
        color: red;
    }
}

.media-container {
    position: relative;
    border-radius: 16px;
    overflow: hidden;

    &__plugin {
        position: absolute;
        bottom: 16px;
        box-shadow: -1.86844px 1.86844px 12.4563px rgba(0, 0, 0, 0.12);

        @screen md-max {
            right: 16px;
        }

        @screen md {
            left: 16px;
        }
    }
}

.label {
    box-sizing: border-box;
    padding: 16px 24px;
    background: #f6f6f6;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.1), 0px 4px 8px rgba(0, 0, 0, 0.1),
        0px 8px 16px rgba(0, 0, 0, 0.05);
    border-radius: 16px;
}

.plugin {
    border-radius: 8px;
    overflow: hidden;
    width: 72px;
    height: 72px;
}

.play-pause-button {
    height: 48px;
    width: 48px;
}
