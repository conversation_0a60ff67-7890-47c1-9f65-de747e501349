import s from './ConfiguratorTeaser.module.scss'
import React, { FC } from 'react'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import Image from '@corsairitshopify/corsair-image'
import PrimaryText, {
    HorizontalAlignmentEnum,
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import { Button } from '@components/molecules/Button/Button'
import cn from 'classnames'
import { useIsMobileDevice } from '@config/hooks/useIsMobileDevice'

export interface ConfiguratorTeaserProps {
    title?: string
    textPanel?: PrimaryTextProps
    image?: ImageType
    hoverImage?: ImageType
}

export interface ConfiguratorTeaserContent {
    content: ConfiguratorTeaserProps
}

export const ConfiguratorTeaser: FC<ConfiguratorTeaserContent> = ({
    content
}) => {
    const { image, hoverImage } = content
    const isSupported = !useIsMobileDevice()
    const headline = content.textPanel?.headline
    const bodyCopy = content.textPanel?.bodyCopy
    const disclaimerText = content.textPanel?.disclaimerText
    const textAlignment = HorizontalAlignmentEnum.CENTER
    const loadingLazy = 'lazy'
    const buttonStyle = 'primary'
    const link = content.textPanel?.link
    return (
        <div className={s['configurator-teaser']}>
            <div className={s['text-with-media__text-wrapper']}>
                <PrimaryText
                    headline={headline}
                    bodyCopy={bodyCopy}
                    textAlignment={textAlignment}
                    className={s['configurator-teaser__text']}
                />
            </div>
            {image && (
                <div className={s['configurator-teaser__teaser']}>
                    <div className={s['configurator-teaser__image-wrapper']}>
                        <Image
                            src={image?.file?.url}
                            alt={image?.description || ''}
                            width={1920}
                            height={1080}
                            layout="responsive"
                            loading={loadingLazy}
                            className={s['configurator-teaser__image']}
                        />
                        {link && link.linkUrl && isSupported && (
                            <div className={s['configurator-teaser__button']}>
                                <Button
                                    newTab={link.newTab}
                                    variant={buttonStyle}
                                    href={link.linkUrl}
                                    noLocale
                                    label={link.linkTitle}
                                >
                                    {link.linkTitle}
                                </Button>
                            </div>
                        )}
                    </div>
                    {hoverImage && (
                        <div className={s['configurator-teaser__hover-image']}>
                            <Image
                                src={hoverImage?.file?.url}
                                alt={hoverImage?.description || ''}
                                loading={loadingLazy}
                                layout="fill"
                            />
                        </div>
                    )}
                </div>
            )}
            {disclaimerText && !isSupported && (
                <div
                    className={cn(
                        s['configurator-teaser__warning-message'],
                        'body-copy'
                    )}
                >
                    {disclaimerText}
                </div>
            )}
        </div>
    )
}

export default ConfiguratorTeaser
