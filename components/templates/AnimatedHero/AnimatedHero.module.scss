.animated-hero {
    position: relative;
    z-index: 10;
    height: auto;

    &__wrapper {
        height: auto;

        &__video {
            overflow: hidden;
            transition: all 0.8s ease-out;
            height: calc(100vh - 124px);

            @media screen and (max-width: 767px) {
                height: calc(100vh - 64px);
            }

            // height: calc(100vh - 124px);
            // @media only screen and (max-width: 767px) {
            //     height: calc(100vh - 64px);
            // }

            div {
                height: 100%;

                video {
                    width: 100%;
                    height: 100%;
                    display: block;
                    object-fit: cover;
                    margin: 0 auto;
                }
            }
        }
    }

    &__content {
        display: flex;
        align-items: center;
        padding: 24px 64px;

        &__title {
            font-size: min(48px, 2.55vw);
        }

        &__subtitle {
            color: var(--greyscale-black-65);
            font-size: 28px;
        }
    }

    @media only screen and (max-width: 767px) {
        &__content {
            padding: 16px;

            &__subtitle {
                font-size: 20px;
            }

            &__title {
                font-size: 32px;
            }
        }
    }
}