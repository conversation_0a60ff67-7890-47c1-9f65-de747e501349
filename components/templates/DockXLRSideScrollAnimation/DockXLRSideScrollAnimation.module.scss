.dock-xlr-sidescroll {
    @apply relative lg:py-16 max-w-8xl mx-auto;

    @screen md {
        @apply max-w-full;
    }

    &__anim-wrapper {
        height: 250vh;
        position: relative;

        @screen md-max {
            height: 270vh;
        }
    }

    &__scrollbartest {
        @media screen and (max-height: 800px) {
            top: -85px !important;
        }
    }

    &__sticky {
        position: sticky;
        top: var(--sticky-nav-height--sm);
        left: 0;
        width: 100%;
        overflow: hidden;
        height: calc(100vh - var(--sticky-nav-height--sm));

        @screen md {
            top: var(--sticky-nav-height--lg);
            height: calc(100vh - var(--sticky-nav-height--lg));
        }
    }

    &__inner {
        @apply overscroll-none h-full flex items-center;
        flex-wrap: nowrap;
    }

    &__text {
        max-width: 585px;
    }

    .dock-xlr-sidescroll__text-color {
        color: inherit;
    }

    &__group {
        position: relative;
        width: 100%;
        height: 100%;
        flex: 0 0 100%;
        padding: 0 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        @screen lg {
            display: flex;
            flex-direction: row;
            flex: 0 0 100%;
            padding: 0 48px;
            justify-content: flex-start;
        }
    }

    &__media {
        flex: 0 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;

        @screen lg {
            width: 50%;
            height: auto;
        }

        video,
        img {
            border-radius: 16px;
        }
    }

    &__text-wrapper-no-padding-top {
        @screen md-max {
            padding-top: 0 !important;
        }
    }

    &__text-wrapper {
        @apply py-16 px-0;

        @screen md {
            @apply py-32;
        }

        @screen lg-max {
            h2 {
                @apply text-6xl;
            }
        }

        @screen lg {
            max-width: 50%;
            flex: 0 0 auto;
            padding-left: var(--container-padding--md);
            padding-right: var(--container-padding--md);
        }

        @screen lg2 {
            padding-left: var(--container-padding--lg);
            padding-right: var(--container-padding--lg);
        }

        @screen xl {
            padding-left: calc(100% / var(--grid-cols--lg));
            padding-right: calc((100% / var(--grid-cols--lg)));
        }
    }

    &__media-2 {
        padding-bottom: 0;
        height: auto;

        @screen md {
            // @apply h-auto;
            // @apply w-screen h-auto absolute top-1/2 left-1/2;
            // @apply transform -translate-x-1/2 -translate-y-1/2;
            // margin-left: 800px; // calc(585px + 6.4rem + 6.4rem);
        }

        // @screen lg {
        //     margin-left: 540px; // calc(585px + 6.4rem + 6.4rem);
        // }

        .dock-xlr-sidescroll__media-inner {
            @apply mx-auto;
            max-width: 85vw;
            img,
            video {
                border-radius: 16px;
            }
        }
    }
}
