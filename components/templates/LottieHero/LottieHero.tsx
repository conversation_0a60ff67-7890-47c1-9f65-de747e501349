import s from './LottieHero.module.scss'
import cn from 'classnames'
import { Player } from '@lottiefiles/react-lottie-player'
import React, { FC } from 'react'
import Image from '@corsairitshopify/corsair-image'
import { SpecialAnimationContent } from '@components/templates/SpecialAnimation/SpecialAnimation'

export const LottieHero: FC<SpecialAnimationContent> = ({ content }) => {
    // const lottieAnimationUrl = '/lottie_strip_light_green_red'
    const lottieAnimationUrl = content.medias?.find(
        (media) => media.file.contentType === 'application/json'
    )?.file.url
    // const imageUrl =
    //     'https://images.ctfassets.net/h50kqpe25yx1/6fcRSRdUPEf9iYrRIEAVCL/5debca83256e8996c30453392eb61588/Holiday-txt.png'
    const image = content.images?.[0]
    return (
        <div className={s['lottie-hero']}>
            {lottieAnimationUrl && (
                <div className={cn(s['lottie-hero__lottie'])}>
                    <Player autoplay loop src={lottieAnimationUrl} />
                </div>
            )}
            {image && (
                <div className={s['lottie-hero__image-container']}>
                    <Image
                        src={image.file.url}
                        alt={image?.description || ''}
                        layout="fill"
                        objectFit="contain"
                    />
                </div>
            )}
        </div>
    )
}

export default LottieHero
