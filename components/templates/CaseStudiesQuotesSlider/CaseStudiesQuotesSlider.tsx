import s from './CaseStudiesQuotesSlider.module.scss'
import { FC } from 'react'
import dynamic from 'next/dynamic'
import { SliderProps } from '@components/templates/Slider/Slider'
import { CardProps } from '@components/templates/CardList/CardList'
import { SwiperSlide } from 'swiper/react'
import { A11y, Navigation, Pagination } from 'swiper'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'
import cn from 'classnames'
import { CaseStudiesQuote } from '@components/templates/CaseStudiesQuotesSlider/CaseStudiesQuote'

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

const sliderSettings = {
    slidesPerView: 1,
    centeredSlides: true,
    spaceBetween: 16,
    modules: [A11y, Navigation, Pagination],
    pagination: {
        clickable: true
    }
}

export const CaseStudiesQuotesSlider: FC<{ content: SliderProps }> = ({
    content
}) => {
    const { sliderItems, bgColor } = content
    const slides = sliderItems as CardProps[]
    const backgroundColor = content.bgColor ?? SectionBgColor.TRANSPARENT
    const sliderTheme = SectionThemeDarkBgColors.includes(backgroundColor)
        ? SwiperSliderTheme.LIGHT
        : SwiperSliderTheme.DARK
    return (
        <div className={cn(s['case-studies-quotes-slider'], bgColor)}>
            {slides && slides.length === 1 && (
                <div className={s['case-studies-quotes-slider__slide']}>
                    <CaseStudiesQuote slide={slides[0]} />
                </div>
            )}
            {slides && slides.length > 1 && (
                <SwiperSlider
                    settings={sliderSettings}
                    paginationTheme={sliderTheme}
                    className={s['case-studies-quotes-slider__slider']}
                    navigationTheme={sliderTheme}
                >
                    {slides.map((slide, index) => {
                        return (
                            <SwiperSlide
                                key={`case-studies-quote-${index}-${slide.textPanel?.headline}`}
                            >
                                <div
                                    className={
                                        s['case-studies-quotes-slider__slide']
                                    }
                                >
                                    <CaseStudiesQuote slide={slide} />
                                </div>
                            </SwiperSlide>
                        )
                    })}
                </SwiperSlider>
            )}
        </div>
    )
}
