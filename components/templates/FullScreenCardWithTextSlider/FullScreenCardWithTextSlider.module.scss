.full-screen-card-with-text-slider {
    --offset-pagination: 92px;
    --sticky-navi-height: 89px;
    --total-offset: calc(var(--offset-pagination) + var(--sticky-navi-height));

    &__slide {
        @apply lg:h-screen;
        max-height: calc(100vh - var(--total-offset));
        > span {
            height: 100% !important;
        }
    }

    [data-nav='next'],
    [data-nav='prev'] {
        @apply md-max:hidden;
        top: calc(50% - calc(var(--offset-pagination) / 2));
    }

    :global {
        .swiper {
            padding-bottom: var(--offset-pagination);

            .swiper-pagination {
                bottom: 34px;
            }
        }
        @screen md-max {
            .swiper {
                padding-bottom: 0 !important;
            }
        }
        .swiper-wrapper {
            align-items: center;
        }
    }

    &__text-wrapper {
        @apply absolute left-0 right-0 z-1 mx-auto;
        bottom: 24px;
        max-width: 810px;
        width: fit-content;

        &-mobile {
            @screen md-max {
                padding: 0 !important;
                min-height: 100%;
            }
        }
    }

    &__text-block {
        @apply p-24px;
    }

    &__video {
        @apply h-full;

        video {
            object-fit: cover;
            height: 100%;
            width: 100%;
            border-radius: 12px;
        }
    }
}
