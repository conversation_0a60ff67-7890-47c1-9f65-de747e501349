import { Hotspot } from '@components/atoms/Hotspot/Hotspot'
import Sectioner from '@components/atoms/Sectioner/Sectioner'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import PrimaryText, {
    PrimaryTextProps
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import InfoBoxText from '@components/organisms/InfoBox/InfoBoxText'
import { Overlay } from '@components/organisms/Overlay/Overlay'
import SalesPopupCard, {
    SalesPopupCardProps
} from '@components/organisms/SalesPopupCard/SalesPopupCard'
import { HotspotOrCalloutProps } from '@components/templates/HardwareHotspotPanel/HardwareHotspotContent'
import { MERCH_CONFIG_FIT } from '@components/templates/ProductConfigurator/MerchConfigurator/MerchConfigurator'
import { getFitFromSKU } from '@components/templates/ProductConfigurator/MerchConfigurator/useMerchConfiguratorCategorySelection'
import { useYouMayAlsoLikeAutoScroll } from '@components/templates/YouMayAlsoLike/useYouMayAlsoLikeAutoScroll'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import { chunkArray } from '@lib/utils/arrayUtils'
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { useRouter } from 'next/router'
import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import s from './YouMayAlsoLike.module.scss'
import { focusController } from 'helpers/AdaHelpers'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

export type BundleAbleProduct = (ConfigurableProduct | SimpleProduct) & {
    firstBundleProduct?: ConfigurableProduct | SimpleProduct
}

type HotspotContent = HotspotOrCalloutProps & {
    product?: BundleAbleProduct
    relatedProducts?: BundleAbleProduct[]
}

export interface YouMayAlsoLikeProps {
    title?: string
    image?: ImageType
    cloudinaryImage?: CloudinaryMedia[]
    mobileImage?: ImageType
    cloudinaryMobileImage?: CloudinaryMedia[]
    textAlignment?: 'left' | 'right'
    hotspotContents?: HotspotContent[]
    theme?: 'light' | 'dark'
    textPanel?: PrimaryTextProps
    id?: string
    cmsATCLocation?: number
    variant?: string
}

export interface YouMayAlsoLikeResponse {
    content: YouMayAlsoLikeProps
    variant?: 'default' | 'card'
    theme?: 'default' | 'neo'
}

export const YouMayAlsoLike: FC<YouMayAlsoLikeResponse> = ({
    content,
    variant = 'default',
    theme
}) => {
    const {
        title,
        textPanel,
        id,
        image,
        cloudinaryImage,
        mobileImage,
        cloudinaryMobileImage,
        hotspotContents: hotspotContentsFromContent = [],
        cmsATCLocation = 1
    } = content
    const hotspotContentsRef = useRef(hotspotContentsFromContent)
    const { locale } = useRouter()
    const imageWrapper = useRef<HTMLDivElement>(null)
    const scrollWrapper = useRef<HTMLDivElement>(null)
    const { isOnScreen } = useOnScreen(scrollWrapper, true, { threshold: 0 })

    /** hotspots */
    const [cardStack, setCardStack] = useState<
        (
            | (SalesPopupCardProps & {
                  product: {
                      firstBundleProduct?: ConfigurableProduct | SimpleProduct
                  }
              })
            | undefined
        )[]
    >([])
    const [activeIndex, setActiveIndex] = useState(-1)
    const [resetAnimation, setResetAnimation] = useState(false)
    const [scrollable, setScrollable] = useState(false)
    const [panelPos, setPanelPos] = useState({ right: 0, top: 0, height: 0 })
    const [fetched, setFetched] = useState(false)
    const [dialogIndex, setDialogIndex] = useState(1)
    const [notSellableFits, setNotSellableFits] = useState<Set<string>>(
        new Set()
    )
    useEffect(() => {
        if (activeIndex !== -1) {
            setDialogIndex(activeIndex)
        }
    }, [activeIndex])
    const { isAnimationStopped } = useAnimationAndVideosToggle()

    useEffect(() => {
        hotspotContentsRef.current = hotspotContentsFromContent
        setCardStack([])
        setActiveIndex(-1)
        setFetched(false)
        setResetAnimation(false)
    }, [title])

    const [isDesktop, setIsDesktop] = useState(false)
    useEffect(() => {
        // Checking if window is defined
        if (typeof window !== 'undefined') {
            setIsDesktop(window.innerWidth >= 768)

            // Adding event listener to update isDesktop state on resize
            const handleResize = () => {
                setIsDesktop(window.innerWidth >= 768)
            }
            window.addEventListener('resize', handleResize)

            return () => {
                // Cleaning up event listener on component unmount
                window.removeEventListener('resize', handleResize)
            }
        }
    }, [])

    useEffect(() => {
        if (
            hotspotContentsRef.current.length > 0 &&
            isOnScreen &&
            locale &&
            !fetched
        ) {
            const skus: string[] = []
            hotspotContentsRef.current.forEach((hotspot) => {
                if (hotspot.sku) {
                    skus.push(hotspot.sku)
                }
            })

            const relatedSkus = [
                ...new Set(
                    hotspotContentsRef.current.flatMap((hotspot) => {
                        return hotspot.relatedSkus ?? []
                    })
                )
            ]
            const skusToFetch = [...skus, ...relatedSkus]
            if (skusToFetch.length) {
                const getProducts = async () => {
                    const arrListSkusChunked = chunkArray(skusToFetch, 30)
                    const fetchedProducts: any[] = []

                    for (const listSkus of arrListSkusChunked) {
                        const getProducts: (
                            | ConfigurableProduct
                            | SimpleProduct
                        )[] = await getProductsBySkus(listSkus, locale ?? '')

                        getProducts.filter((product) => {
                            if (
                                product.__typename === 'ConfigurableProduct' &&
                                product?.variants?.length
                            ) {
                                product?.variants?.filter((variant) => {
                                    if (product.not_sellable) {
                                        const fit =
                                            getFitFromSKU(product?.sku) === 'M'
                                                ? MERCH_CONFIG_FIT.MEN
                                                : MERCH_CONFIG_FIT.WOMEN
                                        setNotSellableFits((prevState) =>
                                            prevState.add(fit)
                                        )
                                    }
                                    const matchSKU = fetchedProducts.find(
                                        (fetchedProduct) =>
                                            fetchedProduct.sku ===
                                            variant?.product?.sku
                                    )
                                    if (!matchSKU)
                                        fetchedProducts.push(variant?.product)
                                })
                            } else {
                                fetchedProducts.push(product)
                            }
                        })
                    }
                    return fetchedProducts
                }

                const bundleSkus: string[] = []
                const hotspotsWithProductAndRelatedProducts: HotspotContent[] =
                    hotspotContentsRef.current
                getProducts().then((products) => {
                    setFetched(true)
                    products.forEach((product) => {
                        const bundles = product.bundle_and_save_skus as string[]
                        if (bundles && bundles.length > 0) {
                            bundleSkus.push(...bundles)
                        }

                        const indexOfHotspotBySku = hotspotsWithProductAndRelatedProducts.findIndex(
                            (hotspot) => hotspot.sku === product.sku
                        )

                        if (indexOfHotspotBySku !== -1) {
                            const updatedHotspot = {
                                ...hotspotsWithProductAndRelatedProducts[
                                    indexOfHotspotBySku
                                ],
                                product: product
                            }
                            hotspotsWithProductAndRelatedProducts[
                                indexOfHotspotBySku
                            ] = updatedHotspot
                            return
                        }

                        const indexOfHotspotByRelatedSku = hotspotsWithProductAndRelatedProducts.findIndex(
                            (hotspot) =>
                                hotspot.relatedSkus?.some(
                                    (sku) => sku === product.sku
                                )
                        )

                        if (indexOfHotspotByRelatedSku !== -1) {
                            const updatedHotspot = {
                                ...hotspotsWithProductAndRelatedProducts[
                                    indexOfHotspotByRelatedSku
                                ],
                                relatedProducts: [
                                    ...(hotspotsWithProductAndRelatedProducts[
                                        indexOfHotspotByRelatedSku
                                    ]?.relatedProducts ?? []),
                                    product
                                ]
                            }
                            hotspotsWithProductAndRelatedProducts[
                                indexOfHotspotByRelatedSku
                            ] = updatedHotspot
                            return
                        }
                    })

                    hotspotContentsRef.current = hotspotsWithProductAndRelatedProducts

                    /*
                        const uniqueSkus = bundleSkus.filter(
                            (val, index, arr) => arr.indexOf(val) === index
                        )
                        if (uniqueSkus.length) {
                            getProductsBySkus(uniqueSkus, locale ?? '').then(
                                (bundleProducts) => {
                                    products.forEach((product) => {
                                        const bundleAbleProduct = product as BundleAbleProduct
                                        bundleAbleProduct.firstBundleProduct = bundleProducts.find(
                                            (el) =>
                                                bundleAbleProduct.bundle_and_save_skus?.includes(
                                                    el.sku?.toString() ?? ''
                                                )
                                        )
                                    })
                                }
                            )
                        }
                        */
                })
            }
        }
    }, [locale, isOnScreen, fetched])

    const updateCardStack = useCallback(
        (index?: number, card?: SalesPopupCardProps) => {
            const content =
                index != null ? hotspotContentsRef.current[index] : undefined
            const newCard =
                card ??
                (content && (content.product || content.textPanel)
                    ? ({
                          product: content.product,
                          relatedProducts: content.relatedProducts,
                          backProduct: undefined,
                          textPanel: content.textPanel,
                          image: content.media
                      } as SalesPopupCardProps)
                    : undefined)

            setCardStack([cardStack[1], newCard])

            setResetAnimation(true)
        },
        [cardStack]
    )

    useEffect(() => {
        if (resetAnimation) {
            setResetAnimation(false)
        }
    }, [resetAnimation])

    const handleResize = () => {
        if (imageWrapper.current && scrollWrapper.current) {
            const imageRect = imageWrapper.current.getBoundingClientRect()
            const scrollRect = scrollWrapper.current.getBoundingClientRect()
            setScrollable(imageRect.width > scrollRect.width)
            const right =
                imageRect.width > scrollRect.width
                    ? 0
                    : scrollRect.right - imageRect.right
            setPanelPos({
                right,
                top:
                    imageWrapper.current.offsetTop +
                    scrollWrapper.current.offsetTop,
                height: imageWrapper.current.clientHeight
            })
        }
    }

    // when the YMAL Panel is in Tabs
    useEffect(() => {
        if (isOnScreen) {
            handleResize()
        }
    }, [isOnScreen])

    const selectHotspot = useCallback(
        (index: number) => {
            // if the parent is hidden, the height of the panel is 0
            // calculate it again to show the card
            if (panelPos.height === 0) {
                handleResize()
            }
            if (index !== activeIndex) {
                updateCardStack(index)
                setActiveIndex(index)
            } else {
                updateCardStack(-1)
                setActiveIndex(-1)
            }
        },
        [activeIndex, panelPos.height, updateCardStack]
    )

    useEffect(() => {
        handleResize()
        window.addEventListener('resize', handleResize)
        return () => window.removeEventListener('resize', handleResize)
    }, [])

    useYouMayAlsoLikeAutoScroll({
        scrollable,
        scrollWrapper,
        activeIndex,
        isOnScreen
    })

    const imageUrl = cloudinaryImage?.[0]?.secure_url ?? image?.file?.url
    const imageWidth =
        cloudinaryImage?.[0]?.width ?? image?.file?.details.image.width
    const imageHeight =
        cloudinaryImage?.[0]?.height ?? image?.file?.details.image.height
    const imageDescription =
        cloudinaryImage?.[0]?.context?.custom?.alt ?? image?.description

    const mobileImageUrl =
        cloudinaryMobileImage?.[0]?.secure_url ?? mobileImage?.file?.url
    const mobileImageWidth =
        cloudinaryMobileImage?.[0]?.width ??
        mobileImage?.file?.details.image.width
    const mobileImageHeight =
        cloudinaryMobileImage?.[0]?.height ??
        mobileImage?.file?.details.image.height
    const mobileImageDescription =
        cloudinaryMobileImage?.[0]?.context?.custom?.alt ??
        mobileImage?.description
    const hotspotClickHandler = (i: number) => {
        selectHotspot(i)
        Promise.resolve().then(() => {
            focusController(
                `#hotspot-${i}`,
                `#dialog-${dialogIndex}`,
                '#sales-popup-card__close'
            )
        })
    }
    const renderHotspotPanel = () => {
        return (
            <div
                className={cn(
                    'z-2 relative mx-auto h-full flex',
                    s['product-hotspot-panel__content'],
                    {
                        [s['product-hotspot-panel__content__neo']]:
                            theme === 'neo'
                    }
                )}
                ref={imageWrapper}
            >
                {imageUrl && !mobileImageUrl && (
                    <ElgatoImage
                        src={imageUrl}
                        alt={imageDescription}
                        width={imageWidth}
                        height={imageHeight}
                        layout="intrinsic"
                        objectFit="cover"
                        className={s['product-hotspot-panel__image']}
                    />
                )}
                {imageUrl && mobileImageUrl && (
                    <div>
                        <div className="block md:hidden">
                            <ElgatoImage
                                src={mobileImageUrl}
                                alt={mobileImageDescription}
                                width={mobileImageWidth}
                                height={mobileImageHeight}
                                layout="fill"
                                objectFit="cover"
                                className={s['product-hotspot-panel__image']}
                            />
                        </div>
                        <div className="hidden md:block">
                            <ElgatoImage
                                src={imageUrl}
                                alt={imageDescription}
                                width={imageWidth}
                                height={imageHeight}
                                layout="fill"
                                objectFit="cover"
                                className={s['product-hotspot-panel__image']}
                            />
                        </div>
                    </div>
                )}

                <div className={cn('h-full w-full absolute left-0 top-0')}>
                    {hotspotContentsRef.current.map((hotspotContent, i) => {
                        const color =
                            hotspotContent.hotspotColor === '#000'
                                ? 'dark'
                                : 'light'
                        const desktopPosition = {
                            position: 'absolute',
                            transform: 'translate(-15px, -15px)',
                            display:
                                hotspotContent.sku && !hotspotContent.product
                                    ? 'none'
                                    : 'flex',
                            alignItems: 'center',
                            ...hotspotContent.position
                        }

                        const mobilePositionStyle = {
                            position: 'absolute',
                            transform: 'translate(-15px, -15px)',
                            display:
                                hotspotContent.sku && !hotspotContent.product
                                    ? 'none'
                                    : 'flex',
                            alignItems: 'center',
                            ...hotspotContent.mobilePosition
                        }
                        const positionStyle = isDesktop
                            ? desktopPosition
                            : hotspotContent.mobilePosition
                            ? mobilePositionStyle
                            : desktopPosition
                        return (
                            <Hotspot
                                product={hotspotContent.product}
                                key={`hotspot-${i}`}
                                id={`hotspot-${i}`}
                                icon={activeIndex === i ? 'minus' : 'add'}
                                // eslint-disable-next-line i18next/no-literal-string
                                color={color || content.theme || 'light'}
                                label={hotspotContent.bigTitle}
                                onClick={() => hotspotClickHandler(i)}
                                eventTracking={hotspotContent.eventTracking}
                                position={positionStyle as React.CSSProperties}
                                customOptions={hotspotContent.customOptions}
                                className="gtm-you-may-also-like-hotspot"
                                haspopup
                                controlId={`dialog-${i}`}
                            />
                        )
                    })}
                </div>
            </div>
        )
    }

    return (
        <div
            className={cn('grid relative', s['product-hotspot-panel'], {
                ['pt-16 md:py-32 md:mx-32px']: variant === 'default',
                [s['product-hotspot-panel--card']]: variant === 'card',
                [s['product-hotspot-panel__neo']]: theme === 'neo'
            })}
            id={id}
        >
            {textPanel && variant === 'default' && (
                <PrimaryText
                    backgroundColor={textPanel?.backgroundColor}
                    bodyCopy={textPanel?.bodyCopy}
                    calloutTitle={textPanel?.calloutTitle}
                    calloutTag={textPanel?.calloutTag}
                    disclaimerText={textPanel?.disclaimerText}
                    headline={textPanel?.headline}
                    headlineStyle={textPanel?.headlineStyle}
                    headlineTag={textPanel?.headlineTag}
                    link={textPanel?.link}
                    richText={textPanel?.richText}
                    smallCopy={textPanel?.smallCopy}
                    subheader={textPanel?.subheader}
                    textAlignment={textPanel?.textAlignment}
                    title={textPanel?.title}
                    className="mb-16"
                />
            )}
            {!textPanel && <div />}
            <div
                className={cn(
                    'relative flex items-center overflow-auto',
                    s['scroll-wrapper'],
                    {
                        [s['scroll-wrapper__neo']]: theme === 'neo'
                    }
                    // theme === 'neo' && 'overflow-hidden w-full h-full'
                )}
                ref={scrollWrapper}
            >
                {theme === 'neo' ? (
                    <section className={cn('w-full')}>
                        <Container size={ContainerSize.XLARGE}>
                            {renderHotspotPanel()}
                        </Container>
                        <Sectioner />
                    </section>
                ) : (
                    renderHotspotPanel()
                )}
            </div>
            {textPanel && variant === 'card' && (
                <InfoBoxText
                    title={textPanel?.headline}
                    text={textPanel?.bodyCopy}
                />
            )}
            <div
                style={{
                    right: `${panelPos.right}px`,
                    top: `${panelPos.top}px`,
                    height: `${panelPos.height}px`
                }}
                className={cn(
                    'absolute w-full z-2 overflow-hidden pointer-events-none',
                    s['product-hotspot-panel__product__wrapper'],
                    {
                        [s['product-hotspot-panel__product__wrapper__neo']]:
                            theme === 'neo'
                    }
                )}
                role="dialog"
                aria-modal="true"
                // eslint-disable-next-line i18next/no-literal-string
                aria-label="overview of a product"
                id={`dialog-${dialogIndex}`}
            >
                {!isDesktop && theme === 'neo' && (
                    <Overlay
                        // eslint-disable-next-line i18next/no-literal-string
                        theme="dark"
                        isOpen={activeIndex !== -1}
                        closable
                        // eslint-disable-next-line i18next/no-literal-string
                        closeButtonStyle="hidden"
                        onClose={() => selectHotspot(-1)}
                        className={
                            s[
                                'product-hotspot-panel__product__wrapper__neo-overlay'
                            ]
                        }
                    >
                        {cardStack?.[1]?.product && (
                            <SalesPopupCard
                                product={cardStack[1].product}
                                textPanel={cardStack[1].textPanel}
                                closeClicked={() => selectHotspot(-1)}
                                theme={theme}
                                className="h-full"
                                backProduct={cardStack[1].backProduct}
                                relatedProducts={cardStack[1].relatedProducts}
                                image={cardStack[1].image}
                                notSellableFits={notSellableFits}
                                cmsATCLocation={cmsATCLocation}
                            />
                        )}
                    </Overlay>
                )}
                {cardStack.map((card, index) => {
                    if (!card) {
                        return undefined
                    }
                    const fade = index === 0 ? 'out' : 'in'
                    return (
                        <div
                            key={`product_${card.product?.sku}`}
                            className={cn(
                                'absolute m-16px top-0 left-0 right-0 bottom-0 pointer-events-auto',
                                s[
                                    `product-hotspot-panel__product--fade-${fade}`
                                ],
                                {
                                    [s[
                                        `product-hotspot-panel__product--fade-${fade}-animate`
                                    ]]: !resetAnimation
                                },
                                {
                                    [(s['product-hotspot-panel__product__neo'],
                                    'hidden md:block')]: theme === 'neo'
                                },
                                {
                                    [s[
                                        'product-hotspot-panel__product--no-animation'
                                    ]]: isAnimationStopped
                                }
                            )}
                        >
                            <SalesPopupCard
                                product={card.product}
                                textPanel={card.textPanel}
                                closeClicked={() => selectHotspot(-1)}
                                // bundleClicked={(bundle) => {
                                //     if (bundle.product) {
                                //         updateCardStack(undefined, {
                                //             product: bundle.product as ProductInterface,
                                //             backProduct: {
                                //                 id: activeIndex,
                                //                 name: card.product!.name!.toString()
                                //             }
                                //         })
                                //
                                //         return
                                //     }
                                //     if (bundle.id != null) {
                                //         updateCardStack(bundle.id)
                                //     }
                                // }}
                                // bundleProduct={card.product?.firstBundleProduct}
                                theme={theme}
                                className="h-full"
                                backProduct={card.backProduct}
                                relatedProducts={card.relatedProducts}
                                image={card.image}
                                notSellableFits={notSellableFits}
                                cmsATCLocation={cmsATCLocation}
                            />
                        </div>
                    )
                })}
            </div>
        </div>
    )
}

export default YouMayAlsoLike
