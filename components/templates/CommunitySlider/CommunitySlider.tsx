import React, { FC } from 'react'
import BazaarVoiceScriptProvider, {
    useBazaarVoiceScriptContext
} from '@components/templates/SocialMediaGallery/BazaarVoiceScriptProvider'
import s from './CommunitySlider.module.scss'
import PrimaryText, {
    HorizontalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { ExternalContentData } from '@components/templates/ExternalContent/ExternalContent'

const CommunitySlider: FC<ExternalContentData> = ({ content }) => {
    const { textPanel, id } = content
    const { isReady } = useBazaarVoiceScriptContext()
    const productId = `productId:'${id}'`
    return (
        <div className={s['community-slider']}>
            <Container size={ContainerSize.LARGE}>
                <div className={s['community-slider__text']}>
                    {textPanel && (
                        <PrimaryText
                            calloutTitle={textPanel.calloutTitle}
                            headline={textPanel.headline}
                            bodyCopy={textPanel.bodyCopy}
                            link={textPanel.link}
                            textAlignment={HorizontalAlignmentEnum.CENTER}
                        />
                    )}
                </div>
                <div
                    className={s['community-slider__gallery']}
                    id="community-slider"
                >
                    <div
                        data-crl8-container-id="product"
                        data-crl8-filter={productId}
                    />
                    <BazaarVoiceScriptProvider />
                </div>
            </Container>
        </div>
    )
}

export default CommunitySlider
