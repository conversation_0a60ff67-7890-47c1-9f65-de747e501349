import s from './StarfieldNotifyForm.module.scss'
import { FormInput } from '@components/molecules/FormInput/FormInput'
import { Button } from '@components/molecules/Button/Button'
import React, { FC, ReactNode, useEffect, useState } from 'react'
import { useTranslation } from 'next-i18next'
import cn from 'classnames'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import { useMediaQuery } from '@config/hooks/useMediaQuery'

interface StarfieldNotifyMeFormProps {
    onSubmit?: (form: FormData) => void
    id?: string
    disclaimer: ReactNode
    headline: string
    bodyCopy: string
    variant?: string
    content: {
        backgroundImage?: ImageType | VideoType | undefined
        backgroundImageMobile?: ImageType | VideoType | undefined
    }
}

const stringNotEmpty = (value: string | null | undefined): boolean => {
    return !(!value || value.trim() === '')
}

const pristineErrorTrigger = (name: string) => {
    document.getElementsByName(name).forEach((el) =>
        // eslint-disable-next-line i18next/no-literal-string
        el.dispatchEvent(new FocusEvent('focusout'))
    )
}

export const StarfieldNotifyMeForm: FC<StarfieldNotifyMeFormProps> = ({
    id,
    content,
    headline,
    bodyCopy,
    disclaimer,
    variant,
    onSubmit
}) => {
    const { t, i18n } = useTranslation('common')
    const isUSA = /-US$/.test(i18n.language)
    const [isSubmitted, setIsSubmitted] = useState(false)
    const [firstNameValue, setFirstNameValue] = useState('')
    const [lastNameValue, setLastNameValue] = useState('')
    const [emailValue, setEmailValue] = useState('')
    const [background, setBackground] = useState('')
    const backgroundUrl = content?.backgroundImage?.file?.url ?? ''
    const backgroundUrlMobile = content?.backgroundImageMobile?.file?.url ?? ''

    const isSizeBased: boolean = useMediaQuery('(min-width: 850px)')

    let customStyle
    switch (variant) {
        case 'facecam-neo-notify-me':
            customStyle = cn(s['starfield-notify-form__form'])
            break
        default:
            customStyle = ''
            break
    }

    useEffect(() => {
        if (isSizeBased) {
            setBackground(backgroundUrl)
        } else {
            if (backgroundUrlMobile) {
                setBackground(backgroundUrlMobile)
            }
            // default to deskto version if mobile not set
            else {
                setBackground(backgroundUrl)
            }
        }
    }, [isSizeBased])

    const firstNameForm = {
        placeholder: `${t('forms|First Name')}*`,
        name: 'firstName',
        errorText: t('forms|Please fill out this field.')
    }

    const lastNameForm = {
        placeholder: `${t('forms|Last Name')}*`,
        name: 'lastName',
        errorText: t('forms|Please fill out this field.')
    }

    const emailForm = {
        placeholder: `${t('forms|Email Address')}*`,
        name: 'email',
        errorText: t('forms|Please fill out this field.')
    }

    const inputValidation: {
        [key: string]: {
            fn: (val: string | undefined) => boolean
            onErr: (name: string) => void
        }
    } = {
        [emailForm.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        },
        [firstNameForm.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        },
        [lastNameForm.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        }
    }

    const isValid = () => {
        return (
            stringNotEmpty(firstNameValue) &&
            stringNotEmpty(lastNameValue) &&
            stringNotEmpty(emailValue)
        )
    }

    const _onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        const currentData = new FormData(event.currentTarget)
        let valid = true
        Object.entries(inputValidation).forEach(([key, validation]) => {
            const inputValid = validation.fn(currentData.get(key)?.toString())
            valid = valid && inputValid
            if (!inputValid) {
                validation.onErr(key)
            }
        })
        if (!valid) {
            event.preventDefault()
        }
        if (onSubmit) {
            event.preventDefault()
            onSubmit(currentData)
            setIsSubmitted(true)
        }
    }

    return (
        <div
            className={`flex flex-col justify-center items-center h-screen gap-32 py-16 md:py-32 px-16px `.concat(
                customStyle
            )}
            id={id}
            style={{
                backgroundImage: `url(${background})`,
                backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'calc(100% - 10px) center'
            }}
        >
            <Container size={ContainerSize.SMALL}>
                <h2 className="text-white text-center">{headline}</h2>
                <p className="text-white text-center text-sub-headline mt-16px">
                    {bodyCopy}
                </p>
            </Container>

            <form
                className="grid md:grid-cols-2 gap-16px w-full max-w-7xl items-start"
                onSubmit={_onSubmit}
                action={undefined}
            >
                <FormInput
                    name={firstNameForm.name}
                    // eslint-disable-next-line i18next/no-literal-string
                    autoComplete="given-name"
                    value={firstNameValue}
                    placeholder={firstNameForm.placeholder}
                    valid={inputValidation[firstNameForm.name].fn(
                        firstNameValue
                    )}
                    onInput={(ev) => setFirstNameValue(ev.target.value)}
                    errorText={firstNameForm.errorText}
                />
                <FormInput
                    name={lastNameForm.name}
                    // eslint-disable-next-line i18next/no-literal-string
                    autoComplete="family-name"
                    value={lastNameValue}
                    placeholder={lastNameForm.placeholder}
                    valid={inputValidation[lastNameForm.name].fn(lastNameValue)}
                    onInput={(ev) => setLastNameValue(ev.target.value)}
                    errorText={lastNameForm.errorText}
                />
                <FormInput
                    name={emailForm.name}
                    // eslint-disable-next-line i18next/no-literal-string
                    autoComplete="email"
                    value={emailValue}
                    type="email"
                    placeholder={emailForm.placeholder}
                    valid={inputValidation[emailForm.name].fn(emailValue)}
                    onInput={(ev) => setEmailValue(ev.target.value)}
                    errorText={emailForm.errorText}
                    className="col-span-full"
                />
                <div className="col-span-full flex justify-center my-24px">
                    <Button
                        disabled={!isValid() || isSubmitted}
                        type="submit"
                        className="flex-shrink-0 md-max:mt-px"
                        label={t('forms|Sign Up')}
                    >
                        {t('forms|Sign Up')}
                    </Button>
                </div>
                {isUSA && isSubmitted && (
                    <b className="col-span-full text-white flex justify-center">
                        {t(
                            "forms|⚠️ You're officially subscribed to our newsletter."
                        )}
                    </b>
                )}
                {!isUSA && isSubmitted && (
                    <b className="col-span-full text-white flex justify-center">
                        {t(
                            'forms|⚠️ Remember to check your inbox to confirm your subscription.'
                        )}
                    </b>
                )}
                <div className="col-span-full text-white text-center flex-wrap gap-4px">
                    {disclaimer}
                </div>
            </form>
        </div>
    )
}
