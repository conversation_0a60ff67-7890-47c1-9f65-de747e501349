import { FormInput } from '@components/molecules/FormInput/FormInput'
import { Button } from '@components/molecules/Button/Button'
import React, { FC, useState } from 'react'
import { useTranslation } from 'next-i18next'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'

interface QuadrantNotifyMeFormProps {
    onSubmit?: (form: FormData) => void
    id?: string
}

const stringNotEmpty = (value: string | null | undefined): boolean => {
    if (!value || value.trim() === '') {
        return false
    }
    return true
}

const pristineErrorTrigger = (name: string) => {
    document.getElementsByName(name).forEach((el) =>
        // eslint-disable-next-line i18next/no-literal-string
        el.dispatchEvent(new FocusEvent('focusout'))
    )
}

export const QuadrantNotifyMeForm: FC<QuadrantNotifyMeFormProps> = ({
    id,
    onSubmit
}) => {
    const { t, i18n } = useTranslation('common')
    const isUSA = /-US$/.test(i18n.language)
    const [isSubmitted, setIsSubmitted] = useState(false)
    const [firstNameValue, setFirstNameValue] = useState('')
    const [lastNameValue, setLastNameValue] = useState('')
    const [emailValue, setEmailValue] = useState('')

    const firstNameForm = {
        placeholder: `${t('forms|First Name')}*`,
        name: 'firstName',
        errorText: t('forms|Please fill out this field.')
    }

    const lastNameForm = {
        placeholder: `${t('forms|Last Name')}*`,
        name: 'lastName',
        errorText: t('forms|Please fill out this field.')
    }

    const emailForm = {
        placeholder: `${t('forms|Email Address')}*`,
        name: 'email',
        errorText: t('forms|Please fill out this field.')
    }

    const disclaimer = [
        `${t('forms|By subscribing, you are agreeing to our')} `,
        <a
            key="privacy-link"
            href="/s/privacy-policy"
            target="_blank"
            rel="noreferrer"
            className="text-content-blue underline"
            aria-details={`${t('forms|Privacy Policy')} - ${t(
                'ada|Opens in a new Tab'
            )}`}
        >
            {t('forms|Privacy Policy')}
        </a>,
        ' & ',
        <a
            key="terms-link"
            href="/s/terms-of-use"
            target="_blank"
            rel="noreferrer"
            className="text-content-blue underline"
            aria-details={`${t('forms|Terms & Conditions')} - ${t(
                'ada|Opens in a new Tab'
            )}`}
        >
            {t('forms|Terms & Conditions')}
        </a>
    ]

    const inputValidation: {
        [key: string]: {
            fn: (val: string | undefined) => boolean
            onErr: (name: string) => void
        }
    } = {
        [emailForm.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        },
        [firstNameForm.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        },
        [lastNameForm.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        }
    }

    const isValid = () => {
        return (
            stringNotEmpty(firstNameValue) &&
            stringNotEmpty(lastNameValue) &&
            stringNotEmpty(emailValue)
        )
    }

    const _onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        const currentData = new FormData(event.currentTarget)
        let valid = true
        Object.entries(inputValidation).forEach(([key, validation]) => {
            const inputValid = validation.fn(currentData.get(key)?.toString())
            valid = valid && inputValid
            if (!inputValid) {
                validation.onErr(key)
            }
        })
        if (!valid) {
            event.preventDefault()
        }
        if (onSubmit) {
            event.preventDefault()
            onSubmit(currentData)
            setIsSubmitted(true)
        }
    }

    return (
        <div
            className="bg-white-smoke flex flex-col justify-center items-center gap-32 py-16 md:py-32 px-16px"
            id={id}
        >
            <Container size={ContainerSize.SMALL}>
                <h1 className="text-center">
                    {t('forms|quadrant-notify-me_headline')}
                </h1>
            </Container>

            <form
                className="grid md:grid-cols-2 gap-16px w-full max-w-7xl items-start"
                onSubmit={_onSubmit}
                action={undefined}
            >
                <FormInput
                    name={firstNameForm.name}
                    // eslint-disable-next-line i18next/no-literal-string
                    autoComplete="given-name"
                    value={firstNameValue}
                    placeholder={firstNameForm.placeholder}
                    valid={inputValidation[firstNameForm.name].fn(
                        firstNameValue
                    )}
                    onInput={(ev) => setFirstNameValue(ev.target.value)}
                    errorText={firstNameForm.errorText}
                />
                <FormInput
                    name={lastNameForm.name}
                    // eslint-disable-next-line i18next/no-literal-string
                    autoComplete="family-name"
                    value={lastNameValue}
                    placeholder={lastNameForm.placeholder}
                    valid={inputValidation[lastNameForm.name].fn(lastNameValue)}
                    onInput={(ev) => setLastNameValue(ev.target.value)}
                    errorText={lastNameForm.errorText}
                />
                <FormInput
                    name={emailForm.name}
                    // eslint-disable-next-line i18next/no-literal-string
                    autoComplete="email"
                    value={emailValue}
                    type="email"
                    placeholder={emailForm.placeholder}
                    valid={inputValidation[emailForm.name].fn(emailValue)}
                    onInput={(ev) => setEmailValue(ev.target.value)}
                    errorText={emailForm.errorText}
                    className="col-span-full"
                />
                <div className="col-span-full flex justify-center my-24px">
                    <Button
                        disabled={!isValid() || isSubmitted}
                        type="submit"
                        className="flex-shrink-0 md-max:mt-px"
                        label={t('forms|Notify Me')}
                    >
                        {t('forms|Notify Me')}
                    </Button>
                </div>
                {isUSA && isSubmitted && (
                    <b className="col-span-full flex justify-center">
                        {t(
                            "forms|⚠️ You're officially subscribed to our newsletter."
                        )}
                    </b>
                )}
                {!isUSA && isSubmitted && (
                    <b className="col-span-full flex justify-center">
                        {t(
                            'forms|⚠️ Remember to check your inbox to confirm your subscription.'
                        )}
                    </b>
                )}
                <div className="col-span-full text-center flex-wrap gap-4px">
                    {disclaimer}
                </div>
            </form>
        </div>
    )
}
