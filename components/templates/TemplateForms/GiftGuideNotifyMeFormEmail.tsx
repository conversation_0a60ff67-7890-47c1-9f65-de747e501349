import { FormInput } from '@components/molecules/FormInput/FormInput'
import { Button } from '@components/molecules/Button/Button'
import React, { FC, useState } from 'react'
import { useTranslation } from 'next-i18next'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import cn from 'classnames'

interface GiftGuideNotifyMeFormEmailProps {
    onSubmit?: (form: FormData) => void
    id?: string
    headline: string
    body: string
    theme?: 'dark' | 'light'
}

const stringNotEmpty = (value: string | null | undefined): boolean => {
    if (!value || value.trim() === '') {
        return false
    }
    return true
}

const pristineErrorTrigger = (name: string) => {
    document.getElementsByName(name).forEach((el) =>
        // eslint-disable-next-line i18next/no-literal-string
        el.dispatchEvent(new FocusEvent('focusout'))
    )
}

export const GiftGuideNotifyMeFormEmail: FC<GiftGuideNotifyMeFormEmailProps> = ({
    id,
    onSubmit,
    headline,
    body,
    theme
}) => {
    const { t, i18n } = useTranslation('common')
    const isUSA = /-US$/.test(i18n.language)
    const [isSubmitted, setIsSubmitted] = useState(false)
    const [emailValue, setEmailValue] = useState('')

    const emailForm = {
        placeholder: `${t('forms|Email Address')}*`,
        name: 'email',
        errorText: t('forms|Please fill out this field.')
    }

    const disclaimer = [
        `${t('forms|By subscribing to alerts, you accept Elgato’s')} `,
        <a
            key="privacy-link"
            href="/s/privacy-policy"
            target="_blank"
            rel="noreferrer"
            className={cn(
                theme === 'dark'
                    ? 'text-content-blue underline'
                    : 'text-content-blue underline'
            )}
        >
            {t('forms|Privacy Policy')}
        </a>,
        ' & ',
        <a
            key="terms-link"
            href="/s/terms-of-use"
            target="_blank"
            rel="noreferrer"
            className={cn(
                theme === 'dark'
                    ? 'text-content-blue underline'
                    : 'text-content-blue underline'
            )}
        >
            {t('forms|Terms & Conditions')}
        </a>
    ]

    const inputValidation: {
        [key: string]: {
            fn: (val: string | undefined) => boolean
            onErr: (name: string) => void
        }
    } = {
        [emailForm.name]: {
            fn: stringNotEmpty,
            onErr: pristineErrorTrigger
        }
    }

    const isValid = () => {
        return stringNotEmpty(emailValue)
    }

    const _onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
        const currentData = new FormData(event.currentTarget)
        let valid = true
        Object.entries(inputValidation).forEach(([key, validation]) => {
            const inputValid = validation.fn(currentData.get(key)?.toString())
            valid = valid && inputValid
            if (!inputValid) {
                validation.onErr(key)
            }
        })
        if (!valid) {
            event.preventDefault()
        }
        if (onSubmit) {
            event.preventDefault()
            onSubmit(currentData)
            setIsSubmitted(true)
        }
    }

    return (
        <div
            className="bg-primitive-white flex flex-col justify-center items-center gap-32 md:gap-16 py-16 md:pb-32 px-16px relative z-1"
            id={id}
        >
            <Container size={ContainerSize.SMALL}>
                <h1 className="text-black text-center">{headline}</h1>
                <p
                    className="black text-center body-copy mt-16px"
                    dangerouslySetInnerHTML={{ __html: body }}
                />
            </Container>
            <form
                className="grid grid-cols-3 md:grid-cols-5 auto-rows-min gap-16px w-full max-w-7xl items-start"
                onSubmit={_onSubmit}
                action={undefined}
            >
                <FormInput
                    name={emailForm.name}
                    // eslint-disable-next-line i18next/no-literal-string
                    autoComplete="email"
                    value={emailValue}
                    type="email"
                    placeholder={emailForm.placeholder}
                    valid={inputValidation[emailForm.name].fn(emailValue)}
                    onInput={(ev) => setEmailValue(ev.target.value)}
                    errorText={emailForm.errorText}
                    className="col-span-2 md:col-span-4"
                />
                <div>
                    <Button
                        disabled={!isValid() || isSubmitted}
                        type="submit"
                        className="w-full"
                        variant="primary"
                        style={{ display: 'flex', justifyContent: 'center' }}
                    >
                        {t('forms|Sign Up')}
                    </Button>
                </div>
                {isUSA && isSubmitted && (
                    <b className="col-span-full flex justify-center black">
                        {t(
                            "forms|⚠️ You're officially subscribed to our newsletter."
                        )}
                    </b>
                )}
                {!isUSA && isSubmitted && (
                    <b className="col-span-full flex justify-center black">
                        {t(
                            'forms|⚠️ Remember to check your inbox to confirm your subscription.'
                        )}
                    </b>
                )}
                <div className="col-span-full black text-center flex-wrap gap-4px">
                    {disclaimer}
                </div>
            </form>
        </div>
    )
}
