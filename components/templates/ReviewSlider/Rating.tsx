import { Icon } from '@components/atoms/Icon/Icon'
import classNames from 'classnames'
import { FC } from 'react'

const RATING_FULL = 'ratingStarFull'
const RATING_HALF = 'ratingStarHalf'
const RATING_EMPTY = 'ratingStarEmpty'

type Props = {
    rating: number
    color?: string
    className?: string
}

export const Rating: FC<Props> = ({ rating, color = '#D3721D', className }) => {
    return (
        <div
            className={classNames(
                'flex justify-center items-center',
                className
            )}
            style={{ color }}
        >
            {new Array(5).fill(null).map((_, i) => (
                <Icon
                    key={i}
                    name={
                        i + 1 <= rating
                            ? RATING_FULL
                            : i + 0.25 <= rating && i + 0.75 > rating
                            ? RATING_HALF
                            : RATING_EMPTY
                    }
                />
            ))}
        </div>
    )
}
