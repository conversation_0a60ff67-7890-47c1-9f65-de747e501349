import React, { FC } from 'react'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import TextPanel from '@components/organisms/TextPanel/TextPanel'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import { SectionBgColor } from '@components/templates/Section/Section'
import { AutoScroll } from '@splidejs/splide-extension-auto-scroll'
import cn from 'classnames'
import { Splide, SplideSlide } from '@splidejs/react-splide'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { Logo } from '@components/atoms/Logo/Logo'
import s from './ReviewSlider.module.scss'
import { Review } from '@components/templates/ReviewSlider/Review'
import { Rating } from '@components/templates/ReviewSlider/Rating'

export type QuoteResponse = {
    image?: ImageType
    cloudinaryMedia?: CloudinaryMedia[]
    title?: string
    headline?: string
    text?: string
    className?: string
    url?: string
    newTab?: boolean
    logoImage?: ImageType
}

interface ReviewSliderProps {
    textPanel?: PrimaryTextProps
    reviews?: QuoteResponse[]
    id?: string
    bgColor?: SectionBgColor
}

export const ReviewSlider: FC<ReviewSliderProps> = ({
    textPanel,
    reviews,
    id,
    bgColor = SectionBgColor.WHITE_SMOKE
}) => {
    let theme: string | null = null
    if (bgColor === 'bg-black') {
        theme = 'light'
    }

    if (!textPanel) {
        return null
    }

    const { logos, link, ...restTextPanelProps } = textPanel
    const overallReview = textPanel?.badgeText

    return (
        <div
            className={cn(s['review-slider'], 'py-16 md:py-32x', bgColor)}
            id={id}
        >
            {textPanel && (
                <TextPanel
                    content={{ ...restTextPanelProps, noPaddingBottom: true }}
                />
            )}
            <div className="flex justify-center items-center flex-col gap-16px pb-40px pt-16px">
                {logos && (
                    <Logo
                        name={logos[0]}
                        className={s['review-slider__logo']}
                    />
                )}
                <div className="flex items-center gap-8px">
                    <Rating rating={parseFloat(overallReview || '0')} />
                    {/* <div style={{ color: '#D3721D' }}>{overallReview}</div> */}
                    <div className="text-small-copy">{link?.linkTitle}</div>
                    {/* {link && (
                        <Link
                            link={link}
                            className={s['review-slider__mainlink']}
                        >
                            {link.linkTitle}
                        </Link>
                    )} */}
                </div>
            </div>
            {reviews && reviews.length > 0 && (
                <Splide
                    options={{
                        type: 'loop',
                        drag: 'free',
                        focus: 'center',
                        perPage: 7,
                        breakpoints: {
                            1920: {
                                perPage: 5
                            },
                            1440: {
                                perPage: 4
                            },
                            1024: {
                                perPage: 3
                            },
                            768: {
                                perPage: 2
                            }
                        },
                        arrows: false,
                        autoScroll: {
                            speed: 0.5
                        }
                    }}
                    extensions={{ AutoScroll }}
                >
                    {reviews.map((review, i) => (
                        <SplideSlide key={i}>
                            <Review
                                key={i}
                                cloudinaryMedia={review.cloudinaryMedia}
                                rating={review.headline}
                                text={review.text}
                                theme={theme}
                            />
                        </SplideSlide>
                    ))}
                </Splide>
            )}
        </div>
    )
}
export default ReviewSlider
