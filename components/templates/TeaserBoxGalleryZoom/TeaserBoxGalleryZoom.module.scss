$textPadding: 17px;

.teaser-box-gallery-zoom {
    --panelHeight: 400vh;
    --cards: 4;
    position: relative;
    height: var(--panelHeight);

    &--sticky-nav {
        .teaser-box-gallery-zoom__sticky {
            padding-top: calc(var(--sticky-nav-height--sm) + #{$textPadding});

            @screen md {
                padding-top: calc(var(--sticky-nav-height--lg) + #{$textPadding});
            }
        }

        .teaser-box-gallery-zoom__text-wrapper,
        .teaser-box-gallery-zoom__animation-wrapper {
            height: calc(100vh - var(--sticky-nav-height--sm) - #{$textPadding});

            @screen md {
                height: calc(100vh - var(--sticky-nav-height--lg) - #{$textPadding});
            }
        }
    }

    &__sticky {
        position: sticky;
        left: 0;
        width: 100%;
        top: 0;
        overflow: hidden;
        padding-bottom: 80px;
    }

    &__text-wrapper {
        height: 100vh;

        @screen lg {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    &__headline {
        @apply font-univers67BoldCondensed;
        /*
            https://css-tricks.com/snippets/css/fluid-typography/
            we want the our font size in a range where 40px is the minimum size at the smallest viewport width of 375
            and where 200px is the maximum size at the largest viewport width of 2068px (1920 + 64 + 64 = 2068), then our equation looks like this:
         */
        font-size: calc(40px + (200 - 40) * ((100vw - 375px) / (2068 - 375)));
        line-height: calc(40px + (200 - 40) * ((100vw - 375px) / (2068 - 375)));
        text-align: center;
        text-transform: uppercase;
    }

    &__animation-wrapper {
        position: absolute;
        top: calc(var(--sticky-nav-height--sm) + #{$textPadding});
        width: 100%;
        max-width: 1920px;
        margin: 0 auto;
        height: 100vh;
        left: 0;
        right: 0;

        @screen lg-max {
            width: auto;
        }

        @screen md {
            top: calc(var(--sticky-nav-height--lg) + #{$textPadding});
        }

        @screen lg {
            padding-left: var(--container-padding--lg);
            padding-right: var(--container-padding--lg);
        }
    }

    &__helper-img {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0.7;
    }
}

.teaser-box-gallery-zoom-cards {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(calc(33vw * var(--cards)), 0);
    will-change: transform;
    gap: 45px;
    margin-top: 40px;

    @screen md {
        transform: translate(calc(25vw * var(--cards)), 0);
    }

    @screen lg {
        margin-top: 0;
        transform: translate(85%, 0);
    }
}

.teaser-box-gallery-zoom-card {
    aspect-ratio: 9 / 16;
    /* max-width: calc(100% / var(--cards)); */
    max-width: 75vw;
    will-change: transform;
    box-sizing: border-box;
    transform-origin: center center;
    flex: 0 0 auto;

    @screen lg-max {
        max-height: 55vh;
    }

    @screen lg {
        max-width: 21.5%;
    }

    &__inner {
        height: 100%;
        width: 100%;
        border-radius: 12px;
        overflow: hidden;
        will-change: transform, opacity;
        box-sizing: border-box;
        transform-origin: center center;
        transform: translate3d(0px, 0px, 0px) rotateX(0deg) rotateY(0deg) scale(1);
        -webkit-transition: height 500ms cubic-bezier(.5, .5, 0, 1), width 500ms ease, color 1000ms cubic-bezier(.5, .5, 0, 1);
        transition: height 500ms cubic-bezier(.5, .5, 0, 1), width 500ms ease, color 1000ms cubic-bezier(.5, .5, 0, 1);
    }

    video {
        object-fit: cover;
        border-radius: 12px;
        overflow: hidden;
        z-index: 1;
        position: relative;
        width: 100%;
        height: 100%;
    }

    &__video {
        width: 100%;
        height: 100%;
    }

    &__text {
        width: calc(100% - 64px);
        position: absolute;
        bottom: 32px;
        top: auto;
        left: 32px;
        right: 32px;
        z-index: 2;

        h2 {
            text-shadow: 0px 4px 12px rgba(0, 0, 0, 0.25);
            font-size: 2.5em;
        }
    }

    &:first-child, &:nth-child(5n) {
        transform: rotate(8deg) translate3d(0px, 0px, 0px) translate(27%, 0px) scale(1);
    }
    &:nth-child(2n) {
        transform: rotate(-10deg) translate3d(0px, 0px, 0px) translate(27%, 0px) scale(1);
    }
    &:nth-child(3n) {
        transform: rotate(-1deg) translate3d(0px, 0px, 0px) translate(32%, 0px) scale(1);
    }
    &:nth-child(4n) {
        transform: rotate(12deg) translate3d(0px, 0px, 0px) translate(56%, 0px) scale(1);
    }

    /* for safari we need this solution... */
    &__poster {
        will-change: z-index;
        transition: z-index 0.3s ease;
        position: relative;
        z-index: 1;
        pointer-events: none;
        width: 100%;
        height: 100%;
        border-radius: 12px;
        &--active {
            z-index: 0;
        }
    }
}
