import ChevronRight from '@components/icons/ChevronRight'
import { Button } from '@components/molecules/Button/Button'
import {
    Dropdown,
    DropdownLabelValue
} from '@components/molecules/Dropdown/Dropdown'
import { LinkResponse } from '@components/molecules/Link/Link'
import { ProductAddToCart } from '@components/molecules/ProductAddToCart/ProductAddToCart'
import { ProductPrice } from '@components/molecules/ProductPrice/ProductPrice'
import CompareProductCategorySection from '@components/templates/CompareProducts/components/CompareProductCategorySection/CompareProductCategorySection'
import { ProductName } from '@components/templates/CompareProducts/components/CompareProductList/CompareProductList'
import { usePrice } from '@corsairitshopify/pylot-price'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC, useEffect, useState } from 'react'
import s from './CompareProduct.module.scss'
import { PageTheme } from '@components/layouts/MainLayout/LayoutContext'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import ElgatoImage from '@components/common/ElgatoImage'
import { useMedia } from '@lib/hooks/useMedia'
import { useMobile } from '@pylot-data/hooks/use-mobile'
interface ProductProps {
    link: LinkResponse
    productCategory: any
    sku: string
    media: ImageType
    bodyCopy?: string
    hideBuyButton?: boolean
    cloudinaryMedia?: CloudinaryMedia[]
    hasDropdown?: boolean
    productName?: string
}

interface ProductContent {
    indexOfProduct: number
    content: ProductProps
    productNames?: ProductName[]
    productData: SimpleProduct | ConfigurableProduct | undefined
    onChangeProduct: (sku: any, index: number) => void
    pageTheme: PageTheme
    keyCountDropdownValue?: string
    onKeyCountDropdownChange?: (keyCount: string, columnIndex: number) => void
}

const CompareProduct: FC<ProductContent> = ({
    indexOfProduct,
    content,
    productNames,
    productData,
    onChangeProduct,
    pageTheme,
    keyCountDropdownValue,
    onKeyCountDropdownChange
}) => {
    const { t } = useTranslation(['common'])
    const { productCategory } = content
    const { linkTitle, linkUrl } = content.link
    const { subtotal, total } = usePrice(productData?.price_range)
    const bodyCopy = content?.bodyCopy
    const hideBuyButton = content?.hideBuyButton
    const hasDropdown = content?.hasDropdown ?? true
    const { src, alt, width, height } = useMedia({
        media: content?.media,
        cloudinaryMedia: content?.cloudinaryMedia
    })
    const productImage = productData?.image?.url
    const media = src ? src : productImage
    const displayName = productData?.name || ''
    const { isMobile } = useMobile()

    const renderDropdown = () => {
        const currentProductOption = productNames?.find(
            (p) => p.value === productData?.sku
        )
            ? {
                  label:
                      productNames?.find((p) => p.value === productData?.sku)
                          ?.label || '',
                  value:
                      productNames?.find((p) => p.value === productData?.sku)
                          ?.value || ''
              }
            : undefined

        if (hasDropdown) {
            return (
                <div className={s['compare-product__dropdown__wrapper']}>
                    <Dropdown
                        title={displayName}
                        currentOption={currentProductOption}
                        variant="primary"
                        /* eslint-disable-next-line i18next/no-literal-string */
                        buttonColor={
                            pageTheme === 'dark' ? 'dark-grey' : 'gray-10'
                        }
                        className={cn(
                            s['compare-product__dropdown__wrapper__dropdown'],
                            {
                                [s[
                                    'compare-product__dropdown__wrapper__dropdown--dark'
                                ]]: pageTheme === 'dark'
                            },
                            {
                                [s[
                                    'compare-product__dropdown__wrapper__dropdown--light'
                                ]]: pageTheme !== 'dark'
                            }
                        )}
                        options={[
                            {
                                title: '',
                                options: productNames as DropdownLabelValue[]
                            }
                        ]}
                        onChange={(e) => onChangeProduct(e, indexOfProduct)}
                    />
                </div>
            )
        } else {
            return (
                <>
                    {!isMobile && (
                        <div
                            className={cn(s['compare-product__name'], {
                                [s['compare-product__name--light']]:
                                    pageTheme !== 'dark',
                                [s['compare-product__name--dark']]:
                                    pageTheme === 'dark'
                            })}
                        >
                            <div className="text-center mt-16px text-sub-headline-md-max md-max:mt-4 overflow-hidden whitespace-nowrap overflow-ellipsis">
                                {productData?.name}
                            </div>
                        </div>
                    )}
                    {isMobile && (
                        <div
                            className={s['compare-product__dropdown__wrapper']}
                        >
                            <Dropdown
                                title={displayName}
                                currentOption={currentProductOption}
                                variant="primary"
                                /* eslint-disable-next-line i18next/no-literal-string */
                                buttonColor={
                                    pageTheme === 'dark'
                                        ? 'dark-grey'
                                        : 'gray-10'
                                }
                                className={cn(
                                    s[
                                        'compare-product__dropdown__wrapper__dropdown'
                                    ],
                                    {
                                        [s[
                                            'compare-product__dropdown__wrapper__dropdown--dark'
                                        ]]: pageTheme === 'dark'
                                    },
                                    {
                                        [s[
                                            'compare-product__dropdown__wrapper__dropdown--light'
                                        ]]: pageTheme !== 'dark'
                                    }
                                )}
                                options={[
                                    {
                                        title: '',
                                        options: productNames as DropdownLabelValue[]
                                    }
                                ]}
                                onChange={(e) =>
                                    onChangeProduct(e, indexOfProduct)
                                }
                            />
                        </div>
                    )}
                </>
            )
        }
    }

    const renderPriceAndAddToCart = () => {
        if (
            productData &&
            subtotal &&
            parseFloat(subtotal.replace(/[^\d.-]/g, '')) !== 0 &&
            total &&
            parseFloat(total.replace(/[^\d.-]/g, ''))
        ) {
            return (
                <ProductPrice
                    className={s['compare-product__product-price-wrapper']}
                    product={productData}
                    /* eslint-disable-next-line i18next/no-literal-string */
                    theme={pageTheme === 'dark' ? 'light' : 'dark'}
                    inNav={false}
                />
            )
        } else {
            return <div className={s['compare-product__empty-price-wrapper']} />
        }
    }

    const renderLink = () => {
        if (linkTitle && linkUrl) {
            return (
                <div
                    className={cn(
                        s['compare-product__link__wrapper'],
                        'text-center'
                    )}
                >
                    <Button
                        variant="tertiary"
                        /* eslint-disable-next-line i18next/no-literal-string */
                        color={pageTheme === 'dark' ? 'light' : 'dark'}
                        href={linkUrl}
                        label={linkTitle}
                    >
                        {linkTitle}
                        <ChevronRight />
                    </Button>
                </div>
            )
        }
        return null
    }

    const renderProductContent = () => (
        <div className="mb-40px flex flex-col">
            {productData && (
                <div className={cn(s['compare-product__media'])}>
                    <ElgatoImage
                        src={media || ''}
                        alt={alt}
                        width={width}
                        height={height}
                        objectFit="cover"
                    />
                </div>
            )}
            {productData && renderDropdown()}
            {bodyCopy && (
                <div
                    className={cn(
                        s['compare-product__body-copy'],
                        'text-center text-h6-md-max text-primitive-gray-100 mt-4px'
                    )}
                >
                    {bodyCopy}
                </div>
            )}
            {/* {hideBuyButton && (
                <div
                    className={cn(
                        s['compare-product__learn-more-wrapper'],
                        'flex justify-center pt-16px'
                    )}
                >
                    <Button
                        variant="tertiary"
                        href={linkUrl}
                        color={pageTheme === 'dark' ? 'light' : 'dark'}
                        label={linkTitle}
                        className={cn(
                            s['compare-product__learn-more-wrapper__button']
                        )}
                    >
                        {linkTitle}
                        <ChevronRight />
                    </Button>
                </div>
            )} */}

            {productData && (
                <div
                    className={cn(
                        'flex justify-center items-center gap-4 flex-col lg:flex-row lg:gap-10',
                        s['compare-product__price-and-buy'],
                        {
                            'mt-40px': hideBuyButton
                        },
                        {
                            'my-16px': !hideBuyButton
                        }
                    )}
                >
                    {renderPriceAndAddToCart()}
                </div>
            )}
            {productData && !hideBuyButton && (
                <div
                    className={cn(
                        s['compare-product__price-and-buy__wrapper'],
                        'flex items-center md-max:flex-col justify-center gap-12px md:gap-24px'
                    )}
                >
                    <ProductAddToCart
                        id={`compare-product-atc-btn-${productData.uid}`}
                        product={productData}
                        buttonLabel={t('Buy')}
                    />
                    {renderLink()}
                </div>
            )}
        </div>
    )

    return (
        <div className={cn(s['compare-product'])}>
            {renderProductContent()}
            {productCategory?.map((category: any, index: number) => (
                <CompareProductCategorySection
                    productCategory={category}
                    key={index}
                    columnIndex={indexOfProduct}
                    keyCountDropdownValue={keyCountDropdownValue}
                    onKeyCountDropdownChange={onKeyCountDropdownChange}
                    productSku={content.sku}
                />
            ))}
        </div>
    )
}

export default React.memo(CompareProduct)
