import { Products } from '@pylot-data/api/operations/get-products'

/**
 * Returns an arranged array by the order of products sku array
 * @param {Products} productsData - data for products
 * @param {string[]} skus - data for skus
 * Example: productsData [1,3,2,4], skus [1,2,3,4] => returns [1,2,3,4]
 */

const arrangeProductsBySkus = (productsData: Products[], skus: string[]) => {
    return skus.map((sku) =>
        productsData.find((item) => item.productSku === sku)
    )
}

export default arrangeProductsBySkus
