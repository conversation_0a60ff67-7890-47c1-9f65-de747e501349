import { FC, SVGProps } from 'react'

export const ElgatoWeekLogoPopup: FC<SVGProps<SVGSVGElement>> = (props) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="12"
            viewBox="0 0 72 12"
            fill="none"
            {...props}
        >
            <path
                d="M4.80609 0.837443V1.34309C4.80609 1.42287 4.74104 1.48669 4.6625 1.48669H1.0837C1.00393 1.48669 0.940109 1.55174 0.940109 1.63029V5.31099C0.940109 5.39076 1.00516 5.45458 1.0837 5.45458H4.48454C4.56431 5.45458 4.62813 5.51963 4.62813 5.59818V6.10383C4.62813 6.18361 4.56309 6.24743 4.48454 6.24743H1.0837C1.00393 6.24743 0.940109 6.31247 0.940109 6.39102V10.3651C0.940109 10.4448 1.00516 10.5086 1.0837 10.5086H4.76436C4.84414 10.5086 4.90796 10.5737 4.90796 10.6522V11.1579C4.90796 11.2377 4.84291 11.3015 4.76436 11.3015H0.143594C0.0638194 11.3015 0 11.2364 0 11.1579V0.837443C0 0.757668 0.0638194 0.693848 0.143594 0.693848H4.66127C4.74104 0.693848 4.80486 0.758895 4.80486 0.837443H4.80609Z"
                fill="currentColor"
            />
            <path
                d="M5.65748 11.1603V0.837443C5.65748 0.757668 5.72252 0.693848 5.80107 0.693848H6.45399C6.53377 0.693848 6.59759 0.758895 6.59759 0.837443V10.3675C6.59759 10.4473 6.66263 10.5111 6.74118 10.5111H9.96529C10.0451 10.5111 10.1089 10.5761 10.1089 10.6547V11.1603C10.1089 11.2401 10.0438 11.3039 9.96529 11.3039H5.79984C5.72007 11.3039 5.65625 11.2389 5.65625 11.1603H5.65748Z"
                fill="currentColor"
            />
            <path
                d="M15.5669 6.55783H13.7088C13.629 6.55783 13.5652 6.49278 13.5652 6.41423V5.90858C13.5652 5.8288 13.6302 5.76498 13.7088 5.76498H16.507C16.5868 5.76498 16.6506 5.83003 16.6506 5.90858V10.9626C16.6506 11.0265 16.6089 11.0829 16.5475 11.1013C15.6393 11.3738 14.698 11.5113 13.726 11.5113C13.1479 11.5113 12.668 11.4229 12.2851 11.2462C11.9022 11.0694 11.5941 10.835 11.3597 10.5404C11.1253 10.2459 10.9486 9.90838 10.8308 9.52669C10.7129 9.145 10.6319 8.7498 10.5878 8.34356C10.5436 7.93732 10.5215 7.52986 10.5215 7.12362V4.87518C10.5215 4.46894 10.5436 4.06148 10.5878 3.65524C10.6319 3.249 10.7129 2.8538 10.8308 2.47211C10.9486 2.09042 11.1241 1.75168 11.3597 1.45835C11.5954 1.16502 11.9034 0.929381 12.2851 0.752648C12.6668 0.575915 13.1467 0.487549 13.726 0.487549C14.7348 0.487549 15.4969 0.718283 16.0112 1.17852C16.5009 1.6179 16.7488 2.31379 16.7549 3.26863C16.7549 3.34841 16.6899 3.41346 16.6101 3.41346H15.9547C15.8762 3.41346 15.8136 3.35086 15.8111 3.27232C15.7903 2.63657 15.6295 2.15301 15.3288 1.81918C15.006 1.46203 14.4709 1.28285 13.7272 1.28285C13.1491 1.28285 12.7085 1.40312 12.4042 1.64245C12.0998 1.883 11.8776 2.21315 11.7353 2.63411C11.5929 3.05508 11.5119 3.5546 11.4923 4.13266C11.4726 4.71072 11.4628 5.33297 11.4628 5.9994C11.4628 6.66583 11.4726 7.28808 11.4923 7.86614C11.5119 8.4442 11.5929 8.94372 11.7353 9.36469C11.8776 9.78565 12.0998 10.117 12.4042 10.3564C12.7073 10.5969 13.1491 10.716 13.7272 10.716C14.1383 10.716 14.509 10.689 14.8367 10.635C15.1251 10.5883 15.3853 10.5233 15.6172 10.4423C15.6749 10.4226 15.7117 10.3674 15.7117 10.306V6.70019C15.7117 6.62042 15.6467 6.5566 15.5681 6.5566L15.5669 6.55783Z"
                fill="currentColor"
            />
            <path
                d="M21.7536 0.794487L24.9703 11.1174C24.9998 11.2107 24.9298 11.3039 24.8328 11.3039H24.1505C24.0866 11.3039 24.0314 11.2622 24.013 11.2021L23.2226 8.61368C23.2042 8.55354 23.1478 8.51181 23.0852 8.51181H19.1394C19.0756 8.51181 19.0191 8.55354 19.002 8.61491L18.2263 11.2021C18.2079 11.2634 18.1514 11.3052 18.0888 11.3052H17.4519C17.3549 11.3052 17.285 11.2107 17.3144 11.1174L20.5729 0.794487C20.5913 0.734349 20.6478 0.693848 20.7104 0.693848H21.6161C21.6787 0.693848 21.7351 0.734349 21.7536 0.794487ZM22.7477 7.71897C22.8446 7.71897 22.9133 7.6257 22.8851 7.53365L21.1338 1.72233H21.1043L19.3665 7.53365C19.3382 7.6257 19.4082 7.71897 19.5039 7.71897H22.7464H22.7477Z"
                fill="currentColor"
            />
            <path
                d="M26.1035 1.48669H23.8047C23.725 1.48669 23.6611 1.42164 23.6611 1.34309V0.837443C23.6611 0.757668 23.7262 0.693848 23.8047 0.693848H29.6307C29.7105 0.693848 29.7743 0.758895 29.7743 0.837443V1.34309C29.7743 1.42287 29.7092 1.48669 29.6307 1.48669H27.332C27.2522 1.48669 27.1884 1.55174 27.1884 1.63029V11.1603C27.1884 11.2401 27.1233 11.3039 27.0448 11.3039H26.3919C26.3121 11.3039 26.2483 11.2389 26.2483 11.1603V1.63151C26.2483 1.55174 26.1832 1.48792 26.1047 1.48792L26.1035 1.48669Z"
                fill="currentColor"
            />
            <path
                d="M30.1123 5.9994C30.1123 5.51952 30.1172 5.05192 30.127 4.59536C30.1369 4.14002 30.1737 3.70556 30.2375 3.29441C30.3013 2.88326 30.397 2.50525 30.5234 2.16283C30.6511 1.82041 30.834 1.5234 31.0745 1.27425C31.3151 1.02388 31.6157 0.831196 31.979 0.693737C32.3411 0.556278 32.7878 0.487549 33.3168 0.487549C33.8457 0.487549 34.2912 0.556278 34.6545 0.693737C35.0166 0.831196 35.3185 1.02388 35.559 1.27425C35.7996 1.52463 35.9825 1.82041 36.1101 2.16283C36.2377 2.50647 36.3322 2.88326 36.3961 3.29441C36.4599 3.70556 36.4967 4.14002 36.5065 4.59536C36.5163 5.05069 36.5212 5.51952 36.5212 5.9994C36.5212 6.47928 36.5163 6.94688 36.5065 7.40344C36.4967 7.85877 36.4599 8.29202 36.3961 8.70439C36.3322 9.11554 36.2365 9.49355 36.1101 9.83597C35.9825 10.1796 35.7984 10.4754 35.559 10.7258C35.3185 10.9761 35.0178 11.1688 34.6545 11.3063C34.2925 11.4437 33.8457 11.5125 33.3168 11.5125C32.7878 11.5125 32.3423 11.4437 31.979 11.3063C31.6157 11.1688 31.3151 10.9761 31.0745 10.7258C30.834 10.4754 30.6511 10.1796 30.5234 9.83597C30.3958 9.49355 30.3001 9.11554 30.2375 8.70439C30.1737 8.29324 30.1369 7.85877 30.127 7.40344C30.1172 6.94811 30.1123 6.48051 30.1123 5.9994ZM31.0536 5.9994C31.0536 6.7051 31.0659 7.34699 31.0905 7.92505C31.115 8.50311 31.2009 9.00017 31.3482 9.41623C31.4955 9.83229 31.7201 10.1538 32.0244 10.3784C32.3276 10.6043 32.7596 10.716 33.318 10.716C33.8764 10.716 34.3072 10.603 34.6116 10.3784C34.9147 10.1526 35.1405 9.83229 35.2878 9.41623C35.4351 9.00017 35.521 8.50311 35.5455 7.92505C35.5701 7.34699 35.5824 6.7051 35.5824 5.9994C35.5824 5.2937 35.5701 4.65181 35.5455 4.07375C35.521 3.49569 35.4351 2.99863 35.2878 2.58257C35.1405 2.16651 34.9147 1.84495 34.6116 1.62036C34.3072 1.39453 33.8764 1.28285 33.318 1.28285C32.7596 1.28285 32.3288 1.39576 32.0244 1.62036C31.7201 1.84618 31.4955 2.16651 31.3482 2.58257C31.2009 2.99863 31.115 3.49569 31.0905 4.07375C31.0659 4.65181 31.0536 5.2937 31.0536 5.9994Z"
                fill="currentColor"
            />
            <path
                d="M37.1797 0.51416H39.5349L40.9638 8.2182H40.9942L42.7264 0.51416H45.4318L47.0726 8.2182H47.1031L48.4698 0.51416H50.7184L48.287 11.4859H45.7491L44.0322 3.46203H44.0017L42.102 11.4859H39.6098L37.1797 0.51416Z"
                fill="currentColor"
            />
            <path
                d="M51.2666 11.4859V0.51416H57.2536V2.20065H53.5609V4.90614H56.9643V6.59263H53.5609V9.79937H57.3755V11.4859H51.2666Z"
                fill="currentColor"
            />
            <path
                d="M57.8633 11.4859V0.51416H63.8503V2.20065H60.1576V4.90614H63.561V6.59263H60.1576V9.79937H63.9721V11.4859H57.8633Z"
                fill="currentColor"
            />
            <path
                d="M69.4769 0.51416H71.9996L68.93 5.43784L71.9996 11.4859H69.416L66.7867 5.86296H66.7562V11.4859H64.4619V0.51416H66.7562V5.20943H66.7867L69.4769 0.51416Z"
                fill="currentColor"
            />
        </svg>
    )
}
