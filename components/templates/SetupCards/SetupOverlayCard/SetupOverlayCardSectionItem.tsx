import { Icon } from '@components/atoms/Icon/Icon'
import ElgatoImage from '@components/common/ElgatoImage/ElgatoImage'
import { Button } from '@components/molecules/Button/Button'
import { ProductAddToCart } from '@components/molecules/ProductAddToCart/ProductAddToCart'
import { ProductPrice } from '@components/molecules/ProductPrice/ProductPrice'
import { CardProps } from '@components/templates/CardList/CardList'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import { useProductUI } from '@pylot-data/hooks/product/use-product-ui'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC } from 'react'
import s from './SetupOverlayCardSectionItem.module.scss'

type SetupOverlayCardSectionItemProps = {
    card: CardProps
    productsData: (ConfigurableProduct | SimpleProduct)[]
    hoverItem: CardProps | undefined
}

export const SetupOverlayCardSectionItem: FC<SetupOverlayCardSectionItemProps> = ({
    card,
    productsData,
    hoverItem
}) => {
    const { t } = useTranslation('common')
    const { textPanel } = card
    const cardProduct = productsData.find((product) => product.sku === card.sku)
    const link = textPanel?.link
    const { isOutOfStock } = useProductUI(cardProduct)
    const productLink = card?.link?.linkUrl
    const imageUrl =
        card.cloudinaryMedia?.[0]?.secure_url ?? cardProduct?.image?.url

    return (
        <div id={card.title} className={cn('flex flex-col rounded-xl')}>
            <div
                className={cn(
                    s['setup-overlay-card-section-item'],
                    'rounded-xl flex-nowrap md:flex-wrap',
                    {
                        ['border-blue']:
                            hoverItem?.title === card.title &&
                            hoverItem?.sku === card.sku,
                        ['border-transparent border-2']: true
                    }
                )}
            >
                <div
                    className={cn(
                        s['setup-overlay-card-section-item__image'],
                        'w-4/12 md:w-full rounded-md overflow-hidden'
                    )}
                >
                    {imageUrl &&
                        (productLink ? (
                            <a
                                href={productLink}
                                target="_blank"
                                rel="noreferrer"
                                aria-details={t('ada|Opens in a new Tab')}
                                tabIndex={-1}
                            >
                                <ElgatoImage
                                    src={imageUrl}
                                    alt={
                                        card.cloudinaryMedia?.[0]?.context
                                            ?.custom?.alt
                                    }
                                    objectFit="cover"
                                    layout="fill"
                                />
                            </a>
                        ) : (
                            <ElgatoImage
                                src={imageUrl}
                                alt={
                                    card.cloudinaryMedia?.[0]?.context?.custom
                                        ?.alt
                                }
                                objectFit="cover"
                                layout="fill"
                            />
                        ))}
                </div>
                <div
                    className={cn(
                        s['setup-overlay-card-section-item__content'],
                        'flex xl:flex-1 gap-24px justify-between items-center w-8/12 md:w-full'
                    )}
                >
                    <div className="flex justify-between h-auto md:h-full flex-row md:flex-col w-full">
                        <div className="flex flex-col">
                            <h6>
                                {productLink ? (
                                    <a
                                        href={productLink}
                                        target="_blank"
                                        rel="noreferrer"
                                        aria-details={`${
                                            textPanel?.headline &&
                                            `${textPanel?.headline} - `
                                        }${t('ada|Opens in a new Tab')}`}
                                    >
                                        {textPanel?.headline}
                                    </a>
                                ) : (
                                    <>{textPanel?.headline}</>
                                )}
                            </h6>
                            <p className="xs-copy">{textPanel?.bodyCopy}</p>
                        </div>
                        {textPanel?.calloutTitle && (
                            <p className="button-text-small text-primitive-gray-100">
                                {textPanel.calloutTitle}
                            </p>
                        )}
                        {!textPanel?.calloutTitle &&
                            isOutOfStock &&
                            cardProduct && (
                                <p className="button-text-small text-radical-red-1">
                                    {t('Out of stock')}
                                </p>
                            )}
                        {!textPanel?.calloutTitle &&
                            !isOutOfStock &&
                            cardProduct && (
                                <ProductPrice
                                    product={cardProduct}
                                    inNav={false}
                                    // eslint-disable-next-line i18next/no-literal-string
                                    size="small"
                                    variant="inline"
                                    className={cn(s['mobile-block'])}
                                />
                            )}
                    </div>
                    {cardProduct && !link && (
                        <ProductAddToCart
                            id={`setup-overlay-section-item-atc-btn-${cardProduct.uid}`}
                            product={cardProduct}
                            // eslint-disable-next-line i18next/no-literal-string
                            buttonVariant="secondary"
                            buttonLabel={
                                isOutOfStock
                                    ? t('Out of stock')
                                    : t(`Add to Cart`)
                            }
                            className="flex-shrink-0"
                        />
                    )}
                    {link && (
                        <Button
                            variant={link.style}
                            color={link.styleColor}
                            href={link.linkUrl}
                            newTab={link.newTab}
                            iconAlignment={link.iconAlignment}
                            className="flex-shrink-0"
                            label={link.linkTitle}
                        >
                            {link.icon && <Icon name={link.icon} />}
                            {link.linkTitle}
                        </Button>
                    )}
                </div>
            </div>
        </div>
    )
}
