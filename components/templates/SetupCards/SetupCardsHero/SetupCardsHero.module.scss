
.setup-cards-hero{
 height: calc(100vh - 88px);
  @media only screen and (max-width: 767px) {
   height: calc(100vh - 66px);
  }

  h1{
    &.fade-in {
      opacity: 0;
      animation: fadeInScaleSmall 0.6s ease-in forwards;
    }
    &.fade-out {
      animation: fadeOutScaleSmall 0.6s ease-in forwards;
      opacity: 0;
    }
  }

  &__header{
    &__button{
      animation: jump 3s infinite;
    }
  }
  
  
  &__wrapper{
    transform: translateX(-50%);
    max-height: 440px;
    padding: 0 10px;
    @media only screen and (max-height: 800px){
      height: 45vh;
    }

    div{
      width: 25%;
    }
    div:nth-of-type(2){
      margin: 0 10px;
      margin-top: 100px;
    }
    div:nth-of-type(3){
      margin: 0 10px;
      margin-top: 175px;
      margin-left: 0;
    }
    div:nth-of-type(4){
      margin-top: 50px;
    }
    &__image{
      &.fade-in {
        opacity: 0;
        animation: fadeInScale 0.4s ease-in forwards;
      }
      &.fade-out {
        opacity: 1;
        animation: fadeOutScale 0.4s ease-in forwards;
      }
      
      &.delay-2 {
        animation-delay: 0.2s;
      }
      &.delay-3 {
        animation-delay: 0.4s;
      }
      &.delay-4 {
        animation-delay: 0.6s;
      }
      &.delay-5 {
        animation-delay: 0.8s;
      }
    }

    @media only screen and (max-width: 767px) {
      min-width: auto;
      flex-wrap: wrap;
      padding: 0 10px;
      div{
        width: calc(50% - 10px);
      }
      &__image{
        margin-top: 16px;
        img{
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      div:nth-of-type(3),
      div:nth-of-type(4){
        margin-top: 16px;
      }
    }
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeInScaleSmall {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeOutScaleSmall {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(20px);
  }
}
@keyframes fadeOutScale {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(40px);
  }
}
@keyframes fadeOutScaleUp {
  0% {
    transform: translateY(40px);
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes jump {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-15px);
  }
  60% {
    transform: translateY(-8px);
  }
}