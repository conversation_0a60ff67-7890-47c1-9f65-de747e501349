import { Icon } from '@components/atoms/Icon/Icon'
import CloseIcon from '@components/atoms/Icon/general/CloseIcon'
import FavIcon from '@components/atoms/Icon/general/FavIcon'
import ShareIcon from '@components/atoms/Icon/general/ShareIcon'
import { MenuItemProps } from '@components/organisms/Header/Header'
import { LinkListProps } from '@components/organisms/LinkList/LinkList'
import {
    FilterType,
    getFilterFromMenuItem
} from '@components/templates/SetupCards/SetupCards'
import cn from 'classnames'
import dynamic from 'next/dynamic'
import {
    Dispatch,
    FC,
    SetStateAction,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react'
import s from './SetupCardsFilters.module.scss'
import { nanoid } from 'nanoid'

const AnimateHeight = dynamic(() => import('react-animate-height'), {
    ssr: false
})

type SetupCardsFiltersProps = {
    content: LinkListProps
    setShowFavouriteCards: Dispatch<SetStateAction<boolean>>
    setSelectedFilters: Dispatch<SetStateAction<FilterType[]>>
    selectedFilters: FilterType[]
    showFavouriteCards: boolean
    selectedFavouriteCardsId: string[]
    onShareClick: () => void
}

export const SetupCardsFilters: FC<SetupCardsFiltersProps> = ({
    content,
    setSelectedFilters,
    selectedFilters,
    setShowFavouriteCards,
    showFavouriteCards,
    onShareClick,
    selectedFavouriteCardsId
}) => {
    const filterWrapper = useRef<HTMLDivElement | null>(null)
    const filterBtn = useRef<HTMLButtonElement | null>(null)
    const selectedFilter = useRef<HTMLDivElement | null>(null)
    const dropdownContent = useRef<HTMLDivElement | null>(null)
    const handleOutsideClick = (event: MouseEvent) => {
        const targetElement = event.target as HTMLElement
        if (
            (selectedFilter &&
                selectedFilter.current &&
                selectedFilter.current.contains(targetElement)) ||
            (filterBtn.current && filterBtn.current.contains(targetElement)) ||
            (filterWrapper.current &&
                filterWrapper.current.contains(targetElement))
        ) {
            // Clicked inside one of the elements, do nothing
            return
        }

        // Clicked outside all elements, close the component
        setIsOpen(() => false)
    }
    const [isCloseToTop, setIsCloseToTop] = useState(false)
    const elementRef = useRef<HTMLDivElement | null>(null)
    if (selectedFavouriteCardsId.length === 0) {
        setShowFavouriteCards(false)
    }
    useEffect(() => {
        document.addEventListener('mousedown', handleOutsideClick)
        const handleScroll = () => {
            const element = elementRef.current
            if (element) {
                const rect = element.getBoundingClientRect()
                const isClose = rect.top <= 400
                if (isClose !== isCloseToTop) {
                    setIsCloseToTop(isClose)
                }
            }
        }

        window.addEventListener('scroll', handleScroll)
        return () => {
            window.removeEventListener('scroll', handleScroll)
        }
    }, [])

    const { headline, linkListItems } = content
    const filtersCategories = (linkListItems ?? []) as MenuItemProps[]
    const removeFilter = (filter: FilterType) => {
        setSelectedFilters((prevState) =>
            prevState.filter(
                (currentFilter) => currentFilter.key !== filter.key
            )
        )
    }
    const handleButtonClick = () => {
        setShowFavouriteCards((prevState) => !prevState)
        smoothScrollOnClick() // Call the smoothScrollOnClick function here
    }
    const smoothScrollOnClick = () => {
        // Perform the smooth scrolling action here
        const targetPosition = window.innerHeight + 53
        window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
        })
    }
    const removeFilterCategory = (
        activeFilterItems: MenuItemProps[] | undefined
    ) => {
        let selectedFiltersNew = []
        selectedFiltersNew = selectedFilters.filter(
            (item) => !activeFilterItems?.some((el) => el.menuId == item.key)
        )
        setSelectedFilters(selectedFiltersNew)

        console.log(activeFilterItems, 'activeFilterItems')
        // setSelectedFilters((prevState) =>
        //     prevState.filter(
        //         (currentFilter) => currentFilter.key !== filter.key
        //     )
        // )
    }
    const clearFilter = () => {
        setSelectedFilters([])
        setIsOpen((prevState) => !prevState)
    }
    const onFilterClick = (filter?: FilterType) => {
        if (!filter) {
            return
        }
        if (!selectedFilters.some((item) => item.key === filter.key)) {
            setSelectedFilters((prevState) => [...prevState, filter])
        }
    }

    const [categorysOpen, setcategorysOpen] = useState<string[]>([])
    const onCategoryClick = (categoryTitle?: string) => {
        if (!categoryTitle) {
            return
        }
        // if already in: remove
        if (categorysOpen.includes(categoryTitle)) {
            setcategorysOpen((prevState) =>
                prevState.filter((category) => category !== categoryTitle)
            )
        } else {
            setcategorysOpen((prevState) => [...prevState, categoryTitle])
        }
    }

    const isOpenCategory = (categoryTitle?: string) => {
        if (!categoryTitle) {
            return false
        }
        return categorysOpen.includes(categoryTitle)
    }

    const [isOpen, setIsOpen] = useState(false)
    const wrapperId = useMemo(() => nanoid(), [])

    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (isOpen) {
                const filterItems = filterWrapper.current?.querySelectorAll(
                    'button:not(.outline-none), div[role="button"]'
                )
                const focusable = Array.from(filterItems || []) as HTMLElement[]
                const currentIndex = focusable.indexOf(
                    document.activeElement as HTMLElement
                )

                if (event.key === 'Escape') {
                    setIsOpen(false)
                    const filterElement = document.querySelector(
                        '.setup-cards-filters-btn > button'
                    ) as HTMLElement
                    filterElement?.focus()
                } else if (event.key === 'Tab') {
                    if (currentIndex === focusable.length - 1) {
                        event.preventDefault()
                        const filterElement = document.querySelector(
                            '.setup-cards-filters-btn > button'
                        ) as HTMLElement
                        filterElement?.focus()
                    }
                }
            }
        }

        document.addEventListener('keydown', handleKeyDown)

        return () => {
            document.removeEventListener('keydown', handleKeyDown)
        }
    }, [isOpen])

    return (
        <div
            className={cn(
                s['setup-cards-filters__wrap'],
                'sticky top-0 pt-6 z-60'
            )}
        >
            <div
                className={cn(
                    s['setup-cards-filters__banner'],
                    'w-full absolute -top-80 h-80 transition-opacity ease-in-out duration-500'
                )}
            />
            <div
                className={cn(
                    s['setup-cards-filters'],
                    'flex flex-row gap-8px mt-6 mb-32 md:mb-40'
                )}
            >
                <div
                    className={cn(
                        s['setup-cards-filters__button'],
                        'relative p-8px bg-white transition-opacity ease-in-out duration-500',
                        isCloseToTop ? 'opacity-100' : 'opacity-0',
                        'setup-cards-filters-btn'
                    )}
                    ref={elementRef}
                >
                    <button
                        ref={filterBtn}
                        className="flex flex-row"
                        onClick={() => setIsOpen((prevState) => !prevState)}
                    >
                        {/* eslint-disable-next-line i18next/no-literal-string */}
                        <Icon className="relative" name="settingsAdjust" />
                        <div
                            className={cn(
                                s['setup-cards-filters__status-icon'],
                                selectedFilters.length > 0
                                    ? 'opacity-100'
                                    : 'opacity-0',
                                'rounded-full w-4 h-4'
                            )}
                        />
                        {headline && <span className="ml-5px">{headline}</span>}
                    </button>

                    <div
                        ref={filterWrapper}
                        className={cn(
                            s['setup-cards-filters__wrapper'],
                            isOpen ? s['open'] : '',
                            'absolute top-full bg-white z-1 rounded-3xl p-8px'
                        )}
                        id={`filterAccordionGroup_${wrapperId}`}
                    >
                        <AnimateHeight height={isOpen ? 'auto' : 0}>
                            <div
                                className={cn(
                                    s['setup-cards-filters__wrapper__filter']
                                )}
                            >
                                {filtersCategories.map((item) => (
                                    <div
                                        className={cn(
                                            s[
                                                'setup-cards-filters__wrapper__section'
                                            ],
                                            'rounded-xl px-16px mb-4px flex-wrap'
                                        )}
                                        key={item.menuTitle}
                                    >
                                        <div className="flex justify-between">
                                            <div
                                                className={cn(
                                                    s[
                                                        'setup-cards-filters__wrapper__section__title'
                                                    ],
                                                    'font-semibold cursor-pointer w-3/4'
                                                )}
                                                onClick={() =>
                                                    onCategoryClick(
                                                        item.menuTitle
                                                    )
                                                }
                                                onKeyPress={(e) => {
                                                    if (
                                                        e.key === 'Enter' ||
                                                        e.key === ' '
                                                    ) {
                                                        e.preventDefault()
                                                        onCategoryClick(
                                                            item.menuTitle
                                                        )
                                                    }
                                                }}
                                                role="button"
                                                tabIndex={0}
                                                id={`accordion-btn_${item.menuTitle}`}
                                                aria-expanded={isOpenCategory(
                                                    item.menuTitle
                                                )}
                                            >
                                                {item.menuTitle}
                                            </div>
                                            <div
                                                className={cn(
                                                    s[
                                                        'setup-cards-filters__wrapper__section__category-selection'
                                                    ],
                                                    'w-1/4',
                                                    !isOpenCategory(
                                                        item.menuTitle
                                                    ) &&
                                                        item.subMenuItems &&
                                                        item.subMenuItems.filter(
                                                            (subMenuItem) =>
                                                                selectedFilters.some(
                                                                    (
                                                                        selectedFilter
                                                                    ) =>
                                                                        getFilterFromMenuItem(
                                                                            subMenuItem
                                                                        )
                                                                            ?.key ===
                                                                        selectedFilter.key
                                                                )
                                                        ).length > 0
                                                        ? 'block'
                                                        : 'hidden',
                                                    'flex justify-center'
                                                )}
                                            >
                                                <span>
                                                    {item.subMenuItems?.filter(
                                                        (subMenuItem) =>
                                                            selectedFilters.some(
                                                                (
                                                                    selectedFilter
                                                                ) =>
                                                                    getFilterFromMenuItem(
                                                                        subMenuItem
                                                                    )?.key ===
                                                                    selectedFilter.key
                                                            )
                                                    ).length || 0}
                                                </span>
                                                <button
                                                    onClick={() =>
                                                        removeFilterCategory(
                                                            item.subMenuItems?.filter(
                                                                (subMenuItem) =>
                                                                    selectedFilters.some(
                                                                        (
                                                                            selectedFilter
                                                                        ) =>
                                                                            getFilterFromMenuItem(
                                                                                subMenuItem
                                                                            )
                                                                                ?.key ===
                                                                            selectedFilter.key
                                                                    )
                                                            )
                                                        )
                                                    }
                                                >
                                                    <CloseIcon />
                                                </button>
                                            </div>
                                        </div>
                                        <div
                                            className={cn(
                                                s[
                                                    'setup-cards-filters__wrapper__section__items'
                                                ],
                                                'flex flex-col flex-wrap bg-transparent'
                                            )}
                                            id={`accordion-content_${item.menuTitle}`}
                                            role="region"
                                            aria-labelledby={`accordion-btn_${item.menuTitle}`}
                                        >
                                            <AnimateHeight
                                                height={
                                                    isOpenCategory(
                                                        item.menuTitle
                                                    )
                                                        ? 'auto'
                                                        : 0
                                                }
                                            >
                                                {item.subMenuItems?.map(
                                                    (subMenuItem) => {
                                                        const filter = getFilterFromMenuItem(
                                                            subMenuItem
                                                        )
                                                        if (filter) {
                                                            const active = selectedFilters.some(
                                                                (
                                                                    selectedFilter
                                                                ) =>
                                                                    selectedFilter.key ===
                                                                    filter.key
                                                            )
                                                            return (
                                                                <div
                                                                    className={cn(
                                                                        s[
                                                                            'setup-cards-filters__wrapper__section__filter'
                                                                        ],
                                                                        active
                                                                            ? s[
                                                                                  'active'
                                                                              ]
                                                                            : '',
                                                                        'filter-' +
                                                                            filter.key,
                                                                        'flex mb-8px justify-between'
                                                                    )}
                                                                >
                                                                    <button
                                                                        className="text-left"
                                                                        key={
                                                                            filter.key
                                                                        }
                                                                        onClick={() => {
                                                                            onFilterClick(
                                                                                filter
                                                                            )
                                                                        }}
                                                                    >
                                                                        {
                                                                            filter.name
                                                                        }
                                                                    </button>
                                                                    <button
                                                                        className={cn(
                                                                            s[
                                                                                'setup-cards-filters__wrapper__section__filter__remove'
                                                                            ],
                                                                            active
                                                                                ? s[
                                                                                      'active'
                                                                                  ]
                                                                                : 'outline-none'
                                                                        )}
                                                                        onClick={() =>
                                                                            removeFilter(
                                                                                filter
                                                                            )
                                                                        }
                                                                    >
                                                                        <CloseIcon />
                                                                    </button>
                                                                </div>
                                                            )
                                                        }
                                                    }
                                                )}
                                            </AnimateHeight>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </AnimateHeight>
                        <div
                            className={cn(
                                isOpen ? 'block' : 'hidden',
                                'flex p-12px justify-between pt-5'
                            )}
                        >
                            <div className="flex justify-center relative">
                                {selectedFavouriteCardsId.length > 0 && (
                                    <div
                                        className="absolute bg-red-500 rounded-full text-center"
                                        style={{
                                            top: -13,
                                            width: 15,
                                            height: 15,
                                            background: '#204CFE',
                                            fontSize: 10,
                                            color: '#fff'
                                        }}
                                    >
                                        {selectedFavouriteCardsId.length}
                                    </div>
                                )}
                                <button
                                    className={cn(
                                        s[
                                            'setup-cards-filters__wrapper__favIcon'
                                        ],
                                        s[showFavouriteCards ? 'active' : ''],
                                        'mr-8px'
                                    )}
                                    onClick={() => handleButtonClick()}
                                    aria-label="Favorite"
                                >
                                    <FavIcon />
                                </button>

                                <button
                                    onClick={() => onShareClick()}
                                    aria-label="Share"
                                >
                                    <ShareIcon />
                                </button>
                            </div>
                            <button
                                className={cn(
                                    s[
                                        'setup-cards-filters__wrapper__section__items__reset'
                                    ]
                                )}
                                onClick={() => clearFilter()}
                                // eslint-disable-next-line i18next/no-literal-string
                            >
                                Reset Filter
                            </button>
                        </div>
                    </div>
                </div>
                {selectedFilters.map((filter) => (
                    <div
                        ref={selectedFilter}
                        key={filter.key}
                        className={cn(
                            s['setup-cards-filters__button_filter'],
                            window.innerWidth > 767 ? 'block' : 'hidden',
                            'flex flex-row gap-8px bg-white',
                            isCloseToTop ? 'opacity-100' : 'opacity-0'
                        )}
                    >
                        <span className="outline-none">{filter.name}</span>
                        <button onClick={() => removeFilter(filter)}>
                            <CloseIcon />
                        </button>
                    </div>
                ))}
            </div>
        </div>
    )
}
