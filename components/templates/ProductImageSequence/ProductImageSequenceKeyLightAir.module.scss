.key-light-air-animation {
    position: relative;
    width: 100%;
    height: auto;
    padding-bottom: 40px;
        overflow-x: hidden;
    @screen lg {
        padding-bottom: 80px;
    }

    &__wrapper {
        max-width: 1920px;
        margin: 0 auto;
        position: relative;
        height: 400vh;
    }

    &__sequence-wrapper {
        position: relative;
        z-index: 20;
    }

    &__bottom-wrapper {
        width: 191%;
        margin: 0 auto;
        position: absolute;
        z-index: 1;
        top: 50%;
        left: 50%;
        transform: translate(-17.9%, 0);
        height: 100%;
    }

    canvas {
        object-fit: contain;
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        transform: translate(-50%, -62%);
        z-index: 2;
    }

    &__bottom-stand {
        width: 100%;
        z-index: 10;
        transform: translate(0, 0);
        will-change: transform;
    }

    :global {
        #key-light-air-image-sequence {
            width: 24vh;
            margin: 0 auto;
            overflow: visible;
        }
    }
}
