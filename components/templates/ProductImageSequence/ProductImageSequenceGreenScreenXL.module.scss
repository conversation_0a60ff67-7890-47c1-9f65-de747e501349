.green-screen-xl-animation {
    position: relative;
    width: 100%;
    height: auto;

    &__children {
        position: relative;
    }

    &__text {
        opacity: 0;
        position: absolute;
        top: var(--sticky-nav-height--sm);
        left: 0;
        z-index: -1;
        width: 100%;

        @screen md {
            -webkit-transform: translate(0, 50px);
            transform: translate(0, 50px);
        }

        @screen lg {
            top: var(--sticky-nav-height--lg);
        }

        &--active {
            z-index: 20;
        }
    }

    canvas {
        margin-top: 35vh;
        height: calc(100% - 5vh - var(--sticky-nav-height--sm)) !important;
        width: 100%;
        object-position: top;

        @screen md {
            margin-top: 150px;
            height: calc(100% - 150px) !important;
        }
    }

    &__text-inner {
        position: absolute;
        top: 50px;
        left: 15%;
        white-space: nowrap;
        width: 100%;
    }
}
