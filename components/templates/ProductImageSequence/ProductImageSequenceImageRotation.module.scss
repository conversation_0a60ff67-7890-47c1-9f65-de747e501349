.image-rotation-animation {
    &__text-container {
        flex: 0.7;
        min-height: 200px;

        @screen md {
            flex: 1.4;
        }
    }
    &__text {
        position: absolute;
        opacity: 1;
        left: 0;
        width: 100%;

        &--active {
            z-index: 20;
        }
    }

    canvas {
        position: static;
        transform: none;
        flex: 1;
        height: max(50%, 150px) !important;
        width: fit-content;
        z-index: 1;

        @screen md {
            height: auto !important;
            min-height: 0;
            max-width: fit-content;
            flex: 2.9;
        }
    }

    &__text-inner {
        position: absolute;
        top: 50px;
        left: 15%;
        white-space: nowrap;
        width: 100%;
    }

    :global {
        #image-rotation-image-sequence {
            overflow: visible;
            margin-top: var(--sticky-nav-height--sm);
            height: calc(100vh - var(--sticky-nav-height--sm));

            @screen lg {
                margin-top: var(--sticky-nav-height--lg);
                height: calc(100vh - var(--sticky-nav-height--lg));
            }
            bottom: 0 !important;

            display: flex;
            flex-direction: column-reverse;
            justify-content: center;
            align-items: center;
        }
    }
}
