import { FC, RefObject, useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { useOnScreen } from '@lib/hooks/useOnScreen'

export type MultipleImageSplitAnimationAnimationProps = {
    containerRef: RefObject<HTMLDivElement>
    videoRef: RefObject<HTMLDivElement>
    imagesContainerRef: RefObject<HTMLDivElement>
    setVideoPlaying: (playing: boolean) => void
}

const animationIds = {
    zoom: 'multiple-image-split-zoom',
    fadeOut: 'multiple-image-split-fade-out'
}

const MultipleImageSplitAnimationAnimation: FC<MultipleImageSplitAnimationAnimationProps> = (
    props
) => {
    const {
        containerRef,
        setVideoPlaying,
        videoRef,
        imagesContainerRef
    } = props
    const { isOnScreen } = useOnScreen(containerRef, true, { threshold: 0 })
    const zoomAnimationRef = useRef<gsap.core.Timeline | null>(null)
    const fadeOutAnimationRef = useRef<gsap.core.Timeline | null>(null)

    useEffect(() => {
        const { current: containerRefCurrent } = containerRef
        if (
            !containerRefCurrent ||
            !videoRef.current ||
            !imagesContainerRef.current
        ) {
            return
        }
        const init = () => {
            const { current: containerRefCurrent } = containerRef
            if (
                !containerRefCurrent ||
                !videoRef.current ||
                !imagesContainerRef.current
            ) {
                return
            }
            if (zoomAnimationRef.current) {
                zoomAnimationRef.current.scrollTrigger?.refresh()
            } else {
                // text mask zoom in animation
                zoomAnimationRef.current = gsap
                    .timeline({
                        scrollTrigger: {
                            trigger: containerRefCurrent,
                            scrub: 0.3,
                            start: '-=15% top',
                            end: () => '+=15%',
                            // markers: true,
                            id: animationIds.zoom
                        }
                    })
                    .to([...imagesContainerRef.current!.children], {
                        transform: 'translate(0,0) scale(1)'
                    })
            }
            if (fadeOutAnimationRef.current) {
                fadeOutAnimationRef.current.scrollTrigger?.refresh()
            } else {
                // overlay fade out animation and start video
                fadeOutAnimationRef.current = gsap
                    .timeline({
                        scrollTrigger: {
                            trigger: containerRefCurrent,
                            scrub: 0.3,
                            start: '-=5% top',
                            end: () => '+=10%',
                            // markers: true,
                            id: animationIds.fadeOut
                        }
                    })
                    .to(videoRef.current, {
                        opacity: 1
                    })
                    .to(
                        imagesContainerRef.current,
                        {
                            opacity: 0
                        },
                        '<25%'
                    )
                    .call(
                        () => {
                            setVideoPlaying(false)
                        },
                        undefined,
                        '-=0.01'
                    )
                    .call(() => {
                        setVideoPlaying(true)
                    }, undefined)
            }
        }
        const handleResize = () => {
            init()
        }
        init()
        window.addEventListener('resize', handleResize)
        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [
        containerRef,
        videoRef,
        imagesContainerRef,
        isOnScreen,
        setVideoPlaying
    ])

    useEffect(() => {
        if (isOnScreen) {
            zoomAnimationRef.current?.scrollTrigger?.refresh()
            fadeOutAnimationRef.current?.scrollTrigger?.refresh()
        }
    }, [isOnScreen])

    useEffect(() => {
        return () => {
            zoomAnimationRef.current?.kill()
            fadeOutAnimationRef.current?.kill()
            zoomAnimationRef.current = null
            fadeOutAnimationRef.current = null
        }
    }, [])

    return null
}

export default MultipleImageSplitAnimationAnimation
