import React, { FC, useMemo } from 'react'
import s from '@components/organisms/StickyNav/StickyNav.module.scss'
import { PriceDiscount } from '@components/molecules/PriceDiscount/PriceDiscount'
import { Button } from '@components/molecules/Button/Button'
import CartIcon from '@components/atoms/Icon/general/CartIcon'
import { ProductPrice } from '@components/molecules/ProductPrice/ProductPrice'
import { ProductAddToCart } from '@components/molecules/ProductAddToCart/ProductAddToCart'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'

export type StickyNavBuyProductProps = {
    price?: string
    priceDiscount?: string
    product?: any
    isOutOfStock?: boolean
    buttonLabel?: string
    addProductHandler?: () => void
    buttonDesktopSize?: 's' | 'm' | 'l'
    className?: string
}

const STICK_NAV_ATC_LOCATION = 1

export const StickyNavBuy: FC<StickyNavBuyProductProps> = (props) => {
    const {
        price,
        priceDiscount,
        product,
        buttonLabel,
        isOutOfStock = false,
        addProductHandler,
        buttonDesktopSize
    } = props
    const { pageTheme } = useLayoutContext()
    const isNeo = pageTheme === 'neo'
    const priceTheme =
        pageTheme === 'dark' ? 'light' : pageTheme === 'neo' ? 'neo' : undefined
    const priceSize = 'medium'
    const labelClassName = 'hidden lg2:block'
    const isZeroPrice = useMemo(() => {
        if (price !== '') {
            return Number(price?.replace(/\D+/g, '')) === 0
        }
        return false
    }, [price])
    return (
        <div className={isZeroPrice ? 'hidden' : s['sticky-nav__buy']}>
            {price && (
                <PriceDiscount
                    price={price}
                    priceDiscount={priceDiscount}
                    variant="below"
                    size={priceSize}
                    className={s['sticky-nav__price']}
                    totalValue={Number(
                        product?.price_range?.minimum_price?.final_price?.value
                    )}
                    subtotalValue={Number(
                        product?.price_range?.minimum_price?.regular_price
                            ?.value
                    )}
                    theme={priceTheme}
                />
            )}
            {!price && product && product.uid && (
                <ProductPrice
                    product={product}
                    size={priceSize}
                    variant="below"
                    theme={priceTheme}
                />
            )}
            {addProductHandler && (
                <Button
                    onClick={addProductHandler}
                    variant="primary"
                    // eslint-disable-next-line i18next/no-literal-string
                    Component="button"
                    disabled={isOutOfStock}
                    className={s['sticky-nav__add-to-cart']}
                    color={pageTheme === 'dark' ? 'light' : undefined}
                    isNeo={isNeo}
                    desktopSize={buttonDesktopSize}
                    label={buttonLabel}
                    aria-haspopup="dialog"
                    aria-controls="cart-sidebar-root"
                >
                    <span>
                        <CartIcon />
                    </span>
                    <span className="hidden lg2:block">{buttonLabel}</span>
                </Button>
            )}
            {product &&
                product.uid &&
                !addProductHandler &&
                !product?.not_sellable && (
                    <ProductAddToCart
                        id={`sticky-product-atc-btn-${product.uid}`}
                        product={product}
                        className={s['sticky-nav__add-to-cart']}
                        labelClassName={labelClassName}
                        buttonLabel={buttonLabel}
                        cmsATCLocation={STICK_NAV_ATC_LOCATION}
                        isNeo={isNeo}
                        buttonDesktopSize={buttonDesktopSize}
                    >
                        <span>
                            <CartIcon />
                        </span>
                    </ProductAddToCart>
                )}
        </div>
    )
}
