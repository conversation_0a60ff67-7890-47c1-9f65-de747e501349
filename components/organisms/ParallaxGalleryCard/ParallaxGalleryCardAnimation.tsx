import { FC, RefObject, useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/dist/ScrollTrigger'
import { useOnScreen } from '@lib/hooks/useOnScreen'

export type ParallaxGalleryCardAnimationProps = {
    containerRef: RefObject<HTMLElement>
    imageRef: RefObject<HTMLElement>
    parallaxStrength: string
    id: string
}

const ParallaxGalleryCardAnimation: FC<ParallaxGalleryCardAnimationProps> = (
    props
) => {
    const { containerRef, imageRef, parallaxStrength, id } = props
    const scrollAnimationRef = useRef<gsap.core.Timeline | null>(null)
    const { isOnScreen } = useOnScreen(containerRef)

    useEffect(() => {
        if (!containerRef?.current || !imageRef?.current) {
            return
        }
        if (!scrollAnimationRef.current) {
            scrollAnimationRef.current = gsap
                .timeline({
                    scrollTrigger: {
                        trigger: containerRef.current,
                        scrub: true,
                        start: 'top bottom',
                        end: 'bottom top',
                        markers: false,
                        id: `parallax-gallery-card-${id}`
                    }
                })
                .to(containerRef.current, {
                    transform: `translate(0,-${parallaxStrength})`
                })
                .to(
                    imageRef.current,
                    {
                        transform: 'translate(0,0%)'
                    },
                    '<'
                )
        }
    }, [containerRef, id, imageRef, parallaxStrength, scrollAnimationRef])

    useEffect(() => {
        if (isOnScreen) {
            scrollAnimationRef.current?.scrollTrigger?.refresh()
        }
    }, [isOnScreen])

    useEffect(() => {
        return () => {
            scrollAnimationRef.current?.kill()
            scrollAnimationRef.current = null
        }
    }, [])

    return null
}

export default ParallaxGalleryCardAnimation
