import React, { FC, useEffect, useRef, useState } from 'react'
import s from './SalesCardsExclusiveList.module.scss'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { CardProps } from '@components/templates/CardList/CardList'
import dynamic from 'next/dynamic'

const SalesCard = dynamic(
    () => import('@components/organisms/SalesCard/SalesCard'),
    {
        ssr: false
    }
)
import getProductsBySkus from '@pylot-data/api/operations/get-products-by-skus'
import { useRouter } from 'next/router'
import cn from 'classnames'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import { SwiperSlide } from 'swiper/react'
import { SwiperOptions } from 'swiper/types/swiper-options'
import { A11y, Mousewheel, Navigation, Keyboard } from 'swiper'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '@components/templates/Section/Section'

import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import {
    ConfigurableVariant,
    ProductInterface,
    SimpleProduct
} from '@pylot-data/pylotschema'
import { useIsMobileDevice } from '@config/hooks/useIsMobileDevice'
import { TextBlock } from '@components/molecules/TextBlock/TextBlock'
import { SalesBannerProps } from '@components/molecules/SalesBanner/SalesBanner'
import {
    ProductStockStatus,
    ProductVariant
} from '@pylot-data/hooks/product/use-product-ui'
import { SalesCardExclusive } from '@components/organisms/SalesCardExclusive/SalesCardExclusive'
import { BannerProps } from '@components/molecules/Banner/Banner'

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

interface SalesCardProps extends CardProps {
    product?: null | ProductInterface
}

interface SalesCardsExclusiveListProps {
    cards?: SalesCardProps[]
    id?: string
    textPanel?: PrimaryTextProps
    additionalTextPanels?: PrimaryTextProps[]
    backgroundColor?: SectionBgColor
    variant?: 'default' | 'small'
    mobileSlider?: boolean
}

export const SalesCardsExclusiveList: FC<SalesCardsExclusiveListProps> = ({
    cards = [],
    id,
    backgroundColor = SectionBgColor.TRANSPARENT,
    textPanel,
    additionalTextPanels,
    variant = 'default',
    mobileSlider = false
}) => {
    const { locale, events } = useRouter()
    const [salesCards, setSalesCards] = useState<SalesCardProps[]>([])
    const sliderRef = useRef<HTMLDivElement | null>(null)
    const textBlockSize = 'large'
    const sliderSettings: SwiperOptions = {
        spaceBetween: 16,
        slidesPerView: 'auto',
        centeredSlides: false,
        loop: false,
        modules: [A11y, Navigation, Mousewheel, Keyboard],
        initialSlide: 0,
        allowTouchMove: useIsMobileDevice(),
        breakpoints: {
            320: {
                slidesPerView: 'auto'
            },
            1280: {
                slidesPerView: 3.05
            },
            1345: {
                slidesPerView: 3.4
            },
            1490: {
                slidesPerView: 4.05
            },
            1900: {
                slidesPerView: 'auto'
            }
        },
        mousewheel: {
            forceToAxis: true
        },
        keyboard: true
    }
    const textColor = SectionThemeDarkBgColors.includes(backgroundColor)
        ? 'light'
        : 'dark'
    const paginationTheme = SectionThemeDarkBgColors.includes(backgroundColor)
        ? SwiperSliderTheme.LIGHT
        : SwiperSliderTheme.DARK
    const cardVariant = variant === 'small' ? 'small' : 'default'

    useEffect(() => {
        const handleRouteChangeComplete = () => {
            setSalesCards([])
        }
        events.on('routeChangeComplete', handleRouteChangeComplete)
        return () => {
            events.off('routeChangeComplete', handleRouteChangeComplete)
        }
    }, [])

    const currentRegionCode = locale?.substring(3)
    const getTextPanelToShow = () => {
        if (additionalTextPanels?.length) {
            // find the one for region
            return additionalTextPanels.find((textPanel) => {
                return textPanel.regions?.some(
                    (region) => region.name === currentRegionCode
                )
            })
        }

        return textPanel
    }

    const textPanelToShow = getTextPanelToShow()

    useEffect(() => {
        const sortProductCardByStockStatus = (
            productSalesCards: SalesCardProps[]
        ) => {
            const isOutOfStock = (product: ProductInterface) => {
                const variantIndex =
                    (product.variants as ConfigurableVariant[])?.findIndex(
                        (variant) =>
                            variant!.product!.sku?.toLowerCase() ===
                            product.sku?.toLowerCase()
                    ) ?? ProductVariant.NOT_SELECTED
                const variant = (product?.variants?.[variantIndex]?.product ??
                    product) as SimpleProduct
                const isOutOfStock =
                    product?.stock_status === ProductStockStatus.OutOfStock ||
                    variant?.stock_status === ProductStockStatus.OutOfStock ||
                    variantIndex === ProductVariant.NOT_EXIST
                return isOutOfStock
            }

            productSalesCards.sort((a, b) => {
                // do not change the order if one of the items does not contain a product
                if (!a.product || !b.product) {
                    return 0
                }

                const aOutOfStock = isOutOfStock(a.product)
                const bOutOfStock = isOutOfStock(b.product)

                // do not change if both Items have the same stock status
                if (aOutOfStock === bOutOfStock) {
                    return 0
                }

                return aOutOfStock ? 1 : -1
            })

            return productSalesCards
        }

        if (cards.length > 0 && !salesCards.length) {
            const skus = cards
                .map((card) => card.sku ?? '')
                .filter((sku) => sku != '')
            if (skus.length) {
                getProductsBySkus(skus, locale ?? '').then((products) => {
                    const productSalesCards: SalesCardProps[] = []
                    const productsAndVariants: ProductInterface[] = []
                    if (products && products.length) {
                        products.forEach((product) => {
                            if (
                                product.__typename === 'ConfigurableProduct' &&
                                product?.variants?.length
                            ) {
                                product?.variants?.forEach((pVariant) => {
                                    if (pVariant?.product) {
                                        productsAndVariants.push(
                                            pVariant.product as ProductInterface
                                        )
                                    }
                                })
                            } else {
                                productsAndVariants.push(
                                    product as ProductInterface
                                )
                            }
                        })
                        cards?.forEach((card) => {
                            const product = productsAndVariants.find(
                                (product) => product.sku === card.sku
                            )
                            if (product) {
                                card.product = product
                            }
                            productSalesCards.push(card)
                        })
                    }
                    setSalesCards(
                        sortProductCardByStockStatus(productSalesCards)
                    )
                })
            } else {
                setSalesCards(sortProductCardByStockStatus(cards))
            }
        }
    }, [cards, locale, salesCards.length])

    return (
        <div
            id={id}
            className={cn(
                s['sales-card-list'],
                'max-w-full overflow-x-hidden',
                backgroundColor
            )}
        >
            {textPanelToShow && (
                <Container
                    size={
                        salesCards.length <= 3
                            ? ContainerSize.SMALL
                            : ContainerSize.LARGE
                    }
                    className={s['sales-card-list__text-wrapper']}
                >
                    {textPanelToShow && (
                        <TextBlock
                            className={s['sales-card-list__text']}
                            headline={textPanelToShow.headline}
                            bodyCopy={textPanelToShow.bodyCopy}
                            link={textPanelToShow.link}
                            size={textBlockSize}
                            color={textColor}
                            alignment={
                                salesCards.length <= 3 ? 'center' : 'left'
                            }
                        />
                    )}
                </Container>
            )}
            {salesCards && salesCards.length > 0 && salesCards.length <= 3 && (
                <Container size={ContainerSize.LARGE}>
                    <div
                        className={cn(
                            'pt-24px gap-16px flex flex-col md:flex-row md:flex-wrap w-full md:justify-center',
                            s['sales-card-list__list'],
                            {
                                ['hidden md:flex']: mobileSlider
                            }
                        )}
                    >
                        {salesCards.map((card, index) => {
                            if (card.sku && !card.product) return
                            return (
                                <SalesCard
                                    product={card.product}
                                    className={s['sales-card-list__item']}
                                    key={`sales-card-${card.textPanel?.headline}-${index}`}
                                    text={
                                        card.textPanel?.richText
                                            ? card.textPanel?.richText
                                            : card.textPanel?.bodyCopy
                                    }
                                    headline={card.textPanel?.headline}
                                    link={card.textPanel?.link}
                                    subheader={card.textPanel?.subheader}
                                    media={card.media}
                                    posterImage={card.posterImage}
                                    textColor={
                                        card.textColor as 'dark' | 'light'
                                    }
                                    variant={cardVariant}
                                    banner={
                                        card.children?.length
                                            ? (card.children[0] as BannerProps)
                                            : undefined
                                    }
                                    customOptions={card?.customOptions}
                                />
                            )
                        })}
                    </div>
                </Container>
            )}
            {salesCards &&
                salesCards.length > 0 &&
                (salesCards.length > 3 ||
                    (salesCards.length <= 3 && mobileSlider)) && (
                    <div
                        ref={sliderRef}
                        className={cn(s['sales-card-list__slider-wrapper'], {
                            ['md:hidden']: mobileSlider
                        })}
                    >
                        <SwiperSlider
                            loop={false}
                            settings={sliderSettings}
                            className={s['sales-card-list__slider']}
                            paginationTheme={paginationTheme}
                            navigationTheme={SwiperSliderTheme.LIGHT}
                        >
                            {salesCards.map((card, i) => {
                                if (card.sku && !card.product) return null
                                return (
                                    <SwiperSlide key={`sales-card-item-${i}`}>
                                        <SalesCardExclusive
                                            product={card.product}
                                            className={
                                                s['sales-card-list__item']
                                            }
                                            text={
                                                card.textPanel?.richText
                                                    ? card.textPanel?.richText
                                                    : card.textPanel?.bodyCopy
                                            }
                                            headline={card.textPanel?.headline}
                                            link={
                                                card.textPanel?.link ||
                                                card.link
                                            }
                                            subheader={
                                                card.textPanel?.subheader
                                            }
                                            media={card.media}
                                            posterImage={card.posterImage}
                                            textColor={
                                                card.textColor as
                                                    | 'dark'
                                                    | 'light'
                                            }
                                            customOptions={card?.customOptions}
                                            backgroundColor={
                                                card.backgroundColor
                                            }
                                            variant={cardVariant}
                                            banner={
                                                card.children?.length
                                                    ? (card
                                                          .children[0] as SalesBannerProps)
                                                    : undefined
                                            }
                                            hideDiscover={card.hideDiscover}
                                        />
                                    </SwiperSlide>
                                )
                            })}
                        </SwiperSlider>
                    </div>
                )}
        </div>
    )
}

export default SalesCardsExclusiveList
