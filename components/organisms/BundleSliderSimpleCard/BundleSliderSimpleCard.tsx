import s from './BundleSliderSimpleCard.module.scss'
import cn from 'classnames'
import React, { FC, useState } from 'react'
import { Badge } from '@components/atoms/Badge/Badge'
import { useTranslation } from 'next-i18next'
import CardList from '@components/templates/CardList/CardList'
import { BannerProps } from '@components/molecules/Banner/Banner'
import { useRouter } from 'next/router'
import ChevronUpIcon from '@components/atoms/Icon/general/ChevronUpIcon'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

export type BundleSliderSimpleCardProps = {
    card?: any
    index?: number
    first?: boolean
    handleMouseClick?: () => void | undefined
    cloudinaryMedia?: CloudinaryMedia[]
}

export const BundleSliderSimpleCard: FC<BundleSliderSimpleCardProps> = (
    props
) => {
    const { t } = useTranslation(['common'])
    const { card, index } = props
    const [isOpen, setIsOpen] = useState(false)

    const { isAnimationStopped } = useAnimationAndVideosToggle()

    const image = card?.cloudinaryMedia?.[0]?.secure_url

    const handleMouseEnter = () => {
        setIsOpen(true)
    }

    const handleMouseLeave = () => {
        setIsOpen(false)
    }
    const handleClick = () => {
        setIsOpen(!isOpen)
        console.log('clicked')
    }
    const sku = card?.sku

    const banners = card?.children?.filter(
        (child: { meta: { contentType: string } }) =>
            child.meta.contentType === 'moleculeBanner'
    ) as BannerProps[] | undefined

    const { locale = 'en' } = useRouter()
    const currentRegionCode = locale?.substring(3) || ''
    const currentRegionBanner = banners?.find((banner) =>
        banner?.regions?.some((region) => region.name === currentRegionCode)
    )
    const isConfiguratorTitle =
        card.title && card.title.includes('Configurator')

    if (isConfiguratorTitle) {
        card.overlay = []
        card.variant = ''
        return (
            <div
                key={index}
                className={cn(s['bundle-list__card'], 'relative', {
                    [s['hover']]: isOpen
                })}
                onMouseEnter={() => handleMouseEnter()}
                onMouseLeave={() => handleMouseLeave()}
            >
                {card &&
                    card.children &&
                    card.children.map(
                        (
                            child: {
                                mobilePosition: any
                                title(title: any): string
                                fontSize: string | null | undefined
                                textColor(textColor: any): string | undefined
                                backgroundColor: string | undefined
                                map(
                                    arg0: (
                                        child: any,
                                        index: any
                                    ) => JSX.Element | null
                                ): React.ReactNode
                                meta: {
                                    contentType: string
                                }
                                hotspotColor: string
                                position: {
                                    top: any
                                    left: any
                                }
                                bigTitle:
                                    | boolean
                                    | React.ReactChild
                                    | React.ReactFragment
                                    | React.ReactPortal
                                    | null
                                    | undefined
                                children: any
                            },
                            i: React.Key | null | undefined
                        ) => {
                            if (
                                child.meta.contentType ===
                                'organismHardwareHotspotContent'
                            ) {
                                const bgOuterRingColorOpacity =
                                    child.hotspotColor === '#000'
                                        ? 'rgba(0,0,0,0.3)'
                                        : 'rgba(255,255,255,0.3)'
                                const bgColorOpacity =
                                    child.hotspotColor === '#000'
                                        ? 'rgba(0,0,0,0.85)'
                                        : 'rgba(255,255,255,0.85)'
                                const positionTop =
                                    window.innerWidth <= 767
                                        ? child.mobilePosition?.top
                                        : child.position?.top
                                const positionLeft =
                                    window.innerWidth <= 767
                                        ? child.mobilePosition?.left
                                        : child.position?.left

                                return (
                                    // eslint-disable-next-line react/jsx-key
                                    <div
                                        key={i}
                                        className={cn(
                                            'absolute pointer-events-auto flex justify-center items-center',
                                            s[
                                                'bundle-list__card__info__hotspots'
                                            ],
                                            {
                                                // [s['hover']]: !isOpen
                                            }
                                        )}
                                        style={{
                                            top: positionTop,
                                            left: positionLeft
                                        }}
                                    >
                                        <div
                                            className={cn(
                                                s[
                                                    'bundle-list__card__info__hotspots__dot'
                                                ],
                                                s[
                                                    'bundle-list__card__info__hotspots__dot__active'
                                                ],
                                                'absolute pointer-events-auto flex justify-center items-center'
                                            )}
                                            style={{
                                                width: 24,
                                                height: 24,
                                                borderRadius: 99999,
                                                backgroundColor: bgOuterRingColorOpacity
                                            }}
                                        >
                                            <div
                                                className={cn(
                                                    s[
                                                        'bundle-list__card__info__hotspots__dot__inner'
                                                    ]
                                                )}
                                                style={{
                                                    width: 16,
                                                    height: 16,
                                                    borderRadius: 99999,
                                                    backgroundColor: bgColorOpacity
                                                }}
                                            >
                                                &nbsp;
                                            </div>
                                        </div>
                                        <div
                                            className={cn(
                                                s[
                                                    'bundle-list__card__info__hotspots__dot-text'
                                                ],
                                                'absolute z-50 h-auto bg-white'
                                            )}
                                            style={{
                                                [positionLeft &&
                                                parseFloat(
                                                    String(positionLeft)
                                                ) > 50
                                                    ? 'right'
                                                    : 'left']: 20,
                                                borderRadius: 4
                                            }}
                                        >
                                            <p
                                                className={cn(
                                                    s[
                                                        'bundle-list__card__info__hotspots__dot-text-title'
                                                    ],
                                                    'text-black'
                                                )}
                                            >
                                                {child.bigTitle}
                                            </p>
                                        </div>
                                    </div>
                                )
                            } else if (
                                child.meta.contentType === 'moleculeBadge'
                            ) {
                                return (
                                    <div className="top-8 left-8 flex absolute">
                                        <div
                                            key={i}
                                            className={i !== 0 ? 'ml-8' : ''}
                                        >
                                            <Badge
                                                className={
                                                    child.backgroundColor
                                                }
                                                textClassName={cn(
                                                    child.textColor
                                                )}
                                                fontSize={child.fontSize}
                                                /* eslint-disable-next-line i18next/no-literal-string */
                                                size="medium"
                                                sku={sku}
                                            >
                                                <h5>
                                                    {typeof child.title !==
                                                        'function' &&
                                                        child.title}
                                                </h5>
                                            </Badge>
                                        </div>
                                    </div>
                                )
                            } else if (
                                child.meta.contentType ===
                                'templateMediaWithTextOverlay'
                            ) {
                                card.overlay = child.children
                                card.variant = child.children[0].variant
                            }
                        }
                    )}
                {currentRegionBanner && (
                    <div className="top-8 left-8 flex absolute">
                        <Badge
                            className={currentRegionBanner.badgeBackgroundColor}
                            textClassName={currentRegionBanner.badgeTextColor}
                            /* eslint-disable-next-line i18next/no-literal-string */
                            size="medium"
                            sku={sku}
                            hasDiscount={currentRegionBanner.badgeHasDiscount}
                        >
                            {currentRegionBanner.badgeHasDiscount ? (
                                currentRegionBanner.badgeText ||
                                currentRegionBanner.headline
                            ) : (
                                <h5>
                                    {currentRegionBanner.badgeText ||
                                        currentRegionBanner.headline}
                                </h5>
                            )}
                        </Badge>
                    </div>
                )}
                <img alt={card?.media?.title || ''} src={image} />
                <div className={cn(s['bundle-list__card__bottom'])}>
                    <div
                        className={cn(
                            s['bundle-list__card__bottom__bundle'],
                            s['bundle-list__card__bottom__bundle__simple'],
                            'flex text-black font-bold'
                        )}
                    >
                        <div className="flex justify-between w-full items-center my-4">
                            <div
                                className={cn(
                                    s[
                                        'bundle-list__card__bottom__bundle__text'
                                    ],
                                    'uppercase md:text-3xl text-2xl'
                                )}
                            >
                                {card?.textPanel?.headline}

                                {/*{t('Design your Dream Setup')}*/}
                            </div>
                            <button
                                className={cn(
                                    s[
                                        'bundle-list__card__bottom__bundle__button'
                                    ]
                                )}
                                onClick={props.handleMouseClick}
                            >
                                {t('Explore')}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        )
    } else if (!isConfiguratorTitle) {
        return (
            <div
                key={index}
                className={cn(s['bundle-list__card'], 'relative', {
                    [s['hover']]: isOpen
                })}
                onMouseEnter={() => handleMouseEnter()}
                onMouseLeave={() => handleMouseLeave()}
            >
                {card &&
                    card.children &&
                    card.children.map(
                        (
                            child: {
                                mobilePosition: any
                                hasDiscount: boolean | undefined
                                title(title: any): string
                                fontSize: string | null | undefined
                                textColor(textColor: any): string | undefined
                                backgroundColor: string | undefined
                                map(
                                    arg0: (
                                        child: any,
                                        index: any
                                    ) => JSX.Element | null
                                ): React.ReactNode
                                meta: {
                                    contentType: string
                                }
                                hotspotColor: string
                                position: {
                                    top: any
                                    left: any
                                }
                                bigTitle:
                                    | boolean
                                    | React.ReactChild
                                    | React.ReactFragment
                                    | React.ReactPortal
                                    | null
                                    | undefined
                                children: any
                            },
                            i: React.Key | null | undefined
                        ) => {
                            if (
                                child.meta.contentType ===
                                'organismHardwareHotspotContent'
                            ) {
                                const bgOuterRingColorOpacity =
                                    child.hotspotColor === '#000'
                                        ? 'rgba(0,0,0,0.3)'
                                        : 'rgba(255,255,255,0.3)'
                                const bgColorOpacity =
                                    child.hotspotColor === '#000'
                                        ? 'rgba(0,0,0,0.85)'
                                        : 'rgba(255,255,255,0.85)'
                                const positionTop =
                                    window.innerWidth <= 767
                                        ? child.mobilePosition?.top
                                        : child.position?.top
                                const positionLeft =
                                    window.innerWidth <= 767
                                        ? child.mobilePosition?.left
                                        : child.position?.left
                                return (
                                    // eslint-disable-next-line react/jsx-key
                                    <div
                                        key={i}
                                        className={cn(
                                            'absolute pointer-events-auto flex justify-center items-center',
                                            s[
                                                'bundle-list__card__info__hotspots'
                                            ],
                                            {
                                                [s['hover']]: !isOpen
                                            },
                                            {
                                                [s[
                                                    'no-animation'
                                                ]]: isAnimationStopped
                                            }
                                        )}
                                        style={{
                                            top: positionTop,
                                            left: positionLeft
                                        }}
                                    >
                                        <div
                                            className={cn(
                                                s[
                                                    'bundle-list__card__info__hotspots__dot'
                                                ],
                                                s[
                                                    'bundle-list__card__info__hotspots__dot__active'
                                                ],
                                                'absolute pointer-events-auto flex justify-center items-center'
                                            )}
                                            style={{
                                                width: 24,
                                                height: 24,
                                                borderRadius: 99999,
                                                backgroundColor: bgOuterRingColorOpacity
                                            }}
                                        >
                                            <div
                                                className={cn(
                                                    s[
                                                        'bundle-list__card__info__hotspots__dot__inner'
                                                    ]
                                                )}
                                                style={{
                                                    width: 16,
                                                    height: 16,
                                                    borderRadius: 99999,
                                                    backgroundColor: bgColorOpacity
                                                }}
                                            >
                                                &nbsp;
                                            </div>
                                        </div>
                                        <div
                                            className={cn(
                                                s[
                                                    'bundle-list__card__info__hotspots__dot-text'
                                                ],
                                                'absolute z-50 h-auto bg-white'
                                            )}
                                            style={{
                                                [positionLeft &&
                                                parseFloat(
                                                    String(positionLeft)
                                                ) > 50
                                                    ? 'right'
                                                    : 'left']: 20,
                                                borderRadius: 4
                                            }}
                                        >
                                            <p
                                                className={cn(
                                                    s[
                                                        'bundle-list__card__info__hotspots__dot-text-title'
                                                    ],
                                                    'text-black font-univers67BoldCondensed'
                                                )}
                                            >
                                                {child.bigTitle}
                                            </p>
                                        </div>
                                    </div>
                                )
                            } else if (
                                child.meta.contentType === 'moleculeBadge'
                            ) {
                                return (
                                    <div className="top-8 left-8 flex absolute">
                                        <div
                                            key={i}
                                            className={i !== 0 ? 'ml-8' : ''}
                                        >
                                            <Badge
                                                className={
                                                    child.backgroundColor
                                                }
                                                textClassName={cn(
                                                    child.textColor
                                                )}
                                                fontSize={child.fontSize}
                                                /* eslint-disable-next-line i18next/no-literal-string */
                                                size="medium"
                                                sku={sku}
                                                hasDiscount={child.hasDiscount}
                                            >
                                                <h5>
                                                    {typeof child.title !==
                                                        'function' &&
                                                        child.title}
                                                </h5>
                                            </Badge>
                                        </div>
                                    </div>
                                )
                            } else if (
                                child.meta.contentType === 'templateCardList'
                            ) {
                                return null
                            }
                        }
                    )}
                {currentRegionBanner && (
                    <div className="top-8 left-8 flex absolute">
                        <Badge
                            className={currentRegionBanner.badgeBackgroundColor}
                            textClassName={currentRegionBanner.badgeTextColor}
                            /* eslint-disable-next-line i18next/no-literal-string */
                            size="medium"
                            sku={sku}
                            hasDiscount={currentRegionBanner.badgeHasDiscount}
                        >
                            {currentRegionBanner.badgeHasDiscount ? (
                                currentRegionBanner.badgeText ||
                                currentRegionBanner.headline
                            ) : (
                                <h5>
                                    {currentRegionBanner.badgeText ||
                                        currentRegionBanner.headline}
                                </h5>
                            )}
                        </Badge>
                    </div>
                )}
                <img alt={card?.media?.title || ''} src={image} />
                <div
                    className={cn(s['bundle-list__card__bottom'])}
                    role="button"
                    onClick={() => handleClick()}
                    onKeyPress={() => handleClick()}
                    tabIndex={0}
                >
                    <div
                        className={cn(
                            s['bundle-list__card__bottom__accordion'],
                            s['no-animation'],
                            'flex w-full text-black font-bold',
                            {
                                [s['hover']]: isOpen
                            },
                            {
                                [s['active']]: isOpen
                            }
                        )}
                    >
                        {card.children && (
                            <div>
                                <div className="flex items-center">
                                    <ChevronUpIcon />
                                    <div
                                        className={cn(
                                            s[
                                                'bundle-list__card__bottom__accordion__title'
                                            ]
                                        )}
                                    >
                                        {t('in_the_bundle')}
                                    </div>
                                </div>
                                <div
                                    className={cn(
                                        s[
                                            'bundle-list__card__bottom__accordion__content'
                                        ],
                                        'w-full',
                                        {
                                            [s['active']]: isOpen
                                        }
                                    )}
                                >
                                    <div
                                        className={cn(
                                            s[
                                                'bundle-list__card__bottom__accordion__content__line'
                                            ]
                                        )}
                                    >
                                        {card.children &&
                                            card.children.map(
                                                (
                                                    child: any,
                                                    i:
                                                        | React.Key
                                                        | null
                                                        | undefined
                                                ) => (
                                                    <CardList
                                                        key={i}
                                                        content={Object.assign(
                                                            {},
                                                            child,
                                                            {
                                                                variant:
                                                                    'bundle-products'
                                                            }
                                                        )}
                                                    />
                                                )
                                            )}
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                    <div
                        className={cn(
                            s['bundle-list__card__bottom__bundle'],
                            'flex text-black font-bold'
                        )}
                    >
                        <CardList
                            content={Object.assign({}, card, {
                                variant: 'bundle-product'
                            })}
                        />
                    </div>
                </div>
            </div>
        )
    } else {
        return null
    }
}
export default BundleSliderSimpleCard
