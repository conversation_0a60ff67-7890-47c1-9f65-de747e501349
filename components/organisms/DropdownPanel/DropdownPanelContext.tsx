import { createContext, FC, useContext } from 'react'

export type DropdownPanelProviderProps = {
    setUpdateTheHeight: () => void
}

export type DropdownPanelContent = {
    updateHeightChange: () => void
}

const DropdownPanelContext = createContext<DropdownPanelContent>({
    updateHeightChange: () => {
        //
    }
})

export const DropdownPanelProvider: FC<DropdownPanelProviderProps> = ({
    children,
    setUpdateTheHeight
}) => {
    return (
        <DropdownPanelContext.Provider
            value={{
                updateHeightChange: () => {
                    setUpdateTheHeight()
                }
            }}
        >
            {children}
        </DropdownPanelContext.Provider>
    )
}

export const useDropdownPanelContent = (): DropdownPanelContent =>
    useContext(DropdownPanelContext)
