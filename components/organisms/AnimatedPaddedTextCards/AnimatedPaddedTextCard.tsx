import s from './AnimatedPaddedTextCard.module.scss'
import React, { FC } from 'react'
import cn from 'classnames'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import { SectionBgColor } from '@components/templates/Section/Section'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

export interface AnimatedPaddedTextCardProps {
    headline?: string
    bodyCopy?: string
    className?: string
    headlineClasses?: string[]
    eventTracking?: any
    animationImage?: ImageType | VideoType
    backgroundColor?: SectionBgColor | string
    textColor?: string
}

export const AnimatedPaddedTextCard: FC<AnimatedPaddedTextCardProps> = ({
    className,
    headline,
    bodyCopy,
    headlineClasses = [],
    animationImage,
    backgroundColor,
    textColor
}) => {
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    return (
        <div
            className={cn(
                'rounded-2xl gap-12px overflow-hidden',
                s['animated-padded-text-card'],
                className,
                backgroundColor
            )}
        >
            <div className={cn(s['animated-padded-text-card__media'])}>
                <div
                    className={cn(
                        s['animated-padded-text-card__media-animated'],
                        { [s['no-animation']]: isAnimationStopped }
                    )}
                >
                    <div
                        className={cn(
                            s['animated-padded-text-card__icon-container'],
                            'py-16'
                        )}
                    >
                        <img
                            src={animationImage?.file?.url}
                            // eslint-disable-next-line i18next/no-literal-string
                            alt="LogoIcon"
                        />
                        <img
                            src={animationImage?.file?.url}
                            // eslint-disable-next-line i18next/no-literal-string
                            alt="LogoIcon"
                        />
                    </div>
                </div>
            </div>
            <div
                className={cn(
                    s['animated-padded-text-card__text'],
                    'flex flex-col gap-6px md:gap-8px'
                )}
            >
                <h4
                    className={cn(
                        s['animated-padded-text-card__text-headline'],
                        'flex items-center',
                        { 'text-white': textColor === 'light' }
                    )}
                >
                    {headline}
                </h4>
                <p
                    className={cn(
                        s['animated-padded-text-card__text-body'],
                        'small-copy h-full',
                        { 'text-white': textColor === 'light' }
                    )}
                >
                    {bodyCopy}
                </p>
            </div>
        </div>
    )
}

export default AnimatedPaddedTextCard
