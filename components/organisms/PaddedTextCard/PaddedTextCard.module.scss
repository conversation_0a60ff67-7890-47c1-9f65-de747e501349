.padded-text-card {
    &__cloudinary-logo-image {
        width: 72px;
        height: 72px;
    }
    @apply flex flex-wrap items-stretch content-between;
    &:hover {
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
        transition: ease-in-out 0.5s;
        cursor: pointer;
    }

    &__text {
        &-headline {
            height: 100%;
            min-height: fit-content;
            align-items: center;
            @screen md {
                min-height: 60px;
            }
        }
    }

    &__media {
        min-height: 100px;
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        @screen md {
            @apply flex items-center justify-between flex-wrap;
        }

        &-item {
            @apply flex items-center flex-col;
            @screen md {
                max-width: 129px;
            }

            &-lottie,
            img {
                width: 72px;
                height: 72px;
            }
        }
    }
}
