import s from './PaddedTextCard.module.scss'
import React, { FC } from 'react'
import cn from 'classnames'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import { FeatureListProps } from '@components/templates/FeatureList/FeatureList'
import Image from '@corsairitshopify/corsair-image'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { Player } from '@lottiefiles/react-lottie-player'
import { Icon } from '@components/atoms/Icon/Icon'
import { Button } from '@components/molecules/Button/Button'
import ElgatoImage from '@components/common/ElgatoImage'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'

export interface PaddedTextCardProps {
    headline?: string
    textPanel?: PrimaryTextProps
    bodyCopy?: string
    richText?: string
    className?: string
    link?: LinkResponse
    featureList?: FeatureListProps
    logoImage?: ImageType | any
    headlineClasses?: string[]
    textAlignment?: string
    lottieAnimation?: ImageType
    eventTracking?: any
    cloudinaryLogoImage?: CloudinaryMedia[]
}

export const PaddedTextCard: FC<PaddedTextCardProps> = ({
    className,
    headline,
    bodyCopy,
    link,
    featureList,
    headlineClasses = [],
    cloudinaryLogoImage
}) => {
    return (
        <div
            className={cn(
                'p-16 bg-bg-grey rounded-2xl',
                s['padded-text-card'],
                className
            )}
        >
            <div
                className={cn(s['padded-text-card__text'], 'flex flex-col')}
                style={{ marginBottom: '16px' }}
            >
                <h3
                    className={cn(
                        s['padded-text-card__text-headline'],
                        headlineClasses,
                        'text-h2 md-max:text-h2-md-max'
                    )}
                >
                    {cloudinaryLogoImage?.[0] && (
                        <span
                            className={cn(
                                'flex flex-none',
                                s['padded-text-card__cloudinary-logo-image']
                            )}
                        >
                            <ElgatoImage
                                src={cloudinaryLogoImage?.[0]?.secure_url}
                                width={72}
                                height={72}
                            />
                        </span>
                    )}
                    {headline}
                </h3>
                {bodyCopy && (
                    <p
                        className={cn(
                            s['padded-text-card__text-body'],
                            'body-copy h-full'
                        )}
                        style={{ display: 'contents' }}
                        dangerouslySetInnerHTML={{ __html: unescape(bodyCopy) }}
                    />
                )}
            </div>
            {link?.linkUrl && (
                <Button
                    href={link.linkUrl}
                    variant={link.style}
                    newTab={link.newTab}
                    color={link.styleColor}
                    dataLayer={link.eventTracking}
                    style={{ padding: 'unset' }}
                    label={link.linkTitle}
                >
                    {link.linkTitle}
                    {link?.style === 'tertiary' && (
                        <Icon
                            // eslint-disable-next-line i18next/no-literal-string
                            name="chevronRight"
                            className="w-24px h-24px"
                        />
                    )}
                </Button>
            )}
            {featureList && (
                <div className={cn(s['padded-text-card__media'])}>
                    {featureList?.features?.map((feature, index) => {
                        return (
                            // eslint-disable-next-line i18next/no-literal-string
                            <div
                                key={index}
                                className={cn(
                                    s['padded-text-card__media-item']
                                )}
                            >
                                {feature?.cloudinaryImage?.[0] && (
                                    <ElgatoImage
                                        src={
                                            feature?.cloudinaryImage?.[0]
                                                ?.secure_url
                                        }
                                        alt={
                                            feature?.cloudinaryImage?.[0]
                                                ?.context?.custom?.alt || ''
                                        }
                                        width={72}
                                        height={72}
                                    />
                                )}
                                {feature?.lottieAnimation?.file?.url && (
                                    <div
                                        className={cn(
                                            s['padded-text-card__media-lottie']
                                        )}
                                    >
                                        <Player
                                            autoplay
                                            loop
                                            src={
                                                feature?.lottieAnimation?.file
                                                    ?.url
                                            }
                                        />
                                    </div>
                                )}
                                <h6
                                    className={cn(
                                        s[
                                            'padded-text-card__media-item__body-copy'
                                        ],
                                        'font-bold text-center'
                                    )}
                                >
                                    {feature?.label}
                                </h6>
                            </div>
                        )
                    })}
                </div>
            )}
        </div>
    )
}

export default PaddedTextCard
