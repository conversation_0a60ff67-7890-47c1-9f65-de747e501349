import React, { FC, useCallback, useEffect, useState } from 'react'
import retailerCountries from './retailerCountries.json'
import { useRouter } from 'next/router'
import { defaultLocale } from '@config/hooks/useStoreConfig'
import cn from 'classnames'
import s from './RetailersDropdown.module.scss'
import { useTranslation } from 'next-i18next'
import ChevronDownIcon from '@components/atoms/Icon/general/ChevronDownIcon'

export type RetailerLocalization = {
    [key: string]: {
        name: string
    }
}
export type RetailerCountry = {
    iso_code: string
    name: string
    localizations: RetailerLocalization
}

export type RetailerContinent = {
    name: string
    localizations: RetailerLocalization
    countries: RetailerCountry[]
}

export type RetailerCountryDropdownProps = {
    close?: boolean
    activeCountry: string
    setActiveCountry: (country: string) => void
    countriesWithRetailers: string[]
}

export const RetailerCountryDropdown: FC<RetailerCountryDropdownProps> = (
    props
) => {
    const {
        // close = false,
        activeCountry,
        setActiveCountry,
        countriesWithRetailers
    } = props
    const router = useRouter()
    const { locale = defaultLocale } = router
    const { t } = useTranslation('common')
    const language = locale.split('-')[0]
    const [activeCountryName, setActiveCountryName] = useState('')
    const [isOpen, setIsOpen] = useState(false)
    const getContinentName = useCallback(
        (continent: RetailerContinent) => {
            if (
                continent.localizations &&
                continent.localizations[language] &&
                continent.localizations[language].name
            ) {
                return continent.localizations[language].name
            }
            return continent.name
        },
        [language]
    )
    const toggleList = useCallback(() => {
        setIsOpen((prevIsOpen) => !prevIsOpen)
    }, [])
    const selectCountry = useCallback((country: string) => {
        setActiveCountry(country)
        setIsOpen(false)
    }, [])
    /* useEffect(() => {
        if (!close && isOpen) {
            setIsOpen(false)
        }
    }, [close, isOpen]) */
    return (
        <>
            {retailerCountries && retailerCountries.length > 0 && (
                <div
                    className={cn(s['retailers-dropdown'], {
                        [s['retailers-dropdown--open']]: isOpen
                    })}
                >
                    <div className={s['retailers-dropdown__toggle']}>
                        <button
                            onClick={toggleList}
                            onKeyPress={toggleList}
                            aria-expanded={isOpen}
                            aria-controls="retailers-dropdown"
                            className={s['retailers-dropdown__button']}
                        >
                            {activeCountryName
                                ? activeCountryName
                                : `${t('Select your country')}`}
                            <ChevronDownIcon
                                className={s['retailers-dropdown__icon']}
                            />
                        </button>
                    </div>
                    <div className={s['retailers-dropdown__list-wrapper']}>
                        <ul className={s['retailers-dropdown__list']}>
                            {(retailerCountries as RetailerContinent[]).map(
                                (continent) => (
                                    <li
                                        key={`retailer-continent-${continent.name}`}
                                    >
                                        <span
                                            className={
                                                s['retailers-dropdown__title']
                                            }
                                        >
                                            {getContinentName(continent)}
                                        </span>
                                        {continent.countries?.length > 0 && (
                                            <ul>
                                                {continent.countries.map(
                                                    (country) => {
                                                        if (
                                                            country.iso_code ===
                                                            activeCountry
                                                        ) {
                                                            setActiveCountryName(
                                                                country
                                                                    .localizations[
                                                                    language
                                                                ]?.name ??
                                                                    country.name
                                                            )
                                                        }
                                                        return (
                                                            <li
                                                                key={`retailer-country-${country.name}`}
                                                                className={
                                                                    s[
                                                                        'retailers-dropdown__item'
                                                                    ]
                                                                }
                                                                data-iso-code={
                                                                    country.iso_code
                                                                }
                                                            >
                                                                <button
                                                                    onClick={() =>
                                                                        selectCountry(
                                                                            country.iso_code
                                                                        )
                                                                    }
                                                                    onKeyPress={() =>
                                                                        selectCountry(
                                                                            country.iso_code
                                                                        )
                                                                    }
                                                                    className={cn(
                                                                        s[
                                                                            'retailers-dropdown__button'
                                                                        ],
                                                                        {
                                                                            ['text-content-blue']:
                                                                                activeCountry ===
                                                                                country.iso_code,
                                                                            [s[
                                                                                'retailers-dropdown__button--disabled'
                                                                            ]]: !countriesWithRetailers.includes(
                                                                                country.iso_code
                                                                            )
                                                                        }
                                                                    )}
                                                                    disabled={
                                                                        !countriesWithRetailers.includes(
                                                                            country.iso_code
                                                                        )
                                                                    }
                                                                >
                                                                    {country
                                                                        .localizations[
                                                                        language
                                                                    ]?.name ??
                                                                        country.name}
                                                                </button>
                                                            </li>
                                                        )
                                                    }
                                                )}
                                            </ul>
                                        )}
                                    </li>
                                )
                            )}
                        </ul>
                    </div>
                </div>
            )}
        </>
    )
}
