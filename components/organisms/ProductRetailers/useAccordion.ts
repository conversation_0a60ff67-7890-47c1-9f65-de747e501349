import React, { useState } from 'react'

export type UseAccordion = (config: {
    showICon: React.ReactElement
    hideIcon: React.ReactElement
}) => {
    show: boolean
    icon: React.ReactElement
    toggle: () => void
}

export const useAccordion: UseAccordion = (config) => {
    const [show, setShow] = useState(false)
    const toggle = () => {
        setShow((prev) => !prev)
    }

    return {
        show,
        toggle,
        icon: show ? config.showICon : config.hideIcon
    }
}
