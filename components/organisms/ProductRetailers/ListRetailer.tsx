import React, { FC } from 'react'
import { RetailerLink } from '@pylot-data/pylotschema'
import s from './ListRetailer.module.scss'
import Image from '@corsairitshopify/corsair-image'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'

export type ListRetailerProps = {
    retailerLinks: RetailerLink[]
}

export const ListRetailer: FC<ListRetailerProps> = (props) => {
    const { retailerLinks } = props
    return (
        <>
            {retailerLinks && retailerLinks.length > 0 && (
                <ul className={s['list-retailer']}>
                    {retailerLinks.map((retailerLink) => (
                        <li
                            key={`retailer-link-${retailerLink.title}`}
                            className={s['list-retailer__item']}
                        >
                            <a
                                href={retailerLink.url}
                                target="_blank"
                                rel="noreferrer"
                                className={s['list-retailer__link']}
                            >
                                {retailerLink.retailer.logo &&
                                    (retailerLink.retailer.logo as ImageType)
                                        .file?.url && (
                                        <Image
                                            src={
                                                (retailerLink.retailer
                                                    .logo as ImageType).file.url
                                            }
                                            width={134}
                                            height={44}
                                            alt={
                                                (retailerLink.retailer
                                                    .logo as ImageType)
                                                    .description ||
                                                retailerLink.retailer
                                                    .retailerName
                                            }
                                        />
                                    )}
                            </a>
                        </li>
                    ))}
                </ul>
            )}
        </>
    )
}
