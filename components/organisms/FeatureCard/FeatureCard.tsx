import s from './FeatureCard.module.scss'
import React, { FC } from 'react'
import cn from 'classnames'
import FeatureCardIcon, {
    FeatureCardIconProps
} from '../../molecules/FeatureCardIcon/FeatureCardIcon'
import { LinkResponse } from '../../molecules/Link/Link'
import { Button } from '../../molecules/Button/Button'
import { Logo } from '../../atoms/Logo/Logo'

export interface FeatureCardProps {
    headline: string
    description?: string
    links?: LinkResponse[]
    icons?: FeatureCardIconProps[]
    className?: string
    style?: React.CSSProperties
}

/*eslint i18next/no-literal-string: ["error", { "ignoreAttribute": [ "size", "Component", 'variant'] }]*/
export const FeatureCard: FC<FeatureCardProps> = ({
    headline,
    description,
    links = [],
    icons = [],
    className,
    style
}) => {
    const emptyStoreButtons =
        !!links.length && links.every((link) => !link.linkUrl)
    return (
        <div
            style={style}
            className={cn(
                'p-16px bg-white flex flex-col rounded-3xl gap-8px',
                s['feature-card'],
                {
                    ['justify-end']: !icons.length && !links.length
                },
                className
            )}
        >
            {(icons.length > 0 || links.length > 0) && (
                <div
                    className={cn('flex mb-8px items-center gap-8px ', {
                        'invisible md-max:hidden ': emptyStoreButtons
                    })}
                >
                    {links &&
                        links.length > 0 &&
                        links.map((link) => (
                            <Button
                                key={link.linkTitle}
                                href={link.linkUrl}
                                Component="a"
                                variant="store"
                                size="sm"
                                label={link.linkTitle}
                            >
                                <Logo name={link.icon!} />
                            </Button>
                        ))}
                    {icons &&
                        icons.length > 0 &&
                        icons.map((icon) => (
                            <FeatureCardIcon
                                key={`${icon.icon}_${icon.text}`}
                                icon={icon.icon}
                                text={icon.text}
                                cloudinaryImage={icon.cloudinaryImage}
                                className={icon.className}
                            />
                        ))}
                </div>
            )}
            <h4 className="text-charcoal">{headline}</h4>
            {description && (
                <div className="text-charcoal text-small-copy leading-10 md-max:text-small-copy-md-max">
                    {description}
                </div>
            )}
        </div>
    )
}

export default FeatureCard
