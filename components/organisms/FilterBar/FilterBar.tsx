import { Icon } from '@components/atoms/Icon/Icon'
import { Button } from '@components/molecules/Button/Button'
import {
    Dropdown,
    DropdownSection
} from '@components/molecules/Dropdown/Dropdown'
import { ExpandingDropdown } from '@components/molecules/Dropdown/ExpandingDropdown'
import { FilterToggles } from '@components/molecules/FilterToggles/FilterToggles'
import { SearchBar } from '@components/molecules/SearchBar/SearchBar'
import { CameraCheckResolutionFilter } from '@components/organisms/CameraCheckDropdowns/CameraCheckDropdowns'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC } from 'react'
import s from './FilterBar.module.scss'

export type FilterBarProps = {
    className?: string
    toggleFilter?: CameraCheckResolutionFilter | string
    searchLabel?: string
    searchValue?: string
    filterTogglesLabel?: string
    filterToggles?: string[]
    search?: boolean
    showSearchSuggestions?: boolean
    searchSuggestions?: string[]
    handleToggleFilterUpdate?: (filter: string) => void
    dropdownLabel?: string
    dropdownOptions?: (string | DropdownSection)[]
    dropdownValue?: string
    onDropdownSelect?: (value: string) => void
    handleSearch?: (search: string, submit: boolean) => void
    variant?: 'beta' | 'downloads' | 'spare-parts'
}

export const FilterBar: FC<FilterBarProps> = (props) => {
    const {
        className,
        search = true,
        searchLabel = '',
        searchValue = '',
        showSearchSuggestions = false,
        searchSuggestions = [],
        handleSearch,
        filterTogglesLabel,
        filterToggles = [],
        handleToggleFilterUpdate,
        dropdownLabel = '',
        dropdownOptions = [],
        dropdownValue = '',
        onDropdownSelect,
        toggleFilter = '',
        variant
    } = props

    const { t } = useTranslation(['common'])
    return (
        <div className={cn(s['filter-bar'], className)}>
            <div
                className={cn(s['filter-bar__inner'], {
                    [s['filter-bar__inner-special-page']]:
                        variant === 'downloads' || variant === 'beta',
                    [s['filter-bar__inner-beta']]: variant === 'beta'
                })}
            >
                {variant === 'downloads' || variant === 'beta' ? (
                    <div className="hidden">
                        {search && filterToggles.length <= 0 && (
                            <SearchBar
                                className={s['filter-bar__search']}
                                label={searchLabel}
                                suggestions={searchSuggestions}
                                showSuggestions={showSearchSuggestions}
                                handleSearch={handleSearch}
                                searchValue={searchValue}
                            />
                        )}
                    </div>
                ) : (
                    <div>
                        {search && (
                            <SearchBar
                                className={s['filter-bar__search']}
                                label={searchLabel}
                                suggestions={searchSuggestions}
                                showSuggestions={showSearchSuggestions}
                                handleSearch={handleSearch}
                                searchValue={searchValue}
                            />
                        )}
                    </div>
                )}
                {filterToggles &&
                    filterToggles.length > 0 &&
                    variant === 'downloads' && (
                        <div className={cn(s['filter-bar__buttons'])}>
                            {dropdownOptions && dropdownOptions.length > 0 && (
                                <ExpandingDropdown
                                    className={cn(
                                        s['filter-bar__download-dropdown'],
                                        'text-charcoal'
                                    )}
                                    title={dropdownLabel}
                                    options={dropdownOptions}
                                    onChange={onDropdownSelect}
                                    asOverlay
                                    currentOption={dropdownValue}
                                />
                            )}
                            <div
                                className={cn(s['filter-bar__buttons-hidden'])}
                            >
                                <Button
                                    variant="secondary"
                                    //eslint-disable-next-line i18next/no-literal-string
                                    iconAlignment="right"
                                    href="/s/beta"
                                    newTab
                                    label={t('Test Beta versions')}
                                >
                                    {t('Test Beta versions')}
                                    {/* eslint-disable-next-line i18next/no-literal-string */}
                                    <Icon name="chevronRight" />
                                </Button>
                            </div>
                        </div>
                    )}
                <div
                    className={cn(
                        s['filter-bar__beta-title'],
                        'md-max: flex-col-reverse'
                    )}
                >
                    {filterToggles &&
                        filterToggles.length > 0 &&
                        variant === 'beta' && (
                            <>
                                {/* eslint-disable-next-line i18next/no-literal-string */}
                                <Icon name="elgatoLogo" />
                                <h4>{t('Beta Software')}</h4>
                            </>
                        )}
                </div>

                {((filterToggles && filterToggles.length > 0) ||
                    (dropdownOptions && dropdownOptions.length > 0)) && (
                    <div className={s['filter-bar__filters']}>
                        {dropdownOptions &&
                            dropdownOptions.length > 0 &&
                            filterToggles.length <= 0 && (
                                <Dropdown
                                    className={s['filter-bar__dropdown']}
                                    title={dropdownLabel}
                                    options={dropdownOptions}
                                    onChange={onDropdownSelect}
                                    asOverlay
                                    currentOption={dropdownValue}
                                />
                            )}
                        {filterToggles && filterToggles.length > 0 && (
                            <FilterToggles
                                currentFilter={toggleFilter}
                                handleFilterUpdate={handleToggleFilterUpdate}
                                label={filterTogglesLabel}
                                toggles={filterToggles}
                                pageVariant={variant}
                            />
                        )}
                    </div>
                )}
            </div>
        </div>
    )
}
