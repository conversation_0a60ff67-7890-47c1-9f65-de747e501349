@import './_header-vars';

.header {
    @apply relative;
    z-index: 20;

    &__inner-wrapper {
        @media screen and (max-width: $nav-mobile-max-width) {
            @apply absolute top-0 left-0 overflow-hidden w-full h-screen pointer-events-none;
        }
    }

    &__inner {
        @apply w-full flex items-center justify-between pointer-events-auto;
        background-color: var(--white);
        padding: 16px;
        height: $header-height-mobile;
        position: relative;

        @media screen and (min-width: $nav-desktop-md-min-width) {
            height: $header-height-md;
            padding: 14px 30px;
        }
    }

    &__logo,
    &__buttons {
        color: var(--charcoal);
        flex-grow: 1;
        flex-basis: 0;
        z-index: 200;
        position: relative;
    }

    &__logo {
        display: flex;
        position: relative;

        a {
            display: inline-block;
        }

        svg {
            width: 140px;

            @media screen and (max-width: $nav-mobile-max-width) {
                height: 32px;
                width: 115px;
            }
        }
    }

    &__buttons {
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        @media screen and (min-width: $nav-desktop-md-min-width) {
            order: 1;
            gap: 24px;
        }
    }

    &__menu {
        @media screen and (min-width: $nav-desktop-md-min-width) {
            @apply mx-auto;
        }
    }

    &__search-btn {
        display: none;

        //temporary disable CRS-5456
        // @media screen and (min-width: $nav-desktop-md-min-width) {
        //     width: 32px;
        //     height: 32px;
        //     display: flex;
        //     justify-content: center;
        //     align-items: center;

        //     > svg {
        //         width: 24px;
        //         height: 24px;
        //     }
        // }
    }

    &__cart-btn {
        position: relative;
        width: 32px;
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;

        > svg {
            width: 24px;
            height: 24px;
        }

        &::after {
            content: '';
            width: 0;
            height: 2px;
            background-color: var(--charcoal);
            display: block;
            transition: width 0.4s cubic-bezier(0.5, 0.15, 0.33, 1);
            position: absolute;
            bottom: 0;
            left: 0;
            border-radius: 6px;
        }

        &:hover {
            &::after {
                width: 100%;
            }
        }
    }

    &__cart-amount {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 3px 4px;
        min-width: 16px;
        max-width: 22px;
        height: 16px;
        background: #e12a40;
        border-radius: 10px;
        color: var(--white);
        position: absolute;
        top: 0;
        right: 0;
    }

    &__menu-btn {
        padding: 6px;

        &-inner {
            width: 20px; // width: 60px;
            height: 15px; // height: 45px;
            position: relative;
            cursor: pointer;
            margin: 0;
            padding: 0;
        }

        @media screen and (min-width: $nav-desktop-md-min-width) {
            display: none;
        }

        span {
            display: block;
            position: absolute;
            height: 2px;
            width: 100%;
            background: var(--charcoal);
            border-radius: 9px;
            opacity: 1;
            left: 0;
            transform: rotate(0deg);
            transition: all 0.25s ease-in-out;
            transform-origin: left center;
            margin: 0;
            padding: 0;

            &:nth-child(1) {
                top: 0; // 0
            }

            &:nth-child(2) {
                top: 6px;
            }

            &:nth-child(3) {
                top: 12px;
            }
        }

        &--active {
            span {
                &:nth-child(1) {
                    transform: rotate(45deg);
                    top: -1px;
                    left: 2px;
                }

                &:nth-child(2) {
                    width: 0;
                    opacity: 0;
                }

                &:nth-child(3) {
                    transform: rotate(-45deg);
                    top: 13px;
                    left: 2px;
                }
            }
        }
    }

    &:not(.header--landing) {
        @media screen and (max-width: $nav-mobile-max-width) {
            min-height: $header-height-mobile;
        }
    }

    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    top: 0;
    /* landing page */
    &--landing {

        @media screen and (min-width: $nav-desktop-md-min-width) {
            top: $mini-nav-height;
        }

        &--light:not(.header--sub-menu) {
            .header {
                &__logo,
                &__buttons {
                    color: var(--white);
                }

                &__inner {
                    background-color: transparent;
                }

                &__menu-btn {
                    span {
                        background-color: var(--white);
                    }
                }

                &__cart-btn {
                    &::after {
                        background-color: var(--white);
                    }
                }
            }
        }

        &--dark:not(.header--sub-menu) {
            .header {
                &__inner {
                    background-color: transparent;
                }
            }
        }
    }

    &--geolocation-banner.header--landing {
        top: auto;
    }
}

/* page theme dark */
.header.page-theme-dark {
    .header__inner {
        background: var(--black);
    }


    @media screen and (max-width: $nav-mobile-max-width) {
        .header__menu-btn {
            color: var(--white);

            span {
                background-color: var(--white);
            }
        }
    }
}

.header.page-theme-dark-overlay {
    .header__inner {
        background: transparent;
    }
}

.header.page-theme-dark:not(.header--sub-menu) {
    @media screen and (min-width: $nav-desktop-md-min-width) {
        color: var(--white);

        ul > li {
            color: var(--white);
        }
    }

    .header__logo,
    .header__buttons {
        color: var(--white);
    }
}

.header.page-theme-dark.header--sub-menu {
    @media screen and (min-width: $nav-desktop-md-min-width) {
        ul > li {
            color: var(--black);
        }
    }

    @media screen and (max-width: $nav-mobile-max-width) {
        .header__logo,
        .header__buttons {
            color: var(--white);
        }
    }
}
