import { Badge } from '@components/atoms/Badge/Badge'
import { Icon } from '@components/atoms/Icon/Icon'
import { BREAKPOINT_TABLET_MAX } from '@components/layouts/MainLayout/breakpoints'
import { Button } from '@components/molecules/Button/Button'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import { SalesPopupCardMerchCategorySelection } from '@components/organisms/SalesPopupCard/SalesPopupCardMerchCategorySelection'
import { TextBuy } from '@components/organisms/TextBuy/TextBuy'
import { MERCH_CONFIG_FIT } from '@components/templates/ProductConfigurator/MerchConfigurator/MerchConfigurator'
import {
    getFitFromSKU,
    getSizeFromSKU,
    useMerchConfiguratorCategorySelection
} from '@components/templates/ProductConfigurator/MerchConfigurator/useMerchConfiguratorCategorySelection'
import Image from '@corsairitshopify/corsair-image'
import { ConfigurableProduct, SimpleProduct } from '@pylot-data/fwrdschema'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import { useProductUI } from '@pylot-data/hooks/product/use-product-ui'
import {
    ConfigurableProduct as ConfigurableProductPylot,
    ProductInterface,
    SimpleProduct as SimpleProductPylot
} from '@pylot-data/pylotschema'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import React, { FC, useEffect, useMemo, useState } from 'react'
import s from './SalesPopupCard.module.scss'

export interface SalesPopupCardProps {
    product: ProductInterface
    relatedProducts?: ProductInterface[]
    backProduct?: {
        name: string
        id: number
    }
    bundleProduct?: ConfigurableProduct | SimpleProduct
    className?: string
    style?: React.CSSProperties
    closeClicked?: () => void
    bundleClicked?: (el: {
        product?: ConfigurableProduct | SimpleProduct
        id?: number
    }) => void
    textPanel?: PrimaryTextProps
    image?: ImageType
    notSellableFits?: Set<string>
    cmsATCLocation?: number
    variant?: string
    theme?: string
}

export const SalesPopupCard: FC<SalesPopupCardProps> = ({
    product,
    relatedProducts = [],
    bundleProduct,
    className,
    style,
    backProduct,
    closeClicked,
    bundleClicked,
    textPanel,
    image,
    notSellableFits,
    cmsATCLocation = 1,
    theme
}) => {
    const { t } = useTranslation('common')

    const [isTablet, setIsTablet] = useState(false)

    const hasMerch = product && !!relatedProducts.length

    useEffect(() => {
        const handleResize = () => {
            setIsTablet(window.innerWidth <= BREAKPOINT_TABLET_MAX)
        }

        handleResize()
        window.addEventListener('resize', handleResize)

        return () => {
            window.removeEventListener('resize', handleResize)
        }
    }, [])

    const {
        sizeConfig,
        updateConfig,
        selectedProduct,
        selectedFit
    } = useMerchConfiguratorCategorySelection({
        productsData: hasMerch ? [product!, ...relatedProducts] : undefined,
        defaultSelectedFit:
            getFitFromSKU(product?.sku) === 'M'
                ? MERCH_CONFIG_FIT.MEN
                : MERCH_CONFIG_FIT.WOMEN,
        defaultSelectedSize: getSizeFromSKU(product?.sku)
    })
    const productToDisplay =
        (selectedProduct as
            | ConfigurableProductPylot
            | SimpleProductPylot
            | null) ?? product

    const isBundle = backProduct || bundleProduct
    const { isOutOfStock } = useProductUI(product)

    const { format } = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency:
            bundleProduct?.price_range?.minimum_price?.final_price?.currency ??
            'USD'
    })

    const productImage = useMemo(() => {
        if (productToDisplay) {
            if (productToDisplay.media_gallery) {
                const foundPopupImage = productToDisplay.media_gallery.find(
                    (img) => {
                        return img?.type === 'ymal'
                    }
                )
                if (foundPopupImage && foundPopupImage.url) {
                    return foundPopupImage
                }
            }
            return productToDisplay.image
        }
        return null
    }, [productToDisplay])

    const productDescription =
        textPanel && textPanel.bodyCopy
            ? textPanel.bodyCopy
            : productToDisplay.description?.html?.toString()

    const headline = textPanel?.headline ?? productToDisplay.name?.toString()

    const subheaderHtml =
        textPanel?.subheader ??
        productToDisplay?.short_description?.html?.toString()

    const overlayBadgeText =
        hasMerch && notSellableFits?.has(selectedFit) && textPanel?.calloutTitle
            ? textPanel?.calloutTitle
            : undefined
    return (
        <div className={cn('flex flex-col', className)}>
            <div
                style={style}
                className={cn(
                    'flex flex-col rounded-xl overflow-hidden flex-grow bg-white',
                    {
                        [s['sales-popup-card__neo']]: theme === 'neo'
                    }
                )}
                data-sku={productToDisplay?.sku}
            >
                <div
                    className={cn(
                        { ['h-full']: theme !== 'neo' },
                        {
                            [s['sales-popup-card__neo-background']]:
                                theme === 'neo'
                        }
                    )}
                >
                    <div
                        className={cn(
                            'relative overflow-hidden rounded-b-xxl',
                            {
                                'h-5/12 md:h-2/5 xxl:h-1/2': isBundle,
                                'h-3/5 md:h-1/2': !isBundle
                            },
                            {
                                [s['sales-popup-card__neo--image']]:
                                    theme === 'neo'
                            }
                        )}
                    >
                        {overlayBadgeText && (
                            <div className={s['sales-popup-card__overlay']}>
                                <Badge
                                    className={
                                        s['sales-popup-card__image-badge']
                                    }
                                    // eslint-disable-next-line i18next/no-literal-string
                                    size="large"
                                    // eslint-disable-next-line i18next/no-literal-string
                                    textClassName="h5"
                                >
                                    {overlayBadgeText}
                                </Badge>
                            </div>
                        )}
                        {!image && productImage && productImage.url && (
                            <Image
                                src={productImage.url.toString()}
                                alt={
                                    productImage.label?.toString() ||
                                    headline ||
                                    ''
                                }
                                layout="fill"
                                objectFit="cover"
                                // eslint-disable-next-line i18next/no-literal-string
                                sizes="432px"
                            />
                        )}
                        {image && image.file.url && (
                            <Image
                                src={image.file.url}
                                alt={image.description || ''}
                                layout="fill"
                                objectFit="cover"
                                // eslint-disable-next-line i18next/no-literal-string
                                sizes="432px"
                            />
                        )}
                        {backProduct && (
                            <Badge
                                className="bg-purple-plum-1 absolute top-8px left-8px"
                                // eslint-disable-next-line i18next/no-literal-string
                                size="bundle"
                            >
                                {/* eslint-disable-next-line i18next/no-literal-string */}
                                <span role="img" aria-label="fire">
                                    &#x1F525;
                                </span>
                                {t('BUNDLE DEAL')}
                            </Badge>
                        )}
                    </div>
                    <div
                        className={cn('bg-white', {
                            'h-7/12 md:h-3/5 xxl:h-1/2': isBundle,
                            'h-3/5 md:h-1/2': !isBundle,
                            [s['sales-popup-card-body__neo']]: theme === 'neo'
                        })}
                    >
                        <Button
                            variant="icon-tertiary"
                            className={cn(
                                'absolute top-12px right-12px',
                                s['sales-popup-card__button'],
                                {
                                    [s['sales-popup-card__button__neo']]:
                                        theme === 'neo'
                                }
                            )}
                            id="sales-popup-card__close"
                            onClick={closeClicked}
                            // eslint-disable-next-line i18next/no-literal-string
                            aria-label="close the product popup"
                            label={t('Close')}
                        >
                            {/* eslint-disable-next-line i18next/no-literal-string */}
                            <Icon name="close" />
                            {/* eslint-disable-next-line i18next/no-literal-string */}
                            {!isTablet && theme === 'neo' && (
                                // eslint-disable-next-line i18next/no-literal-string
                                <Icon name="close" />
                            )}
                        </Button>
                        <TextBuy
                            product={
                                overlayBadgeText && productToDisplay
                                    ? ({
                                          ...productToDisplay,
                                          stock_status: 'OUT_OF_STOCK'
                                      } as ProductInterface)
                                    : productToDisplay
                            }
                            headline={headline}
                            subheaderHtml={subheaderHtml}
                            bodyCopyHtml={
                                hasMerch ? undefined : productDescription
                            }
                            bundleProducts={
                                backProduct &&
                                productToDisplay?.bundle_products?.length
                                    ? (productToDisplay?.bundle_products as ProductInterface[])
                                    : ([] as ProductInterface[])
                            }
                            productLink={
                                backProduct
                                    ? undefined
                                    : productToDisplay?.url_key?.toString()
                            }
                            merchCategorySelectionComponent={
                                hasMerch && product.sku ? (
                                    <SalesPopupCardMerchCategorySelection
                                        sizeConfig={sizeConfig}
                                        updateConfig={updateConfig}
                                        sku={product.sku}
                                    />
                                ) : undefined
                            }
                            link={textPanel?.link}
                            theme={theme}
                            cmsATCLocation={cmsATCLocation}
                        />
                    </div>
                </div>
            </div>
            {/*
            {isBundle && !isOutOfStock && (
                <BundleCallout
                    className="mt-8px flex-shrink-0"
                    variant={backProduct ? 'back' : 'default'}
                    text={
                        backProduct?.name ||
                        format(
                            bundleProduct?.price_range?.minimum_price?.discount
                                ?.amount_off ?? 0
                        )
                    }
                    image={bundleProduct?.image?.url?.toString()}
                    onClick={() =>
                        bundleClicked?.call(this, {
                            id: backProduct?.id,
                            product: bundleProduct
                        })
                    }
                />
            )}
            */}
        </div>
    )
}

export default SalesPopupCard
