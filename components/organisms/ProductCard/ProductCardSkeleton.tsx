import React from 'react'
import s from './ProductCard.module.scss'
import Skeleton from 'react-loading-skeleton'
import cn from 'classnames'
type ProductCardSkeletonProps = {
    numberOfCards: number
}
const ProductCardSkeleton: React.FC<ProductCardSkeletonProps> = (props) => {
    const { numberOfCards } = props
    const tmpArray = new Array(numberOfCards).fill(0)
    return (
        <ul className="flex flex-wrap gap-16px justify-center md:py-32 py-16">
            {tmpArray.map((_, i) => {
                return (
                    <li
                        key={i}
                        className={cn(
                            'list-none rounded-xxl p-16px md:p-32px',
                            s['skeleton']
                        )}
                    >
                        <div className="flex justify-between">
                            <div className="flex items-center">
                                <Skeleton
                                    width={28}
                                    height={28}
                                    className="mr-5 rounded-full"
                                />
                                <Skeleton height={20} width={70} />
                            </div>
                            <Skeleton width={25} height={25} />
                        </div>
                        <div>
                            <Skeleton height={208} width={380} />
                        </div>
                        <div className="flex justify-center items-center">
                            <Skeleton
                                height={43}
                                width={151}
                                className="mt-8"
                            />
                        </div>
                        <div>
                            <Skeleton height={24} width={50} />
                            <Skeleton height={33} />
                        </div>
                        <div className="mt-2">
                            <Skeleton height={18} />
                        </div>
                    </li>
                )
            })}
        </ul>
    )
    return (
        <li
            className={cn(
                'list-none rounded-xxl p-16px md:p-32px',
                s['skeleton']
            )}
        >
            <div className="flex justify-between">
                <div className="flex items-center">
                    <Skeleton
                        width={28}
                        height={28}
                        className="mr-5 rounded-full"
                    />
                    <Skeleton height={20} width={70} />
                </div>
                <Skeleton width={25} height={25} />
            </div>
            <div>
                <Skeleton height={208} width={380} />
            </div>
            <div className="flex justify-center items-center">
                <Skeleton height={43} width={151} className="mt-8" />
            </div>
            <div>
                <Skeleton height={24} width={50} />
                <Skeleton height={33} />
            </div>
            <div className="mt-2">
                <Skeleton height={18} />
            </div>
        </li>
    )
}

export default ProductCardSkeleton
