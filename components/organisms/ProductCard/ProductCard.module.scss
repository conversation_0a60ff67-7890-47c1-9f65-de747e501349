.product-card {
    box-shadow: 0 0 16px 0 rgba($color: #000000, $alpha: 0.08);

    &__header {
        min-height: 48px;
    }

    &__media {
        aspect-ratio: 1.85;
    }

    & &__price {
        @apply flex items-center;
        min-height: 4.1rem;
    }

    & &__buy-btn {
        min-width: 120px;
        justify-content: center;

        &.no-animation {
            animation: none !important;
            transition: none !important;
        }
    }

    &--app {
        .product-card__media {
            aspect-ratio: 180 / 320;
            max-width: 180px;
            margin-left: auto;
            margin-right: auto;
            width: 100%;

            @screen md {
                aspect-ratio: 240 / 426;
                max-width: 240px;
            }
        }
    }

    &__downloads {
        @screen md {
            width: calc(33.33% - 1.1rem) !important;
        }
    }

    &__beta {
        @screen md {
            width: calc(33.33% - 1.1rem) !important;
        }
    }

    &__logo {
        height: 28px;
    }
    &__headline{
        line-height: 0;
    }
}

.skeleton{
    box-shadow: 0 0 16px 0 rgba(0, 0, 0, .08);
    @screen md {
        min-width: 45rem;
        max-width: 100% / 12 * 4;
    }
}