import React, { FC, useCallback, useEffect, useRef, useState } from 'react'
import {
    CompatibilityLegendItem,
    CompatibilityLegendItemEnum
} from '@components/molecules/CompatibilityLegendItem/CompatibilityLegendItem'
import cn from 'classnames'
import s from './CompatibilitySpecsDropdown.module.scss'
import ChevronDownIcon from '@components/atoms/Icon/general/ChevronDownIcon'
import { CompatibilityTextSpecsRowProps } from '@components/molecules/CompatibilityTextSpecsRow/CompatibilityTextSpecsRow'
import { CompatibilityTextSpecsRowList } from '@components/organisms/CompatibilityTextSpecsRowList/CompatibilityTextSpecsRowList'
import { CameraType } from '@components/organisms/CameraCheckDropdowns/CameraCheckDropdowns'
import { TeleprompterCompatibilityLegendItem } from '@components/molecules/TeleprompterCompatibilityLegendItem/TeleprompterCompatibilityLegendItem'

export type CompatibilitySpecsDropdownProps = {
    headline: string
    variant?: CompatibilityLegendItemEnum
    specifications?: CompatibilityTextSpecsRowProps[]
    camera?: CameraType
}

// export type TeleprompterCompatibilityDropdownProps = {
//     headline: string
//     variant?: TeleprompterCompatibilityLegendItemEnum
//     specifications?: TeleprompterCompatibilityDropdownProps[]
//     camera?: CameraType
// }

/* eslint i18next/no-literal-string: ["error", { "ignoreAttribute": ["size"] }] */
export const CompatibilitySpecsDropdown: FC<CompatibilitySpecsDropdownProps> = (
    props
) => {
    const {
        variant = CompatibilityLegendItemEnum.FULL,
        headline,
        specifications = []
    } = props
    const contentRef = useRef<HTMLDivElement>(null)
    const [isActive, setIsActive] = useState(false)
    const [open, setOpen] = useState(false)
    const CLOSED_HEIGHT = '0px'
    const [height, setHeight] = useState(CLOSED_HEIGHT)
    const toggleDropdown = useCallback(() => {
        const active = isActive
        if (contentRef.current) {
            if (!active) {
                const newHeight = contentRef.current.scrollHeight
                setHeight(`${newHeight}px`)
                setTimeout(() => {
                    setOpen(true)
                }, 300)
            } else {
                setOpen(false)
                setHeight(CLOSED_HEIGHT)
            }
        }
        setIsActive(!active)
    }, [isActive])
    useEffect(() => {
        if (!contentRef.current) return
        const handleResize = () => {
            if (isActive && contentRef.current) {
                setHeight(`${contentRef.current.scrollHeight}px`)
            }
        }
        window.addEventListener('resize', handleResize)
        return () => window.removeEventListener('resize', handleResize)
    }, [isActive, contentRef.current])
    const keyPressHandler = (e: React.KeyboardEvent<HTMLDivElement>) => {
        e.preventDefault()
        if (e.code === 'Space' || e.code === 'Enter') {
            toggleDropdown()
        }
    }
    return (
        <div
            className={cn(s['compatibility-specs-dropdown'], {
                [s['compatibility-specs-dropdown--active']]: isActive,
                [s['compatibility-specs-dropdown--open']]: open && isActive
            })}
        >
            <div
                className={s['compatibility-specs-dropdown__header']}
                onClick={toggleDropdown}
                onKeyPress={keyPressHandler}
                role="button"
                tabIndex={0}
            >
                <TeleprompterCompatibilityLegendItem
                    variant={variant}
                    label={headline}
                    size="large"
                    active={isActive}
                />
                <ChevronDownIcon
                    className={cn(s['compatibility-specs-dropdown__arrow'])}
                />
            </div>
            <div
                className={s['compatibility-specs-dropdown__content']}
                style={{ height: height }}
                ref={contentRef}
            >
                <div className={s['compatibility-specs-dropdown__inner']}>
                    {specifications.length > 0 && (
                        <CompatibilityTextSpecsRowList
                            headline={headline}
                            specifications={specifications}
                        />
                    )}
                </div>
            </div>
        </div>
    )
}
