import { Icon } from '@components/atoms/Icon/Icon'
import cn from 'classnames'
import { FC, useRef } from 'react'
import s from './TabToggles.module.scss'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import { pushLinkToDataLayer } from 'helpers/pushLinkToDataLayer'

interface TabToggle {
    icon?: string
    calloutTitle?: string
}

interface TabToggleProps {
    tabs?: TabToggle[]
    activeTab?: number
    setActiveTab: (tab: TabToggle, index: number) => void
    className?: string
    togglePosition?: 'left' | 'right' | 'center'
}

export const TabToggles: FC<TabToggleProps> = ({
    tabs,
    activeTab,
    setActiveTab,
    className,
    togglePosition
}) => {
    const active = activeTab ?? 0
    const tabRefs = useRef<(HTMLDivElement | null)[]>([])
    const { isMobile } = useMobile()
    return (
        <div
            className={cn(
                'bg-primitive-gray-20 rounded-xxl lg:rounded-3xl w-fit p-8px relative',
                className,
                s['tab-toggles'],
                s[`tab-toggles--${togglePosition}`]
            )}
        >
            <div
                className={cn(
                    'bg-primitive-gray-20 lg:max-w-full h-full flex justify-between relative',
                    s['tab-toggles--wrapper']
                )}
            >
                {tabs?.map((toggle, index) => {
                    return (
                        <div
                            key={index}
                            ref={(el) => {
                                tabRefs.current[index] = el
                            }}
                            className={cn(
                                'hover:bg-primitive-gray-40 hover:text-black flex-1 flex gap-2 flex-row items-center justify-center rounded-xl w-fit px-16px lg:px-24px py-12px z-1 relative p-4px transition-colors duration-300 cursor-pointer',
                                {
                                    'text-white': active === index,
                                    [s['tab-toggles--active']]: active === index
                                },
                                s['tab-toggles__item']
                            )}
                            role="button"
                            tabIndex={0}
                            onKeyPress={() => setActiveTab(toggle, index)}
                            onClick={() => {
                                setActiveTab(toggle, index)
                                pushLinkToDataLayer(tabRefs)
                                if (isMobile) {
                                    tabRefs.current[index]?.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'nearest',
                                        inline: 'center'
                                    })
                                }
                            }}
                        >
                            {toggle.icon && (
                                <Icon
                                    name={toggle.icon}
                                    className={cn(s['tab-toggles__icon'])}
                                />
                            )}
                            <div
                                className={cn(
                                    'whitespace-nowrap',
                                    s['tab-toggles__calloutTitle'],
                                    'elgato-links'
                                )}
                            >
                                {toggle.calloutTitle}
                            </div>
                        </div>
                    )
                })}
            </div>
        </div>
    )
}
