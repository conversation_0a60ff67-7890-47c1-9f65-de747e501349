.neo-product-card {

    &__wrapper {
        display: flex;
        flex-direction: column;
    }

    &__price {
        text-align: left;
        transition: 300ms;
        width: 0%;
        opacity: 0;
        display: none;

    }

    &__minicart {
        transition: 300ms;
        background: black;
        position: absolute;
        padding-left: 13px;
        padding-right: 13px;
        width: 48px;
        height: 48px;

        @screen 1200c {
            width: 48px;
            height: 48px;
            bottom: 32px;
            left: 20px;
            padding-left: 13px;
            padding-right: 13px;
        }
        @screen 1440c {
            padding-left: 13px;
            padding-right: 13px;
            width: 48px;
            height: 48px;
            bottom: 40px;
            left: 20px;
        }
        @screen 1920c {
            padding-left: 16px;
            padding-right: 16px;
            width: 56px;
            height: 56px;
            bottom: 40px;
            left: 40px;
        }
    }

    &__minicart:hover &__carticon {
        fill: rgb(12, 37, 136);
    }

    &__minicart:hover {
        background: var(--primaries-light-blue);
        delay: 400;
        duration: 500;
        cursor: pointer;

        :global {
            .price-discount-custom {
                color: rgb(12, 37, 136) !important;
                cursor: pointer;
            }
        }
    }

    &__internal:hover &__carticon {
        display: none;

        @screen 1440c {
            display: block;
        }
    }

    &__internal:hover &__overlay {
        opacity: 15%;
    }

    &__internal:hover &__minicart  {
        opacity: 1;
        width: calc(100% - 40px);
        padding-left: 16px;
        padding-right: 16px;

        @screen 1920c {
            width: calc(100% - 80px);
            height: 56px;
        }
    }

    &__internal:hover &__price  {
        flex: 1;
        display: inline;
        opacity: 1;
        width: 100%;
        padding-left: 4px;
    }

    :global {
        .price-discount-custom-strike {
            @media screen and (min-width: 1200px) and (max-width: 1440px) {
                font-size: 12px !important;
                line-height: 75% !important;
                margin-bottom: 10px !important;
            }
        }

        .price-discount-custom {
            //color: white !important;
            cursor: pointer;
            font-size: 2rem;

            @media screen and (min-width: 1200px) and (max-width: 1440px) {
                display: flex;
                flex-direction: column;
                gap: 0px;
                align-items: center;
                color: white !important;
                font-size: 16px !important;
            }

            @screen 1440c {
                display: flex;
                flex-direction: row;
                gap: 5px;
                align-items: center;
                color: white !important;
            }
        }
    }

    &__learnmore {
        padding: unset !important;
        height: 48px;
        width: 132px;
        font-size: 16px;
        line-height: 24px;

        @screen 1200c  {
            height: 48px;
            width: 132px;
            font-size: 16px;
            line-height: 24px;
        }
        @screen 1440c {
            height: 48px;
            width: 135px;
            font-size: 16px;
            line-height: 24px;
        }
        @screen 1920c {
            height: 56px;
            width: 195px;
            font-size: 24px;
            line-height: 24px;
        }
    }

    &__media {
        border-radius: 24px;
        overflow: hidden;
        @screen md {
            border-radius: 32px;
        }
        @screen lg {
            border-radius: 64px;
        }

    }

    &__product-title {
        white-space: nowrap;
        padding: 1.6rem 0;
        font-size: 1.6rem;


        @screen md {
            padding: 2.4rem 0;
            font-size: 2.8rem;
        }
        @screen lg {
            font-size: 2rem;
        }
        @screen lg2 {
            font-size: 21px;
        }

        @screen xxl {
            padding: 2.4rem 1.6rem;
            font-size: 3.2rem;
        }
    }
}
