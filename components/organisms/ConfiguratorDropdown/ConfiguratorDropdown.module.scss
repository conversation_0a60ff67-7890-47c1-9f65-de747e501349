.configurator-dropdown {
    &__header {
        min-height: 46px;
        background: var(--bg-grey);
        border-radius: 6px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 0 20px;
        align-items: center;
    }

    &__content {
        margin-top: 8px;
        background: var(--bg-grey);
        border: 1px solid var(--light-grey-1);
        box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.06);
        border-radius: 8px;
        padding: 16px 20px;
    }

    &__icon {
        @apply transform transition-transform duration-500 rotate-0;
    }

    &--open {
        .configurator-dropdown__icon {
            outline: none !important;
            @apply -rotate-180;
        }
    }
}
