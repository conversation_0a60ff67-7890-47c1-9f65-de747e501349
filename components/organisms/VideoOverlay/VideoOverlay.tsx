import s from './VideoOverlay.module.scss'
import cn from 'classnames'
import React, { Dispatch, FC, useEffect, useRef } from 'react'
import {
    CaptionFileType,
    DescriptionFileType,
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import Video from '@components/molecules/Video/Video'
import { Overlay } from '@components/organisms/Overlay/Overlay'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'

export type VideoOverlayProps = {
    playButtonText?: string
    thumb?: ImageType
    video?: VideoType
    videoCaptionFile?: CaptionFileType
    mediaEmbedded?: string
    className?: string
    isOpen?: boolean
    setIsOpen?: Dispatch<React.SetStateAction<boolean>>
    onClose?: () => void
    meta?: {
        contentType: string
    }
    posterImage?: ImageType
    videoDescriptionFile?: DescriptionFileType
    cloudinaryVideo?: CloudinaryMedia[]
}

export const VideoOverlay: FC<VideoOverlayProps> = (props) => {
    const {
        video,
        videoCaptionFile,
        className,
        isOpen,
        mediaEmbedded,
        setIsOpen,
        onClose,
        posterImage,
        videoDescriptionFile,
        cloudinaryVideo
    } = props
    //consider to take ctf video or cloudinary video if both are existing
    //we will take ctf video as default
    const videoUrl = cloudinaryVideo?.[0]?.secure_url || video?.file.url
    const closeButtonStyle = 'content-center-top'
    const widthMatch = mediaEmbedded?.match(/width="([^"]+)"/)
    const heightMatch = mediaEmbedded?.match(/height="([^"]+)"/)
    const videoRef = useRef<HTMLVideoElement>(null)
    let aspectRatio: string | undefined = undefined
    if (widthMatch && heightMatch) {
        try {
            const width = parseInt(widthMatch[1], 10)
            const height = parseInt(heightMatch[1], 10)
            aspectRatio = `${width / height}`
        } catch {
            // do nothing
        }
    }
    useEffect(() => {
        if (isOpen) {
            videoRef?.current?.play()
        } else {
            videoRef?.current?.pause()
        }
    }, [isOpen])
    return (
        <Overlay
            className={cn(s['video-overlay'], className)}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            onClose={onClose}
            closeButtonStyle={closeButtonStyle}
        >
            {video && (
                <div className={s['video-overlay__wrapper']}>
                    <video
                        className={s['video-overlay__video']}
                        controls
                        playsInline
                        preload="metadata"
                        src={videoUrl}
                        ref={videoRef}
                    >
                        <track
                            default
                            kind="captions"
                            src={videoDescriptionFile?.file?.url || ''}
                        />
                    </video>
                </div>
            )}
            {mediaEmbedded && (
                <div
                    className={s['video-overlay__embedded']}
                    style={{
                        aspectRatio: aspectRatio
                    }}
                    dangerouslySetInnerHTML={{
                        __html: mediaEmbedded
                    }}
                />
            )}
            {/* placeholder for last element*/}
            <p
                aria-label="end of modal, return to close button"
                role="navigation"
                // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
                tabIndex={0}
            />
        </Overlay>
    )
}

export default VideoOverlay
