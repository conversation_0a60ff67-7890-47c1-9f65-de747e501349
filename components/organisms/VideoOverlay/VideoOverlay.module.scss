.video-overlay {
    & div:first-child {
        @apply py-16px md:py-32px;

        button {
            @apply min-h-0;
            svg {
                @apply w-24px h-24px;
            }
        }
    }

    &__wrapper, &__embedded {
        @apply mx-auto px-16px w-full;
        margin-top: 28px;
        max-height: calc(100% - 44px - 28px);

        @screen md {
            @apply px-32px;
        }

        @screen lg {
            @apply px-16px;
            max-width: 1490px + 16px + 16px;
        }
    }

    &__video, &__wrapper video {
        @apply rounded-xxl overflow-hidden;
        @apply mx-auto w-auto;
        max-height: 100%;
    }

    &__embedded {
        :global {
            iframe {
                @apply w-full h-full;
                @apply rounded-xxl overflow-hidden;
                max-height: 100%;
            }
        }
    }
}
