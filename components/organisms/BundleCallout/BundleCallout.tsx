import s from './BundleCallout.module.scss'
import React, { FC } from 'react'
import cn from 'classnames'
import { Icon } from '@components/atoms/Icon/Icon'
import Image from '@corsairitshopify/corsair-image'
import { Badge } from '@components/atoms/Badge/Badge'
import { useTranslation } from 'next-i18next'

export interface BundleCalloutProps {
    image?: string
    text: string
    className?: string
    variant?: 'default' | 'back'
    onClick?: () => void
}

export const BundleCallout: FC<BundleCalloutProps> = ({
    image,
    text,
    className,
    variant = 'default',
    onClick
}) => {
    const { t } = useTranslation('common')

    return (
        <button
            className={cn(
                'button-text bg-white rounded-xl flex items-center p-4px w-full text-left',
                s['bundle-callout'],
                className
            )}
            type="button"
            onClick={onClick}
        >
            {variant === 'back' && (
                <BundleCalloutBack backText={t('Back to')} product={text} />
            )}
            {variant === 'default' && (
                <BundleCalloutDefault
                    saveText={t('Save')}
                    bundleText={t('Bundle deal available!')}
                    saveMoney={text}
                    image={image}
                />
            )}
        </button>
    )
}

export default BundleCallout

const BundleCalloutBack: FC<{ backText: string; product: string }> = ({
    product,
    backText
}) => {
    return (
        <>
            <Icon
                // eslint-disable-next-line i18next/no-literal-string
                name="chevronRight"
                className="w-24px h-24px rotate-180 transform mr-8px ml-4px"
            />
            <div>
                {backText} {product}
            </div>
        </>
    )
}

const BundleCalloutDefault: FC<{
    saveMoney: string
    image?: string
    saveText: string
    bundleText: string
}> = ({ saveMoney, image, saveText, bundleText }) => {
    return (
        <>
            <div
                className={cn(
                    'relative h-full mr-8px md:mr-16px rounded-md overflow-hidden flex-shrink-0',
                    s['bundle-callout__image']
                )}
            >
                {image && (
                    <Image
                        src={image}
                        alt={bundleText || ''}
                        layout="fill"
                        objectFit="cover"
                    />
                )}
            </div>
            <div className="flex gap-4px flex-col mr-auto items-start">
                <Badge className="bg-purple-plum-1 h-8 items-center">
                    {saveText} {saveMoney}
                </Badge>
                <h5>{bundleText}</h5>
            </div>
            <Icon
                className="w-24px h-24px mr-12px flex-shrink-0"
                // eslint-disable-next-line i18next/no-literal-string
                name="chevronRight"
            />
        </>
    )
}
