import React, { FC } from 'react'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import cn from 'classnames'
import s from './SocialMediaLinks.module.scss'
import { Icon } from '@components/atoms/Icon/Icon'
import { useTranslation } from 'next-i18next'

export type SocialMediaLinksProps = {
    type?: string
    socialMediaLinks: LinkResponse[]
    color?: 'dark' | 'light'
    bgColor?: string
    size?: 'default' | 'large' | 'larger' | 'huge'
    gap?: 'default' | 'large'
    className?: string
}

export const SocialMediaLinks: FC<SocialMediaLinksProps> = (props) => {
    const {
        socialMediaLinks,
        color,
        bgColor,
        size = 'default',
        gap = 'default',
        className
    } = props
    let iconSizeClass = 'w-34px h-34px'
    if (size !== 'default' && size !== 'larger') {
        iconSizeClass = 'xl:w-34px xl:h-34px'
    }
    const iconColor = color === 'dark' ? `text-youtube-black` : 'text-white'
    const { t } = useTranslation(['common'])
    return (
        <ul
            className={cn(
                s['social-links'],
                {
                    [s[`social-links--gap-${gap}`]]: gap && gap !== 'default'
                },
                className
            )}
        >
            {socialMediaLinks.map((link, i) => (
                <li key={i}>
                    <Link
                        link={link}
                        className={cn(s['social-links__link'], {
                            [`bg-${bgColor}`]: bgColor,
                            rounded: size === 'default',
                            'rounded-lg': size !== 'default',
                            [s[`social-links__link--${size}`]]:
                                size !== 'default'
                        })}
                        aria-label={t('ada|Opens in the current Tab')}
                    >
                        <Icon
                            name={link.icon ? link.icon : ''}
                            className={cn(iconColor, iconSizeClass)}
                        />
                    </Link>
                </li>
            ))}
        </ul>
    )
}
