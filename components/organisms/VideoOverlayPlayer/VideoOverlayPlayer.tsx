import React, { FC, ReactChild } from 'react'
import {
    CaptionFileType,
    DescriptionFileType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import { Button } from '../../molecules/Button/Button'
import { Icon } from '../../atoms/Icon/Icon'
import { videoSupportType } from '@components/templates/FullWidthImageSlider/FullWidthImageSlider'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'

export type VideoOverlayPlayerProps = {
    playButtonText?: string
    video: VideoType
    autoPlay?: boolean
    showVideo: (video: VideoType | undefined) => void
    videoCaptionFile?: CaptionFileType
    videoDescriptionFile?: DescriptionFileType
    fallbackImgUrl?: string
    setIsOpenVideoOverlay: React.Dispatch<React.SetStateAction<boolean>>
    setVideoSupportFile: React.Dispatch<React.SetStateAction<videoSupportType>>
    cloudinaryMedia?: CloudinaryMedia
}

export const VideoOverlayPlayer: FC<VideoOverlayPlayerProps> = (props) => {
    const {
        video,
        playButtonText,
        showVideo,
        videoCaptionFile,
        videoDescriptionFile,
        fallbackImgUrl,
        setIsOpenVideoOverlay,
        setVideoSupportFile,
        cloudinaryMedia
    } = props
    const showVideoOverlay = () => {
        showVideo(video)
        setIsOpenVideoOverlay(true)
        setVideoSupportFile({
            captionFile: videoCaptionFile,
            description: videoDescriptionFile
        })
    }

    return (
        <div className="relative flex items-center justify-center w-full h-full">
            <div className="absolute w-full h-full z-2 top-0 left-0">
                <video
                    src={cloudinaryMedia?.secure_url}
                    className="h-full w-full object-cover"
                    autoPlay
                    muted
                    loop
                    playsInline
                    preload="none"
                    poster={fallbackImgUrl}
                >
                    <track kind="captions" />
                </video>
            </div>
            <Button
                onClick={showVideoOverlay}
                className="absolute z-3"
                label={playButtonText}
            >
                {/* eslint-disable-next-line i18next/no-literal-string */}
                <Icon name="play" />
                {playButtonText}
            </Button>
        </div>
    )
}
