@import '../Header/_header-vars';

.header-main-menu {
    @apply flex flex-col overflow-hidden;
    max-width: 100vw;
    @media screen and (min-width: $nav-desktop-md-min-width) {
        transition: color 0.3s ease;
    }

    @media screen and (min-width: $nav-desktop-md-min-width) {
        @apply flex-row items-center mx-auto gap-24px;
        max-width: none;
    }

    &-wrapper {
        z-index: 200;
        position: relative;
        @media screen and (min-width: $nav-desktop-md-min-width) {
            transition: height 1s linear;
        }
    }

    &--light {
        @apply text-white;
    }

    &__item-mobile,
    &__logged-out-nav {
        @media (min-width: $nav-desktop-md-min-width) {
            @apply hidden;
        }
    }
}

/* page theme dark */
.header-main-menu.page-theme-dark:not(.header-main-menu--active) {
    @media screen and (min-width: $nav-desktop-md-min-width) {
        color: var(--white);
    }
}
