import { CodComparisonCard } from '@components/organisms/CodCardList/CodComparisonCard'
import { VideoOverlayProps } from '@components/organisms/VideoOverlay/VideoOverlay'
import classNames from 'classnames'
import { FC } from 'react'
import PrimaryText, {
    PrimaryTextProps
} from '../../molecules/PrimaryText/PrimaryText'
import { CardProps } from '../../templates/CardList/CardList'
import {
    SectionBgColor,
    SectionThemeDarkBgColors
} from '../../templates/Section/Section'
import { Container, ContainerSize } from '../Container/Container'
import s from './CodCardList.module.scss'

interface ComparisonCardsProps {
    cards: CardProps[]
    variant: 'default' | 'onImage'
    textPanel?: PrimaryTextProps
    backgroundColor: SectionBgColor
    id?: string
    removePadding?: boolean
}

export const ComparisonCards: FC<ComparisonCardsProps> = ({
    cards,
    variant,
    textPanel,
    backgroundColor,
    id,
    removePadding
}) => {
    const outerTextColor = SectionThemeDarkBgColors.includes(backgroundColor)
        ? 'light'
        : 'dark'
    return (
        <div
            className={backgroundColor}
            id={id}
            style={{
                backgroundImage:
                    'url("https://res.cloudinary.com/elgato-pwa/image/upload/v1727169450/2024/Call%20of%20Duty/Landing%20Page/Background/Asset_1_1.png")',
                opacity: 1
            }}
        >
            <Container size={ContainerSize.LARGE}>
                <div
                    className={classNames(
                        'grid flex-col w-full gap-16px py-16 md:py-32',
                        s['comparison-cards'],
                        { 'py-0 md:py-0': removePadding }
                    )}
                    style={{
                        opacity: 1
                    }}
                >
                    {textPanel && (
                        <PrimaryText
                            headline={textPanel.headline}
                            bodyCopy={textPanel.bodyCopy}
                            subheader={textPanel.subheader}
                            disclaimerText={textPanel.disclaimerText}
                            backgroundColor={textPanel.backgroundColor}
                            calloutTitle={textPanel.calloutTitle}
                            calloutTag={textPanel.calloutTag}
                            headlineStyle={textPanel.headlineStyle}
                            headlineTag={textPanel.headlineTag}
                            link={textPanel.link}
                            smallCopy={textPanel.smallCopy}
                            textColor={outerTextColor}
                            className={classNames(
                                'mb-16 md:mb-32 col-span-4 md:col-span-6 lg:col-span-12 xl:mx-auto',
                                s['comparison-cards__text-panel']
                            )}
                        />
                    )}

                    <div
                        className={classNames(
                            'row-start-2 flex justify-center gap-24px md:gap-16px md-max:flex-col col-span-4 md:col-span-6 ',
                            {
                                'lg:col-span-12': variant === 'onImage',
                                'lg:col-span-10 lg:col-start-2 xl:col-span-8 xl:col-start-3':
                                    variant === 'default'
                            }
                        )}
                    >
                        {cards.map((card, i) => {
                            const isVideo =
                                card.cloudinaryMedia?.[0]?.resource_type ==
                                'video'
                            const preTitleColor = card.backgroundColor
                                ? SectionThemeDarkBgColors.includes(
                                      card.backgroundColor as SectionBgColor
                                  )
                                    ? 'light'
                                    : 'dark'
                                : outerTextColor
                            const textColor = card.textColor ?? preTitleColor
                            const cardChildren = card.children
                            let videoOverlay:
                                | VideoOverlayProps
                                | undefined = undefined
                            if (cardChildren && cardChildren.length) {
                                cardChildren.forEach(
                                    (child: VideoOverlayProps) => {
                                        if (
                                            child.meta?.contentType ===
                                            'organismVideoOverlay'
                                        ) {
                                            videoOverlay = child
                                        }
                                    }
                                )
                            }
                            return (
                                <CodComparisonCard
                                    card={card}
                                    key={`compare-card-${card.textPanel?.headline}-${i}`}
                                    variant={variant}
                                    videoOverlay={videoOverlay}
                                    textColor={textColor}
                                    preTitleColor={preTitleColor}
                                    isVideo={isVideo}
                                />
                            )
                        })}
                    </div>
                </div>
            </Container>
        </div>
    )
}

export default ComparisonCards
