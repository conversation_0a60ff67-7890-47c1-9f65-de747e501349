import s from './CodCardList.module.scss'
import React, { FC, useState } from 'react'
import cn from 'classnames'
import Image from '@corsairitshopify/corsair-image/index'
import { ComparisonText } from '@components/molecules/ComparisonText/ComparisonText'
import { CardProps } from '@components/templates/CardList/CardList'
import {
    VideoOverlay,
    VideoOverlayProps
} from '@components/organisms/VideoOverlay/VideoOverlay'
import { Button } from '@components/molecules/Button/Button'
import PlayIcon from '@components/atoms/Icon/general/PlayIcon'
import ElgatoImage from '@components/common/ElgatoImage'
import ElgatoVideo from '@components/common/ElgatoVideo/ElgatoVideo'
import { useTranslation } from 'next-i18next'

export type ComparisonCardProps = {
    card: CardProps
    variant: 'onImage' | 'default'
    videoOverlay?: VideoOverlayProps
    textColor: string
    preTitleColor: string
    isVideo: boolean
}

export const CodComparisonCard: FC<ComparisonCardProps> = (props) => {
    const {
        card,
        variant,
        videoOverlay,
        textColor,
        isVideo,
        preTitleColor
    } = props
    const [videoOverlayOpen, setVideoOverlayOpen] = useState(false)
    const { t } = useTranslation('common')
    const openVideoOverlay = () => {
        setVideoOverlayOpen(true)
    }
    const closeVideoOverlay = () => {
        setVideoOverlayOpen(false)
    }
    let playButton = undefined
    if (videoOverlay && videoOverlay.playButtonText) {
        playButton = (
            <Button onClick={openVideoOverlay} label={t('Play')}>
                <PlayIcon />
                {videoOverlay.playButtonText}
            </Button>
        )
    }
    const hasPreTitle = (card.textPanel?.preTitle?.length ?? 0) > 0
    return (
        <div
            className={cn(
                card.backgroundColor ?? '',
                'rounded-3xl w-full flex justify-end flex-col gap-24px',
                {
                    'p-16px pt-24px md:p-16': variant !== 'onImage'
                }
            )}
            style={{
                backgroundColor: 'black',
                width: '587px'
            }}
        >
            {/* ---- Icon + Pretitle START ---- */}
            {(hasPreTitle || card.textPanel?.logoImage?.file?.url) && (
                <div className="flex flex-col gap-16px">
                    {hasPreTitle && (
                        <div
                            className={cn('flex justify-around', {
                                'text-white': preTitleColor === 'light'
                            })}
                        >
                            {card.textPanel?.preTitle?.map((title, i) => (
                                <h5
                                    className="text-center"
                                    style={{
                                        width: `${
                                            100 /
                                            card.textPanel!.preTitle!.length
                                        }%`
                                    }}
                                    key={`compare-card-title-${title}-${i}`}
                                >
                                    {title}
                                </h5>
                            ))}
                        </div>
                    )}
                    {card.textPanel?.logoImage?.file?.url && (
                        <div className="relative w-32 h-32 overflow-hidden mx-auto flex-shrink-0">
                            <Image
                                src={card.textPanel?.logoImage.file.url}
                                alt={
                                    card.textPanel?.logoImage?.description || ''
                                }
                                layout="fill"
                                objectFit="cover"
                            />
                        </div>
                    )}
                </div>
            )}
            {/* ---- Icon + Pretitle END ---- */}
            <div
                className={cn('relative flex flex-col gap-24px', {
                    'md:grid md:h-full gap-0': variant === 'onImage'
                })}
            >
                {(card.textPanel?.subheader ||
                    card.textPanel?.calloutTitle ||
                    card.textPanel?.headline ||
                    card.textPanel?.bodyCopy ||
                    card.textPanel?.disclaimerText ||
                    card.textPanel?.link) && (
                    <ComparisonText
                        className={cn(s['comparison-card__text'], {
                            'self-center': card.textVerticalAlignment !== 'top',
                            'mt-16px':
                                variant !== 'onImage' &&
                                !card.textPanel?.logoImage?.file?.url &&
                                card.textPanel?.preTitle &&
                                card.textPanel?.preTitle?.length > 0,
                            'md-max:mt-16px':
                                !card.textPanel?.logoImage?.file?.url &&
                                card.textPanel?.preTitle &&
                                card.textPanel?.preTitle?.length > 0,
                            'md:col-start-1 md:row-start-1 md:px-16px md:py-16 z-1 top-0 left-0 right-0 bottom-0 flex justify-center items-center flex-col':
                                variant === 'onImage',
                            'md:self-start':
                                card.textVerticalAlignment === 'top',
                            'md:text-white': textColor === 'light',
                            'md-max:text-white': preTitleColor === 'light'
                        })}
                        pretitle={card.textPanel?.subheader}
                        calloutTitle={card.textPanel?.calloutTitle}
                        title={card.textPanel?.headline}
                        text={card.textPanel?.bodyCopy}
                        smallCopy={card.textPanel?.disclaimerText}
                        link={card.link}
                        sku={card.sku}
                    >
                        {playButton}
                    </ComparisonText>
                )}
                {card.cloudinaryMedia?.[0]?.secure_url && (
                    <div
                        className={cn({
                            'md:col-start-1 md:row-start-1':
                                variant === 'onImage',
                            [s['comparison-card__image--on-image']]:
                                variant === 'onImage'
                        })}
                    >
                        {!isVideo && card.cloudinaryMedia?.[0] && (
                            <>
                                <div
                                    className={cn('w-full h-full', {
                                        'hidden md:block':
                                            card.cloudinaryMobileMedia?.[0]
                                    })}
                                >
                                    <ElgatoImage
                                        className="rounded-xl"
                                        src={
                                            card.cloudinaryMedia?.[0]
                                                ?.secure_url
                                        }
                                        alt={
                                            card.cloudinaryMedia?.[0]?.context
                                                ?.custom?.alt || ''
                                        }
                                        width={card.cloudinaryMedia?.[0]?.width}
                                        height={
                                            card.cloudinaryMedia?.[0]?.height
                                        }
                                        layout="responsive"
                                        objectFit="cover"
                                    />
                                </div>
                                {card.cloudinaryMobileMedia?.[0] && (
                                    <div className="w-full h-full md:hidden">
                                        <ElgatoImage
                                            className="rounded-xl "
                                            src={
                                                card.cloudinaryMobileMedia?.[0]
                                                    ?.secure_url
                                            }
                                            alt={
                                                card.cloudinaryMobileMedia?.[0]
                                                    ?.context?.custom?.alt || ''
                                            }
                                            width={
                                                card.cloudinaryMobileMedia?.[0]
                                                    ?.width
                                            }
                                            height={
                                                card.cloudinaryMobileMedia?.[0]
                                                    ?.height
                                            }
                                            layout="responsive"
                                            objectFit="cover"
                                        />
                                    </div>
                                )}
                            </>
                        )}
                        {isVideo && card.cloudinaryMedia?.[0] && (
                            <ElgatoVideo
                                className="rounded-xl overflow-hidden"
                                secure_url={
                                    card.cloudinaryMedia?.[0]?.secure_url
                                }
                                options={{
                                    autoPlay: true,
                                    preload: 'auto',
                                    muted: true,
                                    loop: true
                                }}
                                fallbackImgUrl={
                                    card.cloudinaryPosterImage?.[0]?.secure_url
                                }
                            />
                        )}
                    </div>
                )}
            </div>
            {videoOverlay && videoOverlay?.video && (
                <VideoOverlay
                    video={videoOverlay.video}
                    isOpen={videoOverlayOpen}
                    setIsOpen={setVideoOverlayOpen}
                    onClose={closeVideoOverlay}
                    posterImage={videoOverlay.posterImage}
                />
            )}
        </div>
    )
}
