/* eslint-disable i18next/no-literal-string */
import { LinkResponse } from '@components/molecules/Link/Link'
import { SalesBannerProps } from '@components/molecules/SalesBanner/SalesBanner'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import { ProductInterface } from '@pylot-data/pylotschema'
import React, { CSSProperties, FC } from 'react'
import { PrimaryTextProps } from '@components/molecules/PrimaryText/PrimaryText'
import { ProductStorePrototypeCard } from '@components/organisms/StorePrototypeCard/ProductStorePrototypeCard'
import { SlideShowStorePrototypeCard } from '@components/organisms/StorePrototypeCard/SlideShowStorePrototypeCard'
import { InfoOverlayStorePrototypeCard } from '@components/organisms/StorePrototypeCard/InfoOverlayStorePrototypeCard'
import { IconStorePrototypeCard } from '@components/organisms/StorePrototypeCard/IconStorePrototypeCard'
import { InfoStorePrototypeCard } from '@components/organisms/StorePrototypeCard/InfoStorePrototypeCard'
import { VideoStorePrototypeCard } from '@components/organisms/StorePrototypeCard/VideoStorePrototypeCard'
import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'

export type StorePrototypeCardProps = {
    className?: string
    headline?: string
    text?: string
    subheader?: string
    link?: LinkResponse
    cardLink?: LinkResponse
    product?: null | ProductInterface
    media?: ImageType | VideoType
    mobileMedia?: ImageType | VideoType
    cloudinaryMedia?: CloudinaryMedia[]
    cloudinaryMobileMedia?: CloudinaryMedia[]
    children?: any[]
    posterImage?: ImageType
    variant?: string
    textColor?: 'dark' | 'light'
    banner?: SalesBannerProps
    hideDiscover?: boolean
    ratio?: string
    backgroundColor?: string
    style?: CSSProperties
    textVerticalAlignment?: string
    copyCode?: string
    shortDescription?: string
    id?: string
    tracking?: boolean
    textPanel?: PrimaryTextProps
    videoDescription?: string
}

export const StorePrototypeCard: FC<StorePrototypeCardProps> = (props) => {
    const { variant } = props

    if (variant === 'Product') {
        return <ProductStorePrototypeCard {...props} />
    }
    if (variant === 'Icons') {
        return <IconStorePrototypeCard {...props} />
    }
    if (variant === 'Info') {
        return <InfoStorePrototypeCard {...props} />
    }
    if (variant === 'InfoOverlay') {
        return <InfoOverlayStorePrototypeCard {...props} />
    }
    if (variant === 'Slideshow') {
        return <SlideShowStorePrototypeCard {...props} />
    }
    if (variant === 'Video') {
        return <VideoStorePrototypeCard {...props} />
    }
    return null
}
