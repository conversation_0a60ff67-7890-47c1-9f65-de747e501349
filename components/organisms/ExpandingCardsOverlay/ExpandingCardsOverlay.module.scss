.expanding-cards-container {
    @screen md {
        padding: 6.4rem 3.2rem;
    }

    @screen lg {
        padding: 6.4rem 2.4rem; // horizontal padding = 3.2rem - 0.8rem (card horizontal padding)
    }

    @screen lg2 {
        padding: 8rem 5.6rem; // horizontal padding = 6.4rem - 0.8rem (card horizontal padding)
    }
}

.expanding-cards {

    &__wrapper {

        @screen lg-max {
            display: flex;
            flex-direction: column;
        }
    }
}

.expanding-card {

    @screen lg {
        transition: width 0.3s ease-out;
        padding: 0 0.8rem;
    }

    @screen lg-max {
        width: 100% !important;
    }

    &--expanded {

        @screen lg {
            width: 50% !important;
        }
    }
}
