import s from './DropdownCardList.module.scss'
import {
    CardListProps,
    CardProps
} from '@components/templates/CardList/CardList'

import { FC } from 'react'
import { DropdownCard } from './DropdownCard'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import cn from 'classnames'

type DropdownCardListProps = Pick<CardListProps, 'backgroundColor'> & {
    cards: CardProps[]
    id?: string
}

const DropdownCardList: FC<DropdownCardListProps> = ({
    cards,
    backgroundColor,
    id
}) => {
    return (
        <div
            className={cn(s['dropdown-card-list'], backgroundColor, {
                [s['dropdown-card-list--rounded']]: id === 'wave-neo-card-list'
            })}
        >
            <Container size={ContainerSize.XLARGE}>
                <div
                    className={cn(s['dropdown-card-list__cards'], {
                        [s['dropdown-card-list__cards--rounded']]:
                            id === 'wave-neo-card-list'
                    })}
                >
                    {cards.map((card, index) => {
                        const videoOverlay = card.children?.find((child) => {
                            if (
                                child.meta?.contentType ===
                                'organismVideoOverlay'
                            ) {
                                return true
                            }
                        })
                        return (
                            <DropdownCard
                                key={`dropdown-card-${index}}`}
                                headline={card.textPanel?.headline}
                                subheader={card.textPanel?.subheader}
                                bodyCopy={card.textPanel?.bodyCopy}
                                media={card.media}
                                backgroundColor={card.backgroundColor}
                                posterImage={card.posterImage}
                                posterImageMobile={card.posterImageMobile}
                                videoOverlay={videoOverlay}
                                videoOptions={card.videoOptions}
                                mediaEmbedded={card.mediaEmbedded}
                                cardListId={id}
                                link={card.textPanel?.link}
                                customOptions={card?.customOptions}
                            />
                        )
                    })}
                </div>
            </Container>
        </div>
    )
}

export default DropdownCardList
