import { TextToggleProps } from '@components/atoms/TextToggle/TextToggle'
import { ViewToggleProps } from '@components/atoms/ViewToggle/ViewToggle'
import { ViewToggles } from '@components/molecules/ViewToggles/ViewToggles'
import { CardProps } from '@components/templates/CardList/CardList'
import { DropdownPanelTabContentProps } from '@components/templates/DropdownPanel/DropdownPanel'
import cn from 'classnames'
import { FC, useState, CSSProperties } from 'react'
import s from './TabsFullWidthTogglesSide.module.scss'
import { ElgatoMedia } from '@components/common/ElgatoMedia/ElgatoMedia'
import { decode } from 'he'
import { useMediaQuery } from '@config/hooks/useMediaQuery'

export type TabsFullWidthTogglesSideProps = {
    tabs?: DropdownPanelTabContentProps[]
    id?: string
}

const TextBox = ({
    headline,
    bodyCopy,
    className,
    style
}: {
    headline?: string
    bodyCopy?: string
    className?: string
    style?: CSSProperties
}) => {
    const isDesktop = useMediaQuery('(min-width: 1200px)')
    return (
        <div
            className={cn(
                'p-24px rounded-xxl flex flex-col gap-12px w-full',
                {
                    'bg-white text-black': isDesktop,
                    'bg-primitive-gray-20 text-black': !isDesktop
                },
                className
            )}
            style={style}
        >
            <h5
                className="text-h5"
                dangerouslySetInnerHTML={{
                    __html: decode(headline ?? '')
                }}
            />
            <p
                className="text-body-copy-md-max"
                dangerouslySetInnerHTML={{
                    __html: decode(bodyCopy ?? '')
                }}
            />
        </div>
    )
}

export const TabsFullWidthTogglesSide: FC<TabsFullWidthTogglesSideProps> = ({
    tabs,
    id
}) => {
    const isDesktop = useMediaQuery('(min-width: 1200px)')
    const [activeTabIndex, setActiveTabIndex] = useState(0)
    const [stopPlayingVideo, setStopPlayingVideo] = useState(false)
    const toggles: ViewToggleProps[] | TextToggleProps[] =
        tabs?.map((tab, index) => {
            return {
                tabIndex: index,
                text: tab.headline,
                thumbnail: tab.thumbnail,
                theme: tab.theme
            } as ViewToggleProps | TextToggleProps
        }) || []

    const setSelectedTab = (i: number) => {
        setActiveTabIndex(i)
        setStopPlayingVideo(!stopPlayingVideo)
    }
    console.log('tabs', tabs)

    return (
        <div
            className={cn(s['tabs-full-width-toggles-side'], 'pb-0 bg-white')}
            id={id}
        >
            <div
                className={cn(
                    'flex flex-col-reverse lg2:flex-row mx-auto pt-0 md:px-64px lg2:pb-64px lg2:max-w-full pb-8 gap-16px'
                )}
                aria-controls={id}
            >
                <ViewToggles
                    toggles={toggles as ViewToggleProps[]}
                    activeTab={activeTabIndex}
                    variant="full-width"
                    onChange={setSelectedTab}
                    className="md:pt-16px"
                />
                <div className={cn('w-full px-16px md:px-0')}>
                    {tabs &&
                        tabs.length > 0 &&
                        tabs.map((tab, i) => {
                            const card = tab?.children?.[0] as CardProps

                            return (
                                <div
                                    key={tab.headline}
                                    className={cn(
                                        'h-full flex flex-col gap-16px',
                                        {
                                            'hidden opacity-0 duration-100':
                                                activeTabIndex !== i,
                                            'block opacity-100':
                                                activeTabIndex === i
                                        }
                                    )}
                                    role="tabpanel"
                                    id={`tabPanelIndex_${i}`}
                                    aria-labelledby={`tabIndex_${i}`}
                                    style={{
                                        maxHeight: '70vh'
                                    }}
                                >
                                    <ElgatoMedia
                                        cloudinaryMedia={card.cloudinaryMedia}
                                        cloudinaryMobileMedia={
                                            card.cloudinaryMobileMedia
                                        }
                                        cloudinaryPosterImage={
                                            card.cloudinaryPosterImage
                                        }
                                        cloudinaryPosterImageMobile={
                                            card.cloudinaryPosterImageMobile
                                        }
                                        objectFit="cover"
                                        loop
                                        sizing="constrain-ratio"
                                        className={cn(
                                            s[
                                                'tabs-full-width-toggles-side__media'
                                            ],
                                            'rounded-xxxl overflow-hidden'
                                        )}
                                    >
                                        {isDesktop && (
                                            <div className="absolute inset-24px">
                                                <TextBox
                                                    headline={
                                                        card.textPanel?.headline
                                                    }
                                                    bodyCopy={
                                                        card.textPanel?.bodyCopy
                                                    }
                                                    className="absolute bottom-0 transform -translate-x-1/2 left-1/2 xxl:w-1/2"
                                                />
                                            </div>
                                        )}
                                    </ElgatoMedia>
                                    {!isDesktop && (
                                        <TextBox
                                            headline={card.textPanel?.headline}
                                            bodyCopy={card.textPanel?.bodyCopy}
                                        />
                                    )}
                                </div>
                            )
                        })}
                </div>
            </div>
        </div>
    )
}
