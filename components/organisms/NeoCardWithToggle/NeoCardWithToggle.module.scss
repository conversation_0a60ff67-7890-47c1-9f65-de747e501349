.neo-card-with-toggle {
    display: flex;
    flex-direction: column;
    gap: 40px;
    margin-bottom: 0;

    &__container {
        @media screen and (max-width: 425px) {
            position: relative;
        }
        padding: 4rem 1.6rem;
        @screen md {
            padding: 6.4rem 3.2rem;
        }

        @screen lg2 {
            padding: 8rem 6.4rem;
        }

        display: flex;
        flex-direction: column;
        gap: 40px;
    }

    &__video-container {
        overflow: hidden;
        width: 100%;
        position: relative;
        border-radius: 40px;

        @screen md {
            border-radius: 40px;
        }

        @screen lg {
            border-radius: 64px;
        }

        video {
            width: 100%;
            height: 100%;
        }
    }

    &__video-1,
    &__video-2,
    &__mobile-video-1,
    &__mobile-video-2,
    {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        overflow: hidden;
        z-index: 1;
        display: block;
    }

    &__poster-image {
        top: 0;
        position: absolute;
        width: 100%;
        height: 100%;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;

    }

    &__video-2-hidden,
    &__video-1-hidden,
    &__mobile-video-2-hidden {
        opacity: 0;
        transition: opacity 100ms ease-in-out;
    }

    &__toggle-title-toggle-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 8px;

        @screen md {
            display: none;
        }
    }

    &__text-media-controls-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding-top: 24px;
        position: relative;
        padding-bottom: 40px;

        @screen md {
            padding-top: 40px;
            gap: 40px;
        }

        @screen lg {
            gap: 24px;
            padding-bottom: 0;
        }
    }

    &__text {
        @apply font-univers55Roman;
        color: var(--primitive-black);
        font-size: 28px;
        line-height: 40px;

        @screen md {
            margin-top: 4px;
            font-size: 28px;
            line-height: 32px;
        }

        @screen lg {
            font-size: 40px;
            line-height: 48px;
        }

        @media (min-width: 1440px) {
            margin-top: 0;
            font-size: 48px;
            line-height: 64px;
        }
    }

    &__toggle-group {
        display: flex;
        flex-direction: row;
        gap: 16px;
        @screen md-max {
            flex-direction: column;
        }
    }

    &__toggle-video-control-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    &__video-control {
        display: inline-flex;
        gap: 16px;
        position: absolute;
        top: -(16px + 40px);
        right: 16px;
        z-index: 1;

        @screen md {
            top: -(16px + 56px);
        }

        @screen lg {
            position: static;
        }
    }

    .neo-card-with-toggle__toggle-title {
        @apply font-univers55Roman;
        font-style: italic;
        font-size: 16px;
        line-height: 24px;

        @screen md {
            text-align: right;
            font-size: 20px;
            line-height: 32px;
        }

        @screen lg {
            display: none;
        }
    }

    &__paused,
    &__muted {
        background-color: var(--black);
        color: var(--white);

        svg {
            &:first-child {
                opacity: 0 !important;
                transform: translateY(100%) !important;
            }

            &:nth-child(2) {
                opacity: 1 !important;
                transform: translateY(0) !important;
            }
        }
    }

    &__pause-icon,
    &__volume-icon {
        width: 40px;
        height: 40px;
        display: flex;
        padding: 8px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        border-radius: 100%;
        background: var(--Primitives-Black, #000);

        @screen md {
            width: 56px;
            height: 56px;
        }

        @screen lg {
            width: 64px;
            height: 64px;
        }

        svg {
            width: 24px;
            height: 24px;
            @screen lg {
                width: 32px;
                height: 32px;
            }
            // When it is not muted
            &:not(.neo-card-with-toggle__muted),
            &:not(.neo-card-with-toggle__paused) {
                color: var(--white);

                &:first-child {
                    transform: translateY(0);
                    opacity: 1;
                    transition: all 0.2s;
                }

                &:nth-child(2) {
                    opacity: 0;
                    transform: translateY(-100%);
                    transition: all 0.2s;
                    position: absolute;
                }
            }

            //When it is muted
            &.neo-card-with-toggle__muted,
            &.neo-card-with-toggle__paused {
                &:first-child {
                    transform: translateY(100%);
                    opacity: 1;
                    transition: all 0.2s;
                }

                &:nth-child(2) {
                    opacity: 0;
                    transform: translateY(0);
                    transition: all 0.2s;
                    position: absolute;
                }
            }

        }

        &:hover {
            // when it is not muted
            &:not(.neo-card-with-toggle__muted),
            &:not(.neo-card-with-toggle__paused) {
                @screen lg {
                    background-color: var(--primaries-mid-blue);
                }
                svg:first-child {
                    transform: translateY(100%);
                    opacity: 0;
                }

                svg:nth-child(2) {
                    transform: translateY(0);
                    opacity: 1;

                    @screen lg {
                        color: var(--primitive-blue-120);
                    }
                }
            }

            // when it is muted
            &.neo-card-with-toggle__muted,
            &.neo-card-with-toggle__paused
            {
                @screen lg {
                    background-color: var(--primaries-mid-blue);
                }
                svg:first-child {
                    display: block;
                    transform: translateY(0) !important;
                    opacity: 1 !important;
                    @screen lg {
                        color: var(--primitive-blue-120) !important;
                    }
                }

                svg:nth-child(2) {
                    transform: translateY(100%) !important;
                    opacity: 0 !important;
                }
            }
        }

    }
}
