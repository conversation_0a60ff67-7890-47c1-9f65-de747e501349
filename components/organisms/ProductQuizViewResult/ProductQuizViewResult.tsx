import React, { FC, isValidElement, useEffect } from 'react'
import s from './ProductQuizViewResult.module.scss'
import cn from 'classnames'
import ProductQuizViewResultCard from '@components/organisms/ProductQuizViewResultCard/ProductQuizViewResultCard'

interface ProductQuizViewResultProps {
    index?: number
    resultValues?: any
    productCards?: any
    clickedValues?: any
}

const ProductQuizViewResult: FC<ProductQuizViewResultProps> = ({
    index,
    productCards
}) => {
    return (
        <div className={cn(s['product-quiz-view-result'])}>
            {productCards &&
                productCards.map((card: any, i: number) => (
                    <ProductQuizViewResultCard
                        key={i}
                        index={i}
                        image={card.image}
                        video={card.video}
                        posterImage={card.posterImage}
                        title={card.name}
                        productTitle={card.productTitle}
                        productSubtitle={card.productSubtitle}
                        sku={card.sku}
                        variant={card.variant}
                        videoCaptionFile={card?.videoCaptionFile}
                        videoDescriptionFile={card?.videoDescriptionFile}
                    />
                ))}
        </div>
    )
}

export default ProductQuizViewResult
