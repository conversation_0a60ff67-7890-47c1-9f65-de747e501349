.newsletter-popup {
    @apply bg-content-blue text-white;
    position: fixed;
    bottom: 0;
    padding: 26px;
    z-index: 2000;
    transition: 1s ease-out;
    max-width: 517px;
    width: 100%;
    transform: translate(-100%,0);
    opacity: 0;

    @screen md {
        min-width: 500px;
        bottom: 35px;
        border-radius: 0px 12px 12px 0px;
        box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.16);
    }

    &--open {
        opacity: 1;
        transform: translate(0,0);
    }

    &__title {
        @apply flex gap-24px justify-between items-center;
    }

    &__title + &__subtitle {
        @apply mt-16px;
    }

    &__form {
        @apply mt-24px;

        :global {
            .btn {
                @apply bg-white w-full text-center mt-24px justify-center;
            }
        }
    }
    :global {
        input {
            opacity: 0.5;
            background: transparent none !important;
            color: white;
            padding: 0 0 12px;
            border-radius: 0 !important;
            border-top: none !important;
            border-left: none !important;
            border-right: none !important;
            border-bottom: 1px solid var(--white);
            outline: none;

            ::placeholder {
                color: white;
                opacity: 1;
            }

            :-ms-input-placeholder {
                color: white;
            }

            ::-ms-input-placeholder {
                color: white;
            }
        }

        input:focus, input:active, input:focus-visible {
            opacity: 1;
            background: transparent none;
            border-bottom: 1px solid var(--white);
        }
    }

    :global {
        .overlay-close {
            @apply text-white transition-opacity duration-300;

            &:hover:not([disabled]) {
                @apply text-white;
                opacity: 0.5;
            }
        }
    }

    &--submitted {
        :global {
            .btn {
                opacity: 0.5;
            }
        }
    }

    &__message {
        @apply mt-16px;
        font-family: "Univers67BoldCondensed";
        font-weight: normal;
    }
}
