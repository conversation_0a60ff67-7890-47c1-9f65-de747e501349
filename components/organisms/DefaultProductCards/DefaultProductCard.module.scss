.default-product-card {
    &__wrapper {
        display: flex;
        flex-direction: column;
    }

    &__price {
        text-align: left;
        transition: 300ms;
        width: 0%;
        opacity: 0;
        display: none;
    }

    &__minicart {
        transition: 300ms;
        background: black;
        position: absolute;
        padding-left: 13px;
        padding-right: 13px;
        width: 56px;
        height: 56px;

        @screen 1200c {
            bottom: 32px;
            left: 20px;
        }
        @screen 1440c {
            padding-left: 16px;
            padding-right: 16px;
            bottom: 40px;
            left: 40px;
        }
    }

    &__minicart:hover &__carticon {
        fill: var(--white);
    }

    &__minicart:hover {
        background: var(--primitive-gray-120);
        delay: 400;
        duration: 500;
        cursor: pointer;

        :global {
            .price-discount-custom {
                color: var(--white) !important;
                cursor: pointer;
            }
        }
    }

    &__internal:hover &__carticon {
        display: none;

        @screen 1440c {
            display: block;
        }
    }

    &__internal:hover &__overlay {
        opacity: 15%;
    }

    &__internal:hover &__minicart {
        opacity: 1;
        padding-left: 16px;
        padding-right: 16px;
        height: 56px;

        @screen 1200c {
            width: calc(100% - 40px);
        }
        @screen 1440c {
            width: calc(100% - 80px);
        }
        @screen 1920c {
            width: calc(100% - 80px);
        }
    }

    &__internal:hover &__price {
        flex: 1;
        display: inline;
        opacity: 1;
        width: 100%;
        padding-left: 4px;
    }

    :global {
        .price-discount-custom {
            //color: white !important;
            cursor: pointer;
            font-size: 24px;
            font-family: 'univers55Roman' !important;

            @screen 1200c {
                display: flex;
                flex-direction: column;
                gap: 0px;
                align-items: center;
                color: white !important;
                font-size: 24px;
                font-weight: 400 !important;
                font-family: 'univers55Roman' !important;
            }

            @screen 1440c {
                display: flex;
                flex-direction: row;
                gap: 5px;
                align-items: center;
                font-size: 24px;
                font-weight: 400 !important;
                font-family: 'univers55Roman' !important;
            }
        }
    }

    &__learnmore {
        padding: unset !important;
        height: 43px;
        width: 130px;
        font-size: 16px;
        line-height: 16px;
        border-radius: 6px;
        border-width: 1px !important;

        @screen 1200c {
            height: 43px;
            width: 130px;
            font-size: 16px;
            line-height: 24px;
        }
        @screen 1440c {
            height: 43px;
            width: 130px;
            font-size: 16px;
            line-height: 16px;
        }
        @screen 1920c {
            height: 43px;
            width: 130px;
            font-size: 16px;
            line-height: 16px;
        }
        &:hover {
            border-color: var(--primitive-blue-110) !important;
            color: var(--primitive-blue-110) !important;
            background: transparent !important;
        }
    }

    &__media {
        border-radius: 12px;
    }

    &__product-title {
        @screen md {
            padding: 3.2rem 0;
            font-size: 2.4rem;
        }

        @screen md-max {
            font-size: 2.4rem;
        }

        @screen xxl {
            padding: 3.2rem 1.6rem;
            font-size: 3.2rem;
        }
    }
}
