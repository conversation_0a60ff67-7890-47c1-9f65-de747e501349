import { CardProps } from '@components/templates/CardList/CardList'
import { FC, useRef } from 'react'
import cn from 'classnames'
import s from './DefaultProductCards.module.scss'
import { A11y, Keyboard, Mousewheel, Navigation } from 'swiper'
import { SwiperSlide } from 'swiper/react'
import dynamic from 'next/dynamic'
import {
    ContainerSize,
    Container
} from '@components/organisms/Container/Container'
import SliderArrows from '@components/molecules/SliderArrows/SliderArrows'
import { DefaultProductCard } from '@components/organisms/DefaultProductCards/DefaultProductCard'
import { useMobile } from '@pylot-data/hooks/use-mobile'

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

interface DefaultProductCardProps {
    id?: string
    classNames?: string
    cards?: CardProps[]
    arrowStyle?: 'neo' | 'normal'
    capitalizeHeadlines?: boolean
}

export const DefaultProductCards: FC<DefaultProductCardProps> = ({
    cards,
    classNames,
    arrowStyle = 'neo',
    capitalizeHeadlines
}) => {
    const sliderRef = useRef<HTMLDivElement | null>(null)
    const { isMobile, isTablet } = useMobile()

    const sliderSettings = {
        spaceBetween: 16,
        slidesPerView: 'auto',
        loop: false,
        modules: [A11y, Navigation, Mousewheel, Keyboard],
        allowTouchMove: true,
        navigation: {
            prevEl: '.product-swiper-button-prev',
            nextEl: '.product-swiper-button-next'
        },
        keyboard: true,
        mousewheel: {
            forceToAxis: true
        }
    }

    const horizontalPadding = !isMobile ? 16 : 8
    const maxPanelWidth = 1024 - horizontalPadding
    const cardMediaWidth = maxPanelWidth / 2
    const mediaStyle: React.CSSProperties = isTablet
        ? { maxWidth: cardMediaWidth }
        : {}

    return (
        <Container
            size={ContainerSize.XLARGE}
            className={cn('overflow-hidden')}
        >
            <div
                className={cn(s['default-product-cards'], 'flex gap-16px')}
                ref={sliderRef}
            >
                {isTablet ? (
                    <SwiperSlider
                        loop={false}
                        settings={sliderSettings}
                        className={cn(s['default-product-cards-slider'])}
                    >
                        <div id="default-product-cards-slider">
                            {cards?.map((card, index) => {
                                return (
                                    <SwiperSlide key={`card${index}`}>
                                        <DefaultProductCard
                                            key={`${card}-${index}`}
                                            card={card}
                                            index={index}
                                            classNames={cn(
                                                s[
                                                    'default-product-cards-slider__item'
                                                ],
                                                'overflow-hidden',
                                                classNames
                                            )}
                                            mediaStyle={mediaStyle}
                                        />
                                    </SwiperSlide>
                                )
                            })}
                        </div>
                        {arrowStyle === 'normal' ? (
                            <SliderArrows
                                style="normal"
                                classNameNext="product-swiper-button-next"
                                classNamePrev="product-swiper-button-prev"
                            />
                        ) : (
                            <SliderArrows
                                classNameNext="product-swiper-button-next"
                                classNamePrev="product-swiper-button-prev"
                            />
                        )}
                    </SwiperSlider>
                ) : (
                    <div
                        id="default-product-cards-slider"
                        className={cn(
                            s['default-product-cards-slider'],
                            'flex flex-row gap-16px'
                        )}
                    >
                        {cards?.map((card, index) => (
                            <DefaultProductCard
                                key={`${card}-${index}`}
                                card={card}
                                index={index}
                                classNames={cn(
                                    s['default-product-cards-slider__item'],
                                    classNames
                                )}
                                mediaStyle={mediaStyle}
                                capitalize={capitalizeHeadlines}
                            />
                        ))}
                    </div>
                )}
            </div>
        </Container>
    )
}

export default DefaultProductCards
