.wave-in-the-box__container {
    //position: relative;
    //width: 100%;
    //height: 100%;
    //img {
    //    width: 100%;
    //    height: 100%;
    //}
}

.wave-in-the-box__inner {
    position: relative;
    width: 100%;
    height: 100%;

    img {
        width: 100%;
        height: 100%;
    }
}

.wave-in-the-box {
    background-color: transparent;
    border-radius: 24px;

    &__badge {
        background: var(--primaries-mid-blue);
        border-radius: 24px;
        gap: 16px;
        position: absolute;
        z-index: 10;
        top: 2.4rem;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        left: 2.4rem;
        margin-right: 2.4rem;
        padding: 1.6rem 3.2rem 1.6rem 1.6rem;
        word-break: break-all;
        hyphens: auto;

        @screen md-max {
            padding: 10px;
            top: 16px;
            left: 16px;
            width: 40px;
            height: 40px;
            margin-right: 16px;
        }

        @screen lg2 {
            border-radius: 3.2rem;
            top: 3.2rem;
            left: 3.2rem;
            margin-right: 3.2rem;
        }
    }

    &__icon {
        background: var(--white);
        border-radius: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 12px;

        @screen md-max {
            border-radius: 100%;
            bottom: 16px;
            right: 16px;
            padding: 8px;
        }
    }

    &__icon-text {
        color: var(--primitive-blue-120);
        font-size: 32px;
        line-height: 120%;
        font-style: normal;
        width: 100%;
        word-break: break-word;

        @screen md-max {
            display: none
        }

        sup {
            top: -1rem !important;
            font-size: 60% !important
        }
    }
}