.partner-card__info-rows {
    @media (min-width: theme('screens.md')) and (max-width: theme('screens.lg')){
        & > div {
            @apply flex-grow w-2/5;
        }
    }

    @screen lg {
        flex-direction: column;
        order: 2;
    }
}

.partner-card {
    display: grid;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 16px;

    @screen lg {
        display: grid;
        grid-template-columns: repeat(8, minmax(0, 1fr));
        margin-bottom: 0;
    }

    &__media-wrapper {
        @screen lg {
            height: 100%;
        }
    }

    &__content {
        display: flex;
        flex-direction: column;
        padding: 24px;
        @apply bg-bg-grey rounded-xxl col-span-4;

        @screen md {
            padding: 40px;
        }
    }

    &__social-links {
        order: 2;
        margin-bottom: 12px;
        margin-top: 4px;

        @screen lg {
            order: 3;
            margin-bottom: 0;
            margin-top: 40px;
        }
    }
}
