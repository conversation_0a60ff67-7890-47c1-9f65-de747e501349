.animate-product-content {
    &__wrapper {
        @screen md {
            max-width: 60%;
            padding-left: 15px;
        }
    }
    @screen md-max {
        padding-bottom: 0;
        padding-left: 19px;
        padding-right: 19px;
    }
    &__price {
        font-size: 3rem;
        color: red;

        :global {
            .price {
                .huge {
                    font-size: 2rem;
                }
            }
        }
    }
    &_link{
        padding: 5px 7px;
        margin: 0 20px 0 10px;
        margin-top: -7px;
        opacity: 0;
        transition: all 0.4s ease-out;
        align-items: center;
        justify-content: center;
        &.active{
            opacity: 1;
            transition: all 0.4s ease-out;
        }
        @media only screen and (max-width:767px)  {
            padding: 5px 10px;
        }
    }

    &_content {
        @screen md {
            transform: translateX(-65px);
            transition: all 0.3s ease-out;

            &.active {
                transform: translateX(0);
                transition: all 0.3s ease-out;
            }
        }
    }
    &_price-wrapper{
        transform: translateX(55px);
        transition: all 0.3s ease-out;
        &.active{
            transform: translateX(0);
            transition: all 0.3s ease-out;
        }
        &_discount-price div{
            color: #898989;
            text-decoration: line-through;
            font-size: 22px;
        }
        &_discount-price > div{
            margin-right: 10px;
        }
        &_price > div{
            margin-right: 10px;
        }
        &_price {
            font-size: 24px;
            color: #204CFE;
        }
        &__button{
            opacity: 0;
            transition: all 0.4s ease-out;
            button{
                padding: 5px 10px!important;
                svg{
                    width: 24px !important;
                    height: 24px !important;
                }
            }
            &.active{
                opacity: 1;
                transition: all 0.4s ease-out;
            }
        }
        @media only screen and (max-width:767px)  {
            transform: translateX(0);
            width: 100%;
            justify-content: flex-start;
            &__button{
                button{
                    padding: 0px 15px !important
                }
            }
        }
    }
    :global {
        .huge {
            font-size: 2rem;
        }
    }
    &__image {
        @apply relative;
        width: 58px;
        height: 58px;
        border-radius: 6px;
        overflow: hidden;
    }
    &__footer {
        display: flex;
        justify-content: space-between;

        & &__buy-button {
            min-width: 120px;
            justify-content: center;
            margin-left: auto;
        }

        & &__buy-button-white {
            background-color: var(--white);
            color: var(--primitive-black);
            a {
                color: var(--white);
            }
        }

        & &__discover-button {
            &__icon {
                width: 16px;
                height: 16px;
            }
        }
        & &__discover-button-white {
            color: var(--white) !important;
        }
    }

    &__content {
    }

    &__content + &__footer {
        @apply mt-16px;
    }

    & &__review[data-bv-show=rating_summary] {
        margin: 0;

        :global {
            .bv_main_container{
                .bv_main_container_row_flex {
                    margin: 0 !important;
                    &:last-child {
                        display: none !important;
                    }
                }
            }
        }
    }
}
