import { FC } from 'react'
import cn from 'classnames'
import { Icon } from '@components/atoms/Icon/Icon'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import Link from 'next/link'
import s from './AnimatedTextOnHover.module.scss'
import { CardProps } from '@components/templates/CardList/CardList'

interface AnimatedTextOnHoverProps {
    card?: CardProps
    active?: boolean
    onCardClick?: () => void
}

const AnimatedTextOnHover: FC<AnimatedTextOnHoverProps> = ({
    card,
    onCardClick,
    active
}) => {
    const { isMobile } = useMobile()
    const panel = card?.textPanel

    return (
        <div
            className={cn(
                'p-16px transition-transform duration-500 ease-in-out cursor-pointer',
                {
                    'bg-primitive-gray-10': active,
                    'bg-white': !active,
                    'pb-0': !active
                }
            )}
            onMouseEnter={onCardClick}
            onClick={onCardClick}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    onCardClick?.()
                }
            }}
            role="button"
            tabIndex={0}
        >
            <div className={cn('flex flex-col')}>
                <div className="relative">
                    {isMobile ? (
                        <div
                            className="absolute right-0 top-0"
                            onClick={onCardClick}
                            role="button"
                            tabIndex={0}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    onCardClick?.()
                                }
                            }}
                        >
                            <Icon
                                name="chevronDown"
                                className={cn(
                                    'transition-transform duration-500',
                                    {
                                        'transform rotate-180': active
                                    }
                                )}
                            />
                        </div>
                    ) : (
                        <div
                            className="absolute right-0 top-1"
                            onClick={() => {
                                if (typeof window !== 'undefined') {
                                    window.open(panel?.link?.linkUrl, '_blank')
                                }
                            }}
                            role="button"
                            tabIndex={0}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter' || e.key === ' ') {
                                    if (typeof window !== 'undefined') {
                                        window.open(
                                            panel?.link?.linkUrl,
                                            '_blank'
                                        )
                                    }
                                }
                            }}
                        >
                            <Icon
                                name="arrowRight"
                                className={cn(
                                    'transition-all duration-300 ease-in-out',
                                    {
                                        'opacity-0': !active,
                                        'opacity-100': active
                                    }
                                )}
                            />
                        </div>
                    )}
                    {!isMobile &&
                        panel &&
                        panel.subheader &&
                        panel.link?.linkUrl && (
                            <div className="absolute right-8 top-0 mr-4">
                                <Link href={panel.link.linkUrl}>
                                    <span className="body-regular text-dark-grey-2">
                                        {panel.subheader}
                                    </span>
                                </Link>
                            </div>
                        )}
                    <div
                        className={cn(
                            s['animated-text-on-hover__body-copy-container'],
                            {
                                [s[
                                    'animated-text-on-hover__body-copy-container--visible'
                                ]]: active
                            }
                        )}
                    >
                        {panel?.headline && (
                            <h4 className="mb-8px">{panel.headline}</h4>
                        )}
                        {panel?.calloutTitle && (
                            <h6 className="text-dark-grey-2 mb-16px">
                                {panel.calloutTitle}
                            </h6>
                        )}
                        {panel?.bodyCopy && (
                            <div
                                className={cn(
                                    s['animated-text-on-hover__body-copy'],
                                    {
                                        [s[
                                            'animated-text-on-hover__body-copy--visible'
                                        ]]: active
                                    }
                                )}
                                dangerouslySetInnerHTML={{
                                    __html: panel?.bodyCopy
                                }}
                            />
                        )}
                    </div>
                </div>
                {isMobile && (
                    <div className={cn('flex items-center')}>
                        {panel?.subheader && active && panel?.link?.linkUrl && (
                            <div className="flex items-center">
                                <Link href={panel.link.linkUrl}>
                                    {panel.subheader}
                                </Link>
                                <div
                                    onClick={() => {
                                        if (typeof window !== 'undefined') {
                                            window.open(
                                                panel.link?.linkUrl,
                                                '_blank'
                                            )
                                        }
                                    }}
                                    role="button"
                                    tabIndex={0}
                                    onKeyDown={(e) => {
                                        if (
                                            e.key === 'Enter' ||
                                            e.key === ' '
                                        ) {
                                            if (typeof window !== 'undefined') {
                                                window.open(
                                                    panel.link?.linkUrl,
                                                    '_blank'
                                                )
                                            }
                                        }
                                    }}
                                >
                                    <Icon
                                        name="arrowRight"
                                        className="ml-2 mt-2"
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    )
}

export default AnimatedTextOnHover
