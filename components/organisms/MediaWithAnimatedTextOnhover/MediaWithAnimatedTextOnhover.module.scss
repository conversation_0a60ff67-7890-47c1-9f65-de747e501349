.media-with-animated-text-onhover {
    max-width: 100%;
    img {
        border-radius: 16px;
        @screen md-max {
            height: 100%;
            // min-height: 230px;
        }
    }
    &__left-panel {
        @media only screen and (max-width: 1024px) {
            padding-top: calc(var(--sticky-nav-height--sm));
        }
    }
    &__media-container {
        // opacity: 0;
        transition: opacity 0.5s ease-in-out;
    }

    &__visible {
        opacity: 1;
    }
    &__badge {
        @screen md-max {
            width: 40px;
            height: 40px;
        }
        img {
            border-radius: 0;
            @screen lg-max {
                width: 40px;
                height: 40px;
            }
        }
    }
}
