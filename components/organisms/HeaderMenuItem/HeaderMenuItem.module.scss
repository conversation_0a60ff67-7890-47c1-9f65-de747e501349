@import '../Header/_header-vars';
// For Neo Menu Item
.header-menu-item-neo {
    &--level-0 {
        span {
            height: 47px !important;
        }

        &.header-menu-item::after {
            bottom: 10px !important;
            color: var(--primaries-dark-blue) !important;
            left: 11px !important;
        }

        &:hover {
            @media screen and (min-width: $nav-desktop-md-min-width) {
                @apply text-primaries-dark-blue #{!important};
                text-decoration: none;
                transition: color 0.3s ease;
            }

            &::after {
                @media screen and (min-width: $nav-desktop-md-min-width) {
                    background-color: var(--primaries-dark-blue) !important;
                    z-index: 10;
                    width: calc(100% - 24px) !important;
                }
            }
        }
    }
}

.header-menu-item-gift-guide {
    &--level-0 {
        &:hover {
            &::after {
                @media screen and (min-width: $nav-desktop-md-min-width) {
                    background-color: var(--red) !important;
                }
            }
        }

        &--color {
            color: #c02335 !important;
        }
    }
}

.header-menu-item-elgato-week {
    &--level-0 {
        color: var(--primitive-purple-80) !important;
        &:hover {
            &::after {
                @media screen and (min-width: $nav-desktop-md-min-width) {
                    background-color: var(--primitive-purple-80) !important;
                }
            }
        }
    }

    &--level-0--dark {
        color: var(--primitive-purple-40);
        &:hover{
            @media screen and (min-width: $nav-desktop-md-min-width) {
                color: var(--primitive-purple-80) !important;
                text-decoration: none;
                transition: color 0.3s ease;
            }
        }
        &:hover {
            &::after {
                @media screen and (min-width: $nav-desktop-md-min-width) {
                    background-color: var(--primitive-purple-80) !important;
                }
            }
        }
    }
}

.header-menu-item {
    position: relative;
    z-index: 100;
    @apply block;
    outline: none 0;
    border: transparent solid 3px;

    &:focus {
        border: var(--content-blue) solid 3px;
    }

    @media screen and (max-width: $nav-mobile-max-width) {
        padding: 8px 16px;
        border-top: 1px solid var(--light-grey-2);
        @apply flex gap-8px;
        align-items: center;
        min-height: 62px;
    }

    &__image--desktop {
        span {
            position: static !important;
        }

        img {
            // TODO Only for Desktop
            @apply static h-auto;
            position: static !important;
            height: auto !important;
        }
    }

    &:not(.header-menu-item--back) {
        @media screen and (max-width: $nav-mobile-max-width) {
            @apply justify-between;
        }
    }

    &__title-wrapper {
        @media screen and (max-width: $nav-mobile-max-width) {
            @apply flex gap-16px items-center;
        }
    }

    &__title {
        z-index: 10;
        @media screen and (max-width: $nav-mobile-max-width) {
            @apply font-univers67BoldCondensed;
            @apply text-h4-md-max;
            @apply leading-tight-md-max;
            @apply uppercase;
        }

        &--small {
            @media screen and (max-width: $nav-mobile-max-width) {
                @apply text-h5-md-max;
                @apply leading-tight-md-max;
            }
        }
    }

    &__label {
        background-color: var(--content-blue);
        @media screen and (min-width: 1200px) {
            position: absolute;
            right: 8px;
            top: 8px;
            z-index: 10;
        }
        @media screen and (min-width: 1792px) {
            top: 19px;
            right: 18px;
        }
    }

    &__neo-title {
        display: flex;
        padding: 10px 12px 10px 12px !important;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 2px;
        border-radius: 16px;
        background: var(--primaries-mid-blue);
        color: var(--primaries-dark-blue);
    }

    &__image-wrapper {
        @media screen and (max-width: $nav-mobile-max-width) {
            position: relative;
        }

        @media screen and (min-width: $nav-desktop-md-min-width) {
            border-radius: 4px;
            overflow: hidden;
        }

        img {
            object-fit: cover;
        }

        svg {
            width: 32px;
            height: 32px;
        }
    }

    &__image {
        &--desktop {
            display: none;

            @media screen and (min-width: $nav-desktop-md-min-width) {
                display: block;
                height: 100%;
                width: 100%;
            }
        }

        &--mobile {
            width: 45px;
            height: 45px;

            @media screen and (min-width: $nav-desktop-md-min-width) {
                display: none;
            }
        }
    }

    /* mobile toggle (plus, minus) */
    &__toggle {
        flex: 0 0 auto;
        width: 24px;
        height: 24px;

        > svg:nth-child(2) {
            display: none;
        }

        @media screen and (min-width: $nav-desktop-md-min-width) {
            @apply hidden;
        }
    }

    &__children-arrow {
        @media screen and (min-width: $nav-desktop-md-min-width) {
            @apply hidden;
        }
    }

    /* mobile toggle active (plus, minus) */
    &--active {
        .header-menu-item__toggle {
            > svg:first-child {
                display: none;
            }

            > svg:nth-child(2) {
                display: block;
            }
        }
    }

    /* mobile back arrow */
    &__back-arrow {
        width: 24px;
        height: 24px;

        > svg {
            @apply text-charcoal;
            transform: rotate(-180deg);
            width: 24px;
            height: 24px;
        }
    }

    /* Mobile back link */
    &--back {
        @apply text-mid-grey-1 bg-bg-grey;

        @media screen and (min-width: $nav-desktop-md-min-width) {
            @apply hidden;
        }
    }

    /* Main Nav */
    &--level-0 {
        @media screen and (min-width: $nav-desktop-md-min-width) {
            border-radius: 7px;
            text-transform: none;
            margin-top: 7px; /* because of the line below (2 size of line + spacing 5) */
        }

        &.header-menu-item::after {
            @media screen and (min-width: $nav-desktop-md-min-width) {
                content: '';
                height: 2px;
                border-radius: 10px;
                background-color: transparent;
                display: block;
                position: absolute;
                top: auto;
                bottom: 20px;
                width: 0;
                left: 3px;
                transition: width 0.4s cubic-bezier(0.5, 0.15, 0.33, 1);
            }
        }

        &:hover {
            @media screen and (min-width: $nav-desktop-md-min-width) {
                @apply text-content-blue;
                text-decoration: none;
                transition: color 0.3s ease;
            }

            &::after {
                @media screen and (min-width: $nav-desktop-md-min-width) {
                    background-color: var(--content-blue);
                    width: calc(100% - 6px);
                }
            }
        }

        span,
        a {
            @media screen and (min-width: $nav-desktop-md-min-width) {
                padding: 13px 5px;
                display: flex;
                align-items: center;
                height: 68px;
            }
        }

        a {
            border: transparent 3px solid;

            &:focus-visible,
            &:focus {
                border: var(--content-blue) 3px solid;
                outline: none;
                border-radius: 7px;
            }
        }

        .header-menu-item__title {
            @media screen and (min-width: $nav-desktop-md-min-width) {
                @apply font-univers65Bold;
                @apply text-small-copy;
                @apply leading-extra-tight-md-max;
                text-transform: none;
            }
        }
    }

    /* Submenu */
    &--level-1 {
        @media screen and (min-width: $nav-desktop-md-min-width) {
            width: 100%;
            height: 100%;
            border-radius: 4px;
            align-items: flex-start;
            position: relative;
            padding: 5px 6px;
        }

        @media screen and (min-width: $nav-desktop-lg-min-width) {
            padding: 10px 8px;
        }

        .header-menu-item__title {
            @media screen and (min-width: $nav-desktop-md-min-width) {
                @apply font-univers67BoldCondensed text-h6 text-white uppercase;
                line-height: 1em;
                vertical-align: text-top;
                display: block;
                position: absolute;
                top: 10%;
                left: 10%;
                max-width: 90px;
            }

            @media screen and (min-width: $nav-desktop-lg-min-width) {
                @apply text-h5;
                top: 15%;
                left: 10%;
                max-width: 180px;
            }
        }

        @media (max-width: $nav-mobile-max-width) {
            &:not(.header-menu-item--back) {
                .header-menu-item__title {
                    @apply text-body-copy-md-max;
                }
            }
        }
        // DO NOT REMOVE
        //.header-menu-item__label {
        //    @media screen and (min-width: $nav-desktop-md-min-width) {
        //        display: none;
        //    }
        //}

        /* blue border bottom */
        &::after {
            @media screen and (min-width: $nav-desktop-md-min-width) {
                content: '';
                width: 0;
                height: 2px;
                background-color: var(--content-blue);
                position: absolute;
                bottom: -3px;
                left: 0;
                opacity: 0;
                border-radius: 10px;
                @apply transition-opacity duration-300;
                transition: width 0.4s cubic-bezier(0.5, 0.15, 0.33, 1);
            }
        }
    }

    &--active {
        .header-menu-item--level-1 {
            &::after {
                @media screen and (min-width: $nav-desktop-md-min-width) {
                    opacity: 1;
                    width: 100%;
                }
            }
        }
    }

    &.red {
        color: var(--primitive-red-80);
    }
}

.header-menu-item--active {
    .header-menu-item--level-1 {
        &::after {
            background-color: var(--content-blue);
            opacity: 1;
            width: 100%;
        }
    }
}
