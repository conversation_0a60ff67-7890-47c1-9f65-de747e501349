import { FC, useState, useEffect, useMemo } from 'react'
import { useTranslation } from 'next-i18next'
import { useCountries } from '@corsairitshopify/pylot-utils'
import {
    useWatch,
    FieldErrors,
    UseFormRegister,
    UseFormUnregister,
    Control
} from 'react-hook-form'
import { Region, CustomerAddressRegion } from '@pylot-data/fwrdschema'
import { CountryCodeEnum } from '@pylot-data/enums/CountryCodeEnum'
import cn from 'classnames'
import { AddressInputVariableInterface } from '@pylot-data/hooks/melissa/use-address-validation'
import { Dropdown } from '@components/molecules/Dropdown/Dropdown'

type Props = {
    defaultValue: CustomerAddressRegion
    register: UseFormRegister<AddressInputVariableInterface>
    errors: FieldErrors<AddressInputVariableInterface>
    countryCode: CountryCodeEnum
    control: Control<AddressInputVariableInterface>
    unregister: UseFormUnregister<AddressInputVariableInterface>
    fieldIdPrefix?: string
}

export const RegionField: FC<Props> = ({
    control,
    defaultValue,
    register,
    errors,
    unregister,
    countryCode = 'US' as CountryCodeEnum, //replace with storeConfig defaultCountry value
    fieldIdPrefix = ''
}) => {
    const { t } = useTranslation(['account'])
    const [regions, setRegions] = useState<Region[] | null>([])
    const { countries, getCountryByCode, isLoading } = useCountries({
        revalidateOnFocus: false
    })
    const country = useWatch({
        control,
        name: 'country_code',
        defaultValue: countryCode
    })

    useEffect(() => {
        if (countries && !isLoading) {
            const [countryBycode] = getCountryByCode(country)
            const region = countryBycode?.available_regions as Region[] | null
            setRegions(region)
            unregister('region') //unregister to reset the values when field changes
        }
    }, [countries, country, isLoading, getCountryByCode, unregister])

    const mappedRegions:
        | { label: string; value: string }[]
        | undefined = useMemo(
        () =>
            regions?.map((region) => ({
                label: region.name?.toString() ?? '',
                value: region.name?.toString() ?? ''
            })),
        [regions]
    )

    //register new filed with same name
    useEffect(() => {
        register('region')
    }, [register])

    if (mappedRegions?.length && !isLoading) {
        return (
            <div className={cn('state', 'form-group')}>
                <Dropdown
                    asOverlay
                    fullWidth
                    variant="dropdown-outlined"
                    register={register('region', {
                        required: true
                    })}
                    options={[{ title: '', options: mappedRegions }]}
                    title={t('account||addressform|State/Province')}
                    titleAsPlaceholder
                    currentOption={mappedRegions.find(
                        (el) => el.value === defaultValue?.region?.toString()
                    )}
                />
                {/* helper for the region name on address display */}
                <div className="hidden">
                    {regions?.map((region, key) => (
                        <option
                            key={key}
                            value={region?.name!}
                            data-region-code={region?.code}
                        >
                            {region?.name}
                        </option>
                    ))}
                </div>

                {errors.region && (
                    <p className="text-radical-red-1 text-xs-copy em mt-8px">
                        {t(`account||account|This field is required`)}
                    </p>
                )}
            </div>
        )
    }

    return (
        <div className={cn('state', 'form-group')}>
            <label className="heading" htmlFor={`${fieldIdPrefix}region`}>
                <input
                    type="text"
                    placeholder={t('account||addressform|State/Province')}
                    defaultValue={defaultValue?.region!}
                    {...register('region', { required: false })}
                    className="form-input"
                    id={`${fieldIdPrefix}region`}
                />
            </label>
        </div>
    )
}
