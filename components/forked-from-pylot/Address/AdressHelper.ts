import {
    AddressInputVariableInterface,
    MelissaData
} from '@pylot-data/hooks/melissa/use-address-validation'

export type AddressInputVariableInterfaceWithRegion = AddressInputVariableInterface & {
    regionName?: string
}

export const setUserAddressInput = (
    {
        city,
        company,
        country_code,
        firstname,
        lastname,
        postcode,
        region,
        street,
        telephone,
        default_billing,
        default_shipping
    }: AddressInputVariableInterface,
    _melissaData: MelissaData,
    setUserSelectedAddressInput: (
        address: AddressInputVariableInterfaceWithRegion
    ) => void
): void => {
    const selectedRegion = document.querySelector(
        `option[value="${region}"]`
    ) as HTMLOptionElement

    const regionCode = selectedRegion?.dataset?.regionCode

    const userAddressInput = {} as AddressInputVariableInterfaceWithRegion

    userAddressInput.city = city
    userAddressInput.company = company
    userAddressInput.country_code = country_code
    userAddressInput.firstname = firstname
    userAddressInput.lastname = lastname
    userAddressInput.postcode = postcode
    userAddressInput.region = regionCode || _melissaData.AdministrativeArea
    userAddressInput.regionName = region || _melissaData.AdministrativeArea
    userAddressInput.street = street
    userAddressInput.telephone = telephone
    userAddressInput.default_billing = default_billing
    userAddressInput.default_shipping = default_shipping

    setUserSelectedAddressInput(userAddressInput)
}
