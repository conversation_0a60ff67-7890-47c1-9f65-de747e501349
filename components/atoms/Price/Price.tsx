import React, { FC } from 'react'
import s from './Price.module.scss'
import cn from 'classnames'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'

export type PriceProps = {
    price: string
    variant?: 'default' | 'discount' | 'neo' | 'cod-dark'
    size?: 'huge' | 'big' | 'medium' | 'small' | 'xs'
    theme?:
        | 'dark'
        | 'light'
        | 'blue'
        | 'red'
        | 'neo'
        | 'neo-blue'
        | 'neo-blue-discount'
    themeDiscount?: 'dark' | 'light' | 'blue' | 'red' | 'neo' | 'neo-blue'
    className?: string
    customDiscountStyle?: string
    promoType?: string
}
export const Price: FC<PriceProps> = (props) => {
    const {
        price,
        variant,
        size = 'huge',
        theme,
        themeDiscount,
        className,
        customDiscountStyle,
        promoType
    } = props
    return (
        <div
            className={cn(
                s['price'],
                {
                    [s[`price--${variant}`]]: variant,
                    [s[`price--size-${size}`]]: size,
                    [s[`price--${theme}`]]: variant !== 'discount',
                    [s[`price--${variant}-themeDiscount`]]: theme === 'neo',
                    [s[`price--hgg-themeDiscount`]]:
                        themeDiscount === 'neo' && promoType === 'hgg',
                    [s[`price--${customDiscountStyle}`]]: customDiscountStyle
                },
                className
            )}
        >
            {price}
        </div>
    )
}
