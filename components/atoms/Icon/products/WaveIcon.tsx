const WaveIcon = ({ ...props }) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10 7V15V16V20L10 20.0658C9.99995 20.9523 9.99991 21.7161 10.0821 22.3278C10.1703 22.9833 10.369 23.6117 10.8787 24.1213C11.3883 24.631 12.0167 24.8297 12.6722 24.9179C13.2839 25.0001 14.0477 25.0001 14.9342 25L15 25H17L17.0658 25C17.9523 25.0001 18.7161 25.0001 19.3278 24.9179C19.9833 24.8297 20.6117 24.631 21.1213 24.1213C21.631 23.6117 21.8297 22.9833 21.9179 22.3278C22.0001 21.7161 22.0001 20.9523 22 20.0658L22 20V16V15V7L22 6.93417C22.0001 6.04768 22.0001 5.28387 21.9179 4.67221C21.8297 4.01669 21.631 3.38834 21.1213 2.87868C20.6117 2.36902 19.9833 2.17027 19.3278 2.08214C18.7161 1.9999 17.9523 1.99995 17.0658 2L17 2H15L14.9342 2C14.0477 1.99995 13.2839 1.9999 12.6722 2.08214C12.0167 2.17027 11.3883 2.36902 10.8787 2.87868C10.369 3.38834 10.1703 4.01669 10.0821 4.67221C9.99991 5.28387 9.99995 6.04768 10 6.93417L10 7ZM21 15V14V7C21 5.11438 21 4.17157 20.4142 3.58579C19.8284 3 18.8856 3 17 3H15C13.1144 3 12.1716 3 11.5858 3.58579C11 4.17157 11 5.11438 11 7V14V15H12H16H20H21ZM11 16V17V20C11 21.8856 11 22.8284 11.5858 23.4142C12.1716 24 13.1144 24 15 24H17C18.8856 24 19.8284 24 20.4142 23.4142C21 22.8284 21 21.8856 21 20V17V16H20H16H12H11ZM18 20C18 21.1046 17.1046 22 16 22C14.8954 22 14 21.1046 14 20C14 18.8954 14.8954 18 16 18C17.1046 18 18 18.8954 18 20ZM16 21C16.5523 21 17 20.5523 17 20C17 19.4477 16.5523 19 16 19C15.4477 19 15 19.4477 15 20C15 20.5523 15.4477 21 16 21ZM9 15.5C9.27614 15.5 9.5 15.7239 9.5 16V19C9.5 20.4149 9.5006 21.4384 9.5817 22.2355C9.66194 23.0244 9.81684 23.5359 10.0899 23.9445C10.3452 24.3267 10.6733 24.6548 11.0555 24.9101C11.4641 25.1832 11.9756 25.3381 12.7645 25.4183C13.5616 25.4994 14.5851 25.5 16 25.5C17.4149 25.5 18.4384 25.4994 19.2355 25.4183C20.0244 25.3381 20.5359 25.1832 20.9445 24.9101C21.3267 24.6548 21.6548 24.3267 21.9101 23.9445C22.1832 23.5359 22.3381 23.0244 22.4183 22.2355C22.4994 21.4384 22.5 20.4149 22.5 19V16C22.5 15.7239 22.7239 15.5 23 15.5C23.2761 15.5 23.5 15.7239 23.5 16V19V19.0285V19.0286V19.0286V19.0286V19.0287C23.5 20.4089 23.5 21.4831 23.4132 22.3368C23.3249 23.2046 23.1427 23.8998 22.7416 24.5001C22.4133 24.9914 21.9914 25.4133 21.5001 25.7416C20.8998 26.1427 20.2046 26.3249 19.3368 26.4132C18.4831 26.5 17.4089 26.5 16.0287 26.5H16.0286H16.0286H16.0286H16.0285H16H15.9715H15.9714H15.9714H15.9714H15.9713C14.5911 26.5 13.5169 26.5 12.6632 26.4132C11.7954 26.3249 11.1002 26.1427 10.4999 25.7416C10.0086 25.4133 9.5867 24.9914 9.25839 24.5001C8.85728 23.8998 8.67512 23.2046 8.58683 22.3368C8.49999 21.4831 8.5 20.4088 8.5 19.0285V19V16C8.5 15.7239 8.72386 15.5 9 15.5ZM16.5 27.5C16.5 27.2239 16.2761 27 16 27C15.7239 27 15.5 27.2239 15.5 27.5V29H10C9.72386 29 9.5 29.2239 9.5 29.5C9.5 29.7762 9.72386 30 10 30H16H22C22.2761 30 22.5 29.7762 22.5 29.5C22.5 29.2239 22.2761 29 22 29H16.5V27.5ZM7 15.5C7.27614 15.5 7.5 15.7239 7.5 16V18C7.5 18.2761 7.27614 18.5 7 18.5C6.72386 18.5 6.5 18.2761 6.5 18V16C6.5 15.7239 6.72386 15.5 7 15.5ZM25.5 16C25.5 15.7239 25.2761 15.5 25 15.5C24.7239 15.5 24.5 15.7239 24.5 16V18C24.5 18.2761 24.7239 18.5 25 18.5C25.2761 18.5 25.5 18.2761 25.5 18V16Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default WaveIcon
