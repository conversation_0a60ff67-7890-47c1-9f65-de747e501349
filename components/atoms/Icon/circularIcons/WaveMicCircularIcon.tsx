const WaveMicCircularIcon = ({ ...props }) => {
    return (
        <svg
            width="58"
            height="58"
            viewBox="0 0 58 58"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M25 21C25 19.8954 25.8954 19 27 19H31C32.1046 19 33 19.8954 33 21V33C33 34.1046 32.1046 35 31 35H29.75V37.5H33.25C33.6642 37.5 34 37.8358 34 38.25C34 38.6642 33.6642 39 33.25 39L24.75 39C24.3358 39 24 38.6642 24 38.25C24 37.8358 24.3358 37.5 24.75 37.5H28.25V35H27C25.8954 35 25 34.1046 25 33V21ZM27 20.5H31C31.2761 20.5 31.5 20.7239 31.5 21V28L26.5 28V21C26.5 20.7239 26.7239 20.5 27 20.5ZM26.5 29.5V33C26.5 33.2761 26.7239 33.5 27 33.5H31C31.2761 33.5 31.5 33.2761 31.5 33V29.5L26.5 29.5Z"
                fill="currentColor"
            />
            <path
                d="M22.75 27C23.1642 27 23.5 27.3358 23.5 27.75L23.5 30.25C23.5 30.6642 23.1642 31 22.75 31C22.3358 31 22 30.6642 22 30.25V27.75C22 27.3358 22.3358 27 22.75 27Z"
                fill="currentColor"
            />
            <path
                d="M36 27.75C36 27.3358 35.6642 27 35.25 27C34.8358 27 34.5 27.3358 34.5 27.75V30.25C34.5 30.6642 34.8358 31 35.25 31C35.6642 31 36 30.6642 36 30.25V27.75Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default WaveMicCircularIcon
