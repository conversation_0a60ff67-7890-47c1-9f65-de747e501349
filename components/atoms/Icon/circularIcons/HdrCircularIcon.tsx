const HdrCircularIcon = ({ ...props }) => {
    return (
        <svg
            width="58"
            height="58"
            viewBox="0 0 58 58"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M24.5873 33H22.8632V29.6001H19.4203V33H17.6908V25.0454H19.4203V28.3057H22.8632V25.0454H24.5873V33Z"
                fill="currentColor"
            />
            <path
                d="M33.2646 28.9824C33.2646 29.4372 33.2252 29.8507 33.1464 30.2231C33.0677 30.592 32.9567 30.9142 32.8134 31.1899C32.6738 31.4657 32.4965 31.7091 32.2817 31.9204C32.0669 32.1281 31.8305 32.3 31.5727 32.436C31.3185 32.5721 31.0284 32.6831 30.7026 32.769C30.3803 32.8514 30.0473 32.9105 29.7036 32.9463C29.3634 32.9821 28.9892 33 28.581 33H26.0835V25.0454H28.8442C29.3741 25.0454 29.8558 25.0866 30.289 25.1689C30.7223 25.2477 31.1269 25.3838 31.5029 25.5771C31.8789 25.7669 32.194 26.0104 32.4482 26.3076C32.7024 26.6048 32.9012 26.979 33.0444 27.4302C33.1912 27.8813 33.2646 28.3988 33.2646 28.9824ZM31.4975 29.0093C31.4975 28.0353 31.2683 27.3353 30.81 26.9092C30.3517 26.4831 29.6445 26.27 28.6884 26.27H27.8129V31.7808H28.624C29.6087 31.7808 30.3338 31.5588 30.7993 31.1147C31.2648 30.6707 31.4975 29.9689 31.4975 29.0093Z"
                fill="currentColor"
            />
            <path
                d="M40.771 33H38.9663C38.8804 32.7458 38.8106 32.4092 38.7569 31.9902C38.739 31.8506 38.7193 31.6519 38.6978 31.394C38.6763 31.1326 38.6638 30.9858 38.6602 30.9536C38.6351 30.6886 38.6011 30.481 38.5581 30.3306C38.5152 30.1802 38.4453 30.0495 38.3487 29.9385C38.2556 29.8239 38.1285 29.7469 37.9673 29.7075C37.8098 29.6646 37.6003 29.6431 37.3389 29.6431H36.0498V33H34.3365V25.0454H37.3819C37.6576 25.0454 37.8796 25.049 38.0479 25.0562C38.2162 25.0633 38.4167 25.0884 38.6494 25.1313C38.8822 25.1707 39.0952 25.2298 39.2886 25.3086C40.1372 25.6667 40.5616 26.2503 40.5616 27.0596C40.5616 27.6003 40.4022 28.0282 40.0835 28.3433C39.7648 28.6584 39.3262 28.8714 38.7676 28.9824V28.9985C39.2224 29.0272 39.5625 29.1328 39.7881 29.3154C40.0137 29.4945 40.173 29.7917 40.2661 30.207C40.2805 30.2751 40.2948 30.3521 40.3091 30.438C40.3234 30.5203 40.336 30.6188 40.3467 30.7334C40.361 30.8444 40.3736 30.9321 40.3843 30.9966C40.3951 31.0825 40.413 31.2472 40.438 31.4907C40.4631 31.7306 40.4846 31.9132 40.5025 32.0386C40.5204 32.1603 40.5526 32.3161 40.5992 32.5059C40.6457 32.6921 40.703 32.8568 40.771 33ZM38.8804 27.3442C38.8804 27.158 38.8428 26.9969 38.7676 26.8608C38.696 26.7248 38.6047 26.6191 38.4937 26.5439C38.3863 26.4688 38.2502 26.4097 38.0855 26.3667C37.9244 26.3201 37.7722 26.2915 37.6289 26.2808C37.4857 26.2664 37.3264 26.2593 37.1509 26.2593H36.0498V28.4775H37.0327C37.1975 28.4775 37.3389 28.474 37.4571 28.4668C37.5788 28.4596 37.7185 28.4453 37.876 28.4238C38.0336 28.3988 38.166 28.3612 38.2735 28.311C38.3809 28.2609 38.4829 28.1965 38.5796 28.1177C38.6799 28.0353 38.7551 27.9297 38.8052 27.8008C38.8553 27.6683 38.8804 27.5161 38.8804 27.3442Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default HdrCircularIcon
