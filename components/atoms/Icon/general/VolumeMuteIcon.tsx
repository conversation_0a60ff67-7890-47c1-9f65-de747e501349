import { FC, SVGProps } from 'react'

type Props = SVGProps<SVGSVGElement>

const VolumeMuteIcon: FC<Props> = (props) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M6.66668 21.3335L12.3905 27.0574C13.2305 27.8973 14.6667 27.3024 14.6667 26.1146V5.88581C14.6667 4.69794 13.2305 4.10305 12.3905 4.943L6.66668 10.6669H4.00001C2.52725 10.6669 1.33334 11.8608 1.33334 13.3335V18.6669C1.33334 20.1396 2.52725 21.3335 4.00001 21.3335H6.66668Z"
                fill="currentColor"
            />
            <path
                d="M18.9596 19.6266C18.569 20.0171 18.569 20.6502 18.9596 21.0408C19.3501 21.4313 19.9833 21.4313 20.3738 21.0408L24 17.4145L27.6262 21.0408C28.0168 21.4313 28.6499 21.4313 29.0405 21.0408C29.431 20.6502 29.431 20.0171 29.0405 19.6266L25.4142 16.0003L29.0405 12.3741C29.431 11.9836 29.431 11.3504 29.0405 10.9599C28.6499 10.5694 28.0168 10.5694 27.6262 10.9599L24 14.5861L20.3738 10.9599C19.9833 10.5694 19.3501 10.5694 18.9596 10.9599C18.569 11.3504 18.569 11.9836 18.9596 12.3741L22.5858 16.0003L18.9596 19.6266Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default VolumeMuteIcon
