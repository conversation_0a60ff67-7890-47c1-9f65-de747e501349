import { FC, SVGProps } from 'react'

export const CyberMondayHealthBar: FC<SVGProps<SVGSVGElement>> = ({
    ...props
}) => {
    return (
        <svg
            viewBox="0 0 572 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g filter="url(#filter0_d_216_73)">
                <path
                    d="M12.7 6.99C10.81 8.87 8.91 10.74 7.01 12.62C6.36 13.26 6 14.13 6 15.05V30.6C6 32.48 7.53 34.01 9.41 34.01H354.91C355.81 34.01 356.68 33.65 357.32 33.01L358.49 31.84C359.13 31.2 360 30.84 360.9 30.84H548.77C549.67 30.84 550.54 31.2 551.18 31.84L552.35 33.01C552.99 33.65 553.86 34.01 554.76 34.01H562.59C564.47 34.01 566 32.48 566 30.6V9.41C566 7.53 564.47 6 562.59 6H15.1C14.2 6 13.34 6.36 12.7 6.99Z"
                    fill="url(#paint0_linear_216_73)"
                />
                <path
                    d="M12.7 6.99C10.81 8.87 8.91 10.74 7.01 12.62C6.36 13.26 6 14.13 6 15.05V30.6C6 32.48 7.53 34.01 9.41 34.01H354.91C355.81 34.01 356.68 33.65 357.32 33.01L358.49 31.84C359.13 31.2 360 30.84 360.9 30.84H548.77C549.67 30.84 550.54 31.2 551.18 31.84L552.35 33.01C552.99 33.65 553.86 34.01 554.76 34.01H562.59C564.47 34.01 566 32.48 566 30.6V9.41C566 7.53 564.47 6 562.59 6H15.1C14.2 6 13.34 6.36 12.7 6.99Z"
                    stroke="#02F5F5"
                    strokeMiterlimit="10"
                />
            </g>
            <path
                d="M357.81 32.92H551.83L549.26 31.28H359.81L357.81 32.92Z"
                fill="#02F5F5"
            />
            <defs>
                <filter
                    id="filter0_d_216_73"
                    x="0.5"
                    y="0.5"
                    width="571"
                    height="39.01"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                >
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix
                        in="SourceAlpha"
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                        result="hardAlpha"
                    />
                    <feOffset />
                    <feGaussianBlur stdDeviation="2.5" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix
                        type="matrix"
                        values="0 0 0 0 0.00784314 0 0 0 0 0.960784 0 0 0 0 0.960784 0 0 0 1 0"
                    />
                    <feBlend
                        mode="normal"
                        in2="BackgroundImageFix"
                        result="effect1_dropShadow_216_73"
                    />
                    <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_dropShadow_216_73"
                        result="shape"
                    />
                </filter>
                <linearGradient
                    id="paint0_linear_216_73"
                    x1="6"
                    y1="20.005"
                    x2="566"
                    y2="20.005"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#203B3D" />
                    <stop offset="1" stopColor="#030303" />
                </linearGradient>
            </defs>
        </svg>
    )
}
