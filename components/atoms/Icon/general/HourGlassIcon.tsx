import { FC, SVGProps } from 'react'

export const HourGlassIcon: FC<SVGProps<SVGSVGElement>> = (props) => {
    return (
        <svg
            width="20"
            height="30"
            viewBox="0 0 20 30"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M20 -0.00244141H0V10.6256L6.02085 14.9988L0 19.3721V30.0001H20V19.3721L13.9791 14.9988L20 10.6256V-0.00244141ZM18.4744 9.84755L11.3857 14.9988L18.4744 20.1501V28.4745H1.52555V20.1501L8.61429 14.9988L1.52555 9.84755V1.52311H18.4744V9.84755Z"
                fill="currentColor"
            />
            <path
                d="M3.05103 27.071V27.1321H16.9488V27.071L10.0025 22.0215L3.05103 27.071Z"
                fill="currentColor"
            />
            <path
                d="M16.9488 9.06951V6.83203H3.05103V9.06951L10.0025 14.1191L16.9488 9.06951Z"
                fill="currentColor"
            />
        </svg>
    )
}
