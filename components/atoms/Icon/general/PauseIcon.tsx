import { FC, SVGProps } from 'react'

type Props = SVGProps<SVGSVGElement>

const PauseIcon: FC<Props> = (props) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M8.00001 4C6.52725 4 5.33334 5.19391 5.33334 6.66667V25.3333C5.33334 26.8061 6.52725 28 8.00001 28H10.6667C12.1394 28 13.3333 26.8061 13.3333 25.3333V6.66667C13.3333 5.19391 12.1394 4 10.6667 4H8.00001Z"
                fill="currentColor"
            />
            <path
                d="M21.3333 4C19.8606 4 18.6667 5.19391 18.6667 6.66667V25.3333C18.6667 26.8061 19.8606 28 21.3333 28H24C25.4728 28 26.6667 26.8061 26.6667 25.3333V6.66667C26.6667 5.19391 25.4728 4 24 4H21.3333Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default PauseIcon
