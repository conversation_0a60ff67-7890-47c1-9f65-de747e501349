const Image360Graph = ({ ...props }) => {
    return (
        <svg
            width="140"
            height="33"
            viewBox="0 0 140 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M0 16.3889C0 21.9961 29.8349 28.58 66.2867 28.971C66.4764 28.9731 66.5644 28.7346 66.4165 28.6157L63.9253 26.6132C63.8257 26.5331 63.8257 26.3815 63.9253 26.3014L66.4282 24.2895C66.5748 24.1717 66.4893 23.935 66.3012 23.9335C29.8534 23.6366 1.04478 21.1087 1.04478 16.3889C1.04478 14.0394 7.31343 10.8369 18.806 9.16582C7.31343 10.8369 0 13.5972 0 16.3889Z"
                fill="white"
            />
            <path
                d="M140 16.3889C140 13.5148 132.433 10.9082 120.149 9C132.164 11.3408 138.955 13.9607 138.955 16.3796C138.955 20.887 113.624 23.4038 79.6073 23.8798C79.4958 23.8813 79.403 23.7914 79.403 23.6798V20.2974C79.403 20.1358 79.2214 20.0409 79.0888 20.1332L70.2359 26.2931C70.1216 26.3727 70.1216 26.5419 70.2359 26.6215L79.0888 32.7814C79.2214 32.8736 79.403 32.7787 79.403 32.6172V29.0821C79.403 28.9736 79.4908 28.8849 79.5992 28.8821C113.717 27.9931 140 21.7454 140 16.3889Z"
                fill="white"
            />
            <path
                d="M23.76 11.44H25.88C25.86 12.76 26.72 13.7 28.42 13.7C30.36 13.7 31.18 12.46 31.18 11.24C31.18 9.78 30.2 9.02 29.18 8.8C28.46 8.66 27.96 8.68 27.26 8.7V7.08C28.64 7.2 30.96 6.92 30.96 4.68C30.96 3.12 29.9 2.5 28.52 2.5C27.48 2.5 26.18 2.96 26 4.68H23.98C24.1 1.24 27.34 0.899999 28.46 0.899999C31.48 0.899999 32.98 2.72 32.98 4.64C32.98 5.78 32.36 7.42 30.22 7.9C33.18 8.32 33.18 10.98 33.18 11.26C33.18 13.2 31.6 15.34 28.38 15.34C26.92 15.34 25.46 14.92 24.58 13.9C23.8 13.02 23.78 12.02 23.76 11.44ZM44.5633 4.44H42.5033C42.4833 2.82 41.2833 2.38 40.4033 2.38C38.4033 2.38 37.5233 4.1 37.3433 7.78C37.7833 7.14 38.5433 6.02 40.5633 6.02C42.8233 6.02 44.7433 7.52 44.7433 10.46C44.7433 13.68 42.4433 15.3 40.0633 15.3C38.7233 15.3 37.3833 14.76 36.6433 13.8C35.6233 12.44 35.3833 10.46 35.3833 8.7C35.3833 5.68 36.1233 0.879999 40.5033 0.879999C42.3233 0.879999 43.3433 1.68 43.8233 2.2C44.5233 3.02 44.5433 3.9 44.5633 4.44ZM40.1433 13.78C41.3833 13.78 42.8233 12.9 42.8233 10.64C42.8233 9.66 42.5433 7.56 40.2433 7.56C38.3833 7.56 37.4833 9.08 37.4833 10.66C37.4833 12.46 38.6033 13.78 40.1433 13.78ZM46.6066 8.24C46.6066 6.18 46.8466 0.879999 51.2466 0.879999C55.3866 0.879999 55.8866 5.32 55.8866 7.92C55.8866 11.2 55.2266 13.04 53.9666 14.22C52.9866 15.14 51.8266 15.28 51.2066 15.28C49.3666 15.28 48.1066 14.2 47.5066 12.98C47.0466 12.06 46.6066 10.72 46.6066 8.24ZM48.5266 8.14C48.5266 11.22 49.1266 13.62 51.2266 13.62C53.5466 13.62 53.9466 10.58 53.9466 8.1C53.9466 4.78 53.3666 2.48 51.2666 2.48C48.8466 2.48 48.5266 5.66 48.5266 8.14ZM61.0898 0.879999C62.8098 0.879999 64.2098 2.26 64.2098 4C64.2098 5.7 62.8298 7.12 61.0898 7.12C59.3698 7.12 57.9698 5.72 57.9698 4C57.9698 2.26 59.3698 0.879999 61.0898 0.879999ZM61.0898 2.26C60.1498 2.26 59.3498 3.02 59.3498 4C59.3498 4.96 60.1298 5.74 61.0898 5.74C62.0698 5.74 62.8298 4.94 62.8298 4C62.8298 3.04 62.0498 2.26 61.0898 2.26ZM82.1119 0.559999L76.7919 15H74.5919L69.4519 0.559999H71.6719L75.7719 12.9L80.0919 0.559999H82.1119ZM84.3417 2.66V0.599999H86.6017V2.66H84.3417ZM84.5417 15V4.96H86.4217V15H84.5417ZM98.6981 10.32H91.4781C91.4381 12.4 92.2981 13.94 94.3581 13.94C95.5781 13.94 96.5581 13.3 96.7181 11.86H98.5981C98.5381 12.36 98.4581 13.18 97.7381 13.96C97.2981 14.46 96.2981 15.3 94.2781 15.3C91.1181 15.3 89.6181 13.34 89.6181 10.16C89.6181 8.18 90.0181 6.54 91.5981 5.44C92.5581 4.76 93.7581 4.7 94.3381 4.7C98.8381 4.7 98.7381 8.68 98.6981 10.32ZM91.5581 9H96.7781C96.7981 8.04 96.6181 6.04 94.2981 6.04C93.0781 6.04 91.5981 6.78 91.5581 9ZM100.181 4.96H102.241L104.661 12.96L107.201 4.96H109.661L111.941 12.88L114.461 4.96H116.401L112.861 15H110.701L108.341 6.74L105.721 15H103.501L100.181 4.96Z"
                fill="white"
            />
        </svg>
    )
}
export default Image360Graph
