import { FC, SVGProps } from 'react'

type Props = SVGProps<SVGSVGElement>

const PlayIcon: FC = (props) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                id="Path"
                d="M26.6667 13.6906C28.4444 14.717 28.4444 17.283 26.6667 18.3094L10.6667 27.5471C8.88889 28.5735 6.66667 27.2905 6.66667 25.2377L6.66667 6.76244C6.66667 4.70964 8.88889 3.42664 10.6667 4.45304L26.6667 13.6906Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default PlayIcon
