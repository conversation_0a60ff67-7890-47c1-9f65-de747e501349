const ShareIcon = ({ ...props }) => {
    return (
        <svg
            width="17"
            height="18"
            viewBox="0 0 17 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17 3C17 4.65685 15.6569 6 14 6C13.0582 6 12.2177 5.56601 11.6677 4.8871L5.84371 8.04178C5.94507 8.34268 6 8.66492 6 9C6 9.33508 5.94507 9.65732 5.84371 9.95822L11.6677 13.1129C12.2177 12.434 13.0582 12 14 12C15.6569 12 17 13.3431 17 15C17 16.6569 15.6569 18 14 18C12.3431 18 11 16.6569 11 15C11 14.8231 11.0153 14.6498 11.0447 14.4813L5.02006 11.218C4.48696 11.7038 3.77807 12 3 12C1.34315 12 0 10.6569 0 9C0 7.34315 1.34315 6 3 6C3.77807 6 4.48696 6.29621 5.02006 6.78201L11.0447 3.51867C11.0153 3.35019 11 3.17688 11 3C11 1.34315 12.3431 0 14 0C15.6569 0 17 1.34315 17 3ZM15.5 3C15.5 3.82843 14.8284 4.5 14 4.5C13.1716 4.5 12.5 3.82843 12.5 3C12.5 2.17157 13.1716 1.5 14 1.5C14.8284 1.5 15.5 2.17157 15.5 3ZM15.5 15C15.5 15.8284 14.8284 16.5 14 16.5C13.1716 16.5 12.5 15.8284 12.5 15C12.5 14.1716 13.1716 13.5 14 13.5C14.8284 13.5 15.5 14.1716 15.5 15ZM4.5 9C4.5 9.82843 3.82843 10.5 3 10.5C2.17157 10.5 1.5 9.82843 1.5 9C1.5 8.17157 2.17157 7.5 3 7.5C3.82843 7.5 4.5 8.17157 4.5 9Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ShareIcon
