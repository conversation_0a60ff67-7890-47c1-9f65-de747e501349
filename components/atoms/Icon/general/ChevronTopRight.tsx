import { useTranslation } from 'next-i18next'
import React from 'react'

const ChevronTopRight = ({ ...props }) => {
    const { t } = useTranslation(['common'])
    return (
        <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.8709 13.6792C16.8709 14.0934 17.2067 14.4292 17.6209 14.4292C18.0351 14.4292 18.3709 14.0934 18.3709 13.6792L18.3709 6.25454C18.3709 5.84033 18.0351 5.50454 17.6209 5.50454L10.1963 5.50454C9.78204 5.50454 9.44626 5.84033 9.44626 6.25454C9.44626 6.66875 9.78204 7.00454 10.1963 7.00454L15.8102 7.00454L5.60006 17.2147C5.30717 17.5076 5.30717 17.9825 5.60006 18.2754C5.89296 18.5682 6.36783 18.5682 6.66072 18.2754L16.8709 8.0652L16.8709 13.6792Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ChevronTopRight
