const mousScroll = ({ ...props }) => {
    return (
        <svg
            width="25"
            height="24"
            viewBox="0 0 25 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M16.25 15.75V8.25C16.25 6.17893 14.5711 4.5 12.5 4.5C10.4289 4.5 8.75 6.17893 8.75 8.25V15.75C8.75 17.8211 10.4289 19.5 12.5 19.5C14.5711 19.5 16.25 17.8211 16.25 15.75ZM12.5 3C9.60051 3 7.25 5.35051 7.25 8.25V15.75C7.25 18.6495 9.60051 21 12.5 21C15.3995 21 17.75 18.6495 17.75 15.75V8.25C17.75 5.35051 15.3995 3 12.5 3Z"
                fill="black"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12.5 6.75C12.9142 6.75 13.25 7.08579 13.25 7.5V10.5C13.25 10.9142 12.9142 11.25 12.5 11.25C12.0858 11.25 11.75 10.9142 11.75 10.5V7.5C11.75 7.08579 12.0858 6.75 12.5 6.75Z"
                fill="black"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M19.5841 9.12604C19.9287 8.89628 20.3944 8.98941 20.6241 9.33405L22.4015 12.0001L20.6241 14.6661C20.3944 15.0108 19.9287 15.1039 19.5841 14.8741C19.2394 14.6444 19.1463 14.1787 19.376 13.8341L20.5987 12.0001L19.376 10.1661C19.1463 9.82146 19.2394 9.35581 19.5841 9.12604Z"
                fill="black"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M5.41507 9.12604C5.75972 9.35581 5.85285 9.82146 5.62308 10.1661L4.40043 12.0001L5.62308 13.8341C5.85285 14.1787 5.75972 14.6444 5.41507 14.8741C5.07042 15.1039 4.60477 15.0108 4.37501 14.6661L2.59766 12.0001L4.37501 9.33405C4.60477 8.98941 5.07042 8.89628 5.41507 9.12604Z"
                fill="black"
            />
        </svg>
    )
}

export default mousScroll
