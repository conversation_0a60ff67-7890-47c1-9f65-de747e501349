import { useTranslation } from 'next-i18next'
const RedditIcon = ({ ...props }) => {
    const { t } = useTranslation(['common'])
    return (
        <svg
            width="34"
            height="34"
            viewBox="0 0 34 34"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M28.0541 12.798C29.7719 12.798 31.1661 14.1922 31.1661 15.91C31.1661 17.1797 30.3943 18.2752 29.3736 18.7731C29.4234 19.0719 29.4483 19.3706 29.4483 19.6943C29.4483 24.4744 23.8964 28.3333 17.0249 28.3333C10.1535 28.3333 4.60163 24.4744 4.60163 19.6943C4.60163 19.3706 4.62653 19.047 4.67632 18.7482C3.58088 18.2503 2.83398 17.1797 2.83398 15.91C2.83398 14.1922 4.22818 12.798 5.94604 12.798C6.76762 12.798 7.53941 13.1465 8.08713 13.6693C10.2282 12.1009 13.1909 11.1299 16.5021 11.0303L18.0706 3.61119C18.1204 3.46181 18.1951 3.33733 18.3196 3.26264C18.444 3.18795 18.5934 3.16306 18.7428 3.18795L23.8964 4.2834C24.2449 3.5365 24.9918 3.03857 25.8632 3.03857C27.0831 3.03857 28.079 4.03443 28.079 5.25436C28.079 6.47428 27.0831 7.47014 25.8632 7.47014C24.6681 7.47014 23.6972 6.52407 23.6474 5.35394L19.0416 4.38298L17.6225 11.0303C20.859 11.1548 23.7968 12.1507 25.913 13.6693C26.4607 13.1216 27.2076 12.798 28.0541 12.798Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default RedditIcon
