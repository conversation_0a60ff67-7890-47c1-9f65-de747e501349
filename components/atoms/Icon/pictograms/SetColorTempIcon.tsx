const SetColorTempIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <ellipse
                cx="40.4977"
                cy="34.2003"
                rx="5.4"
                ry="5.4"
                stroke="currentColor"
                strokeWidth="2"
            />
            <path
                d="M40.5 18.9004V24.7504"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M40.5 43.2002V49.0502"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M53.6641 27L48.5978 29.925"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M53.6641 41.625L48.5978 38.7"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <rect
                x="8.19922"
                y="8.2002"
                width="55.6"
                height="55.6"
                rx="9"
                stroke="currentColor"
                strokeWidth="2"
            />
            <path
                d="M22.4984 40.7591L23.0286 41.607L23.4984 41.3132V40.7591H22.4984ZM30.5984 40.7591H29.5984V41.3132L30.0683 41.607L30.5984 40.7591ZM23.4984 23.8503C23.4984 22.1658 24.864 20.8003 26.5484 20.8003V18.8003C23.7594 18.8003 21.4984 21.0613 21.4984 23.8503H23.4984ZM23.4984 40.7591V23.8503H21.4984V40.7591H23.4984ZM19.8984 47.2503C19.8984 44.8716 21.1467 42.7837 23.0286 41.607L21.9683 39.9112C19.5263 41.438 17.8984 44.1536 17.8984 47.2503H19.8984ZM26.5484 53.9003C22.8757 53.9003 19.8984 50.923 19.8984 47.2503H17.8984C17.8984 52.0276 21.7712 55.9003 26.5484 55.9003V53.9003ZM33.1984 47.2503C33.1984 50.923 30.2211 53.9003 26.5484 53.9003V55.9003C31.3257 55.9003 35.1984 52.0276 35.1984 47.2503H33.1984ZM30.0683 41.607C31.9502 42.7837 33.1984 44.8716 33.1984 47.2503H35.1984C35.1984 44.1536 33.5705 41.438 31.1286 39.9112L30.0683 41.607ZM29.5984 23.8503V40.7591H31.5984V23.8503H29.5984ZM26.5484 20.8003C28.2329 20.8003 29.5984 22.1658 29.5984 23.8503H31.5984C31.5984 21.0613 29.3375 18.8003 26.5484 18.8003V20.8003Z"
                fill="currentColor"
            />
            <path
                d="M22.5 31.5L30.6 31.5"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    )
}

export default SetColorTempIcon
