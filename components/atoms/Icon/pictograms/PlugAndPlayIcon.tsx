const PlugAndPlayIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M28 11C28 9.34315 29.3431 8 31 8H41C42.6569 8 44 9.34315 44 11V15C44 15.5523 43.5523 16 43 16C42.4477 16 42 15.5523 42 15V11C42 10.4477 41.5523 10 41 10H31C30.4477 10 30 10.4477 30 11V15C30 15.5523 29.5523 16 29 16C28.4477 16 28 15.5523 28 15V11Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M33.0728 38.25L41 33.788L40.9997 31.4609L37.1641 29.3068L37.1424 29.2884L33.0677 27L31 28.1755V37.0859L33.0728 38.25ZM33.0737 29.2972L35.997 30.939L36.0188 30.9573L38.9885 32.6252L33.0717 35.9556L33 35.9153V29.3391L33.0737 29.2972Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24 21C24 19.3431 25.3431 18 27 18H45C46.6569 18 48 19.3431 48 21V44C48 46.7614 45.7614 49 43 49H29C26.2386 49 24 46.7614 24 44V21ZM27 20C26.4477 20 26 20.4477 26 21V44C26 45.6569 27.3431 47 29 47H43C44.6569 47 46 45.6569 46 44V21C46 20.4477 45.5523 20 45 20H27Z"
                fill="currentColor"
            />
            <path
                d="M37 61C38.6569 61 40 59.6569 40 58V52C40 51.4477 39.5523 51 39 51C38.4477 51 38 51.4477 38 52L38 58C38 58.5523 37.5523 59 37 59H35C34.4477 59 34 58.5523 34 58V52C34 51.4477 33.5523 51 33 51C32.4477 51 32 51.4477 32 52L32 58C32 59.6569 33.3431 61 35 61V65C35 65.5523 35.4477 66 36 66C36.5523 66 37 65.5523 37 65V61Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default PlugAndPlayIcon
