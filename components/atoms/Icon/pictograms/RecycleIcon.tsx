const RecycleIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M18.0814 22.0358C17.253 23.4707 17.7446 25.3055 19.1795 26.1339L26.1077 30.1339C27.5426 30.9623 29.3773 30.4707 30.2058 29.0358L36 18.9999L37.7321 22C38.0082 22.4783 38.6198 22.6421 39.0981 22.366C39.5764 22.0899 39.7403 21.4783 39.4641 21L37.1743 17.034C38.814 14.5955 40.2574 13.2651 41.4528 12.5952C42.6943 11.8995 43.6918 11.9002 44.4742 12.1378C45.2954 12.3872 45.9745 12.9288 46.4683 13.4531C46.7107 13.7104 46.896 13.9504 47.0191 14.1236C47.0804 14.2098 47.1255 14.2784 47.1537 14.3228C47.1678 14.3449 47.1777 14.361 47.1832 14.3701L47.1871 14.3766L50.182 19.564L50.1865 19.5718C50.4627 20.0501 51.0742 20.2139 51.5525 19.9378L52.6506 19.3038L48.7431 26.0718C48.5645 26.3812 48.2344 26.5718 47.8771 26.5718L40.0621 26.5718L41.1602 25.9378C41.6385 25.6617 41.8024 25.0501 41.5263 24.5718C41.2501 24.0935 40.6385 23.9296 40.1602 24.2057L35.8301 26.7057C35.4381 26.932 35.247 27.3934 35.3642 27.8306C35.4813 28.2678 35.8775 28.5718 36.3301 28.5718L47.8771 28.5718C48.9489 28.5718 49.9393 28 50.4752 27.0718L56.2487 17.0718C56.475 16.6798 56.4098 16.1847 56.0898 15.8647C55.7697 15.5446 55.2746 15.4794 54.8827 15.7057L51.4186 17.7057L48.9186 13.3756L48.9179 13.3744C46.5424 9.26135 41.448 8.27413 36.9369 8.28936C32.3384 8.30488 27.8605 9.35953 26.1135 9.82141C25.3511 10.023 24.7263 10.5266 24.346 11.1852L18.0814 22.0358ZM36.9436 10.2893C38.3337 10.2847 39.6841 10.3809 40.9381 10.6107C40.785 10.6835 40.6307 10.7633 40.4751 10.8504C38.8065 11.7855 37.0325 13.5466 35.159 16.4589C35.1503 16.4724 35.142 16.486 35.134 16.4999L28.4737 28.0358C28.1976 28.5141 27.586 28.678 27.1077 28.4018L20.1795 24.4018C19.7012 24.1257 19.5373 23.5141 19.8135 23.0358L26.078 12.1852C26.2061 11.9633 26.4042 11.8133 26.6248 11.755C28.3156 11.3079 32.5997 10.304 36.9436 10.2893Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M53.8205 27.8661C55.2554 27.0376 57.0902 27.5293 57.9186 28.9641L64.1832 39.8147C64.5635 40.4734 64.6871 41.2663 64.4805 42.0274C64.007 43.7712 62.6814 48.1765 60.3956 52.1667C58.1532 56.0811 54.7511 59.9993 50.0013 60H45V64C45 64.4526 44.696 64.8488 44.2588 64.9659C43.8216 65.083 43.3602 64.8919 43.1339 64.5L37.3604 54.5C36.8245 53.5718 36.8245 52.4282 37.3604 51.5L43.1339 41.5C43.3603 41.108 43.8216 40.9169 44.2588 41.0341C44.696 41.1512 45 41.5474 45 42V47C45 47.5523 44.5523 48 44 48C43.4477 48 43 47.5523 43 47V45.732L39.0925 52.5C38.9139 52.8094 38.9139 53.1906 39.0925 53.5L43 60.2679V59C43 58.4477 43.4477 58 44 58L44.0118 58H49.9988L50.0064 57.9999C50.0171 57.9997 50.0359 57.9992 50.0621 57.998C50.1147 57.9958 50.1966 57.991 50.3019 57.9811C50.5135 57.961 50.814 57.9205 51.158 57.8393C51.859 57.6738 52.6676 57.3564 53.2942 56.77C53.8912 56.2112 54.3905 55.3477 54.4087 53.9247C54.4263 52.5544 53.9958 50.6392 52.7039 48H48.1243C47.572 48 47.1243 47.5523 47.1243 47C47.1243 46.4478 47.572 46 48.1243 46H51.5885L45.7942 35.9641C44.9658 34.5293 45.4574 32.6945 46.8923 31.8661L53.8205 27.8661ZM56.3847 54.4712C57.2107 53.5001 57.9692 52.3787 58.6602 51.1725C60.8195 47.4033 62.0921 43.1912 62.5504 41.5033C62.6102 41.2832 62.5792 41.0366 62.4511 40.8147L56.1865 29.9641C55.9104 29.4859 55.2988 29.322 54.8205 29.5981L47.8923 33.5981C47.414 33.8743 47.2501 34.4859 47.5263 34.9641L54.1866 46.5C54.1946 46.5139 54.2022 46.528 54.2096 46.5422C55.7949 49.6208 56.4331 52.0378 56.4086 53.9504C56.4063 54.1287 56.3982 54.3022 56.3847 54.4712Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M34.9999 57C34.9999 58.6568 33.6568 60 31.9999 60L19.4707 60C18.7102 60 17.9616 59.7106 17.4059 59.1511C16.1324 57.8692 12.9801 54.5185 10.6674 50.5439C8.39863 46.6447 6.70641 41.7393 9.08068 37.6254L11.5813 33.2942L8.11728 31.2942C7.72531 31.0679 7.53421 30.6065 7.65135 30.1694C7.7685 29.7322 8.16467 29.4282 8.61728 29.4282L20.1643 29.4282C21.2361 29.4282 22.2265 30 22.7624 30.9282L28.5359 40.9282C28.7622 41.3202 28.697 41.8153 28.3769 42.1353C28.0569 42.4553 27.5618 42.5205 27.1698 42.2942L22.8397 39.7942C22.3614 39.5181 22.1975 38.9065 22.4737 38.4282C22.7498 37.9499 23.3614 37.786 23.8397 38.0622L24.9378 38.6961L21.0303 31.9282C20.8517 31.6188 20.5216 31.4282 20.1643 31.4282L12.3493 31.4282L13.4474 32.0622C13.9257 32.3383 14.0896 32.9499 13.8134 33.4282C13.8118 33.431 13.8101 33.4338 13.8085 33.4366L10.814 38.6233L10.8103 38.63C10.8052 38.6393 10.7962 38.6558 10.784 38.6791C10.7597 38.7258 10.7229 38.7991 10.6789 38.8953C10.5904 39.0885 10.4752 39.369 10.3736 39.7075C10.1664 40.3974 10.037 41.2563 10.2316 42.0922C10.417 42.8886 10.9151 43.7527 12.1384 44.48C13.3163 45.1804 15.1901 45.7652 18.1217 45.9659L20.4115 41.9999C20.6876 41.5216 21.2992 41.3577 21.7775 41.6339C22.2558 41.91 22.4197 42.5216 22.1435 42.9999L20.4115 46L31.9999 46C33.6568 46 34.9999 47.3431 34.9999 49L34.9999 57ZM10.6771 45.918C11.1051 47.1189 11.697 48.3365 12.3961 49.5381C14.5807 53.2926 17.5921 56.5008 18.8248 57.7416C18.9855 57.9035 19.2145 58 19.4707 58L31.9999 58C32.5522 58 32.9999 57.5522 32.9999 57L32.9999 49C32.9999 48.4477 32.5522 48 31.9999 48L18.6794 48C18.6634 48 18.6474 47.9996 18.6314 47.9988C15.1725 47.8325 12.7603 47.1767 11.1162 46.1991C10.963 46.108 10.8167 46.0142 10.6771 45.918Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default RecycleIcon
