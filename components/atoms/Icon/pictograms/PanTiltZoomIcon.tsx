const PanTiltZoomIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M33.8094 14.3584C36.698 13.4035 39.5898 14.39 41.9594 16.8412C44.3036 19.2661 46.0792 23.0389 47.0804 27.4823C48.0848 31.9402 48.2716 36.8998 47.6155 41.5742C47.5387 42.1211 47.0331 42.5023 46.4862 42.4255C45.9393 42.3487 45.5581 41.8431 45.6349 41.2962C46.2586 36.8521 46.0788 32.136 45.1293 27.9219C44.1766 23.6934 42.525 20.3038 40.5215 18.2313C38.5435 16.1852 36.4208 15.6016 34.4371 16.2574C32.3698 16.9408 30.3118 19.0092 28.7178 22.4004C28.4829 22.9002 27.8873 23.1149 27.3874 22.88C26.8876 22.6451 26.6729 22.0494 26.9078 21.5496C28.6163 17.9146 31.0044 15.2857 33.8094 14.3584Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M38.3788 57.5766C35.4812 58.6205 32.5543 57.6983 30.1464 55.2669C27.7681 52.8654 25.9628 49.0849 24.9422 44.6174C23.9182 40.1346 23.7234 35.1359 24.3845 30.4258C24.4613 29.8789 24.9669 29.4977 25.5138 29.5745C26.0607 29.6513 26.4419 30.1569 26.3651 30.7038C25.7366 35.182 25.9242 39.9353 26.892 44.172C27.8633 48.4238 29.5412 51.8136 31.5675 53.8596C33.5641 55.8758 35.7022 56.415 37.7009 55.695C39.7882 54.943 41.8496 52.7798 43.4216 49.2968C43.6488 48.7934 44.2411 48.5695 44.7445 48.7967C45.2479 49.024 45.4718 49.6162 45.2446 50.1196C43.564 53.8429 41.1878 56.5646 38.3788 57.5766Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14.317 38.0612C13.4228 35.1796 14.452 32.3122 16.9164 29.9685C19.3567 27.6477 23.124 25.8922 27.5507 24.9043C31.9915 23.9132 36.9242 23.7319 41.5742 24.3845C42.1211 24.4613 42.5023 24.9669 42.4255 25.5138C42.3487 26.0607 41.8431 26.4419 41.2962 26.3651C36.8755 25.7446 32.1847 25.9193 27.9863 26.8563C23.7739 27.7964 20.3848 29.4299 18.2947 31.4177C16.2286 33.3826 15.6147 35.4949 16.2271 37.4685C16.8643 39.5219 18.8683 41.5769 22.1964 43.1849C22.6936 43.4252 22.902 44.0231 22.6617 44.5204C22.4214 45.0177 21.8235 45.226 21.3262 44.9857C17.7518 43.2586 15.1864 40.8629 14.317 38.0612Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M55.9965 36.1821C56.0454 34.9037 55.5781 33.5802 54.5212 32.2917C53.4574 30.9949 51.8345 29.7844 49.7154 28.7727C49.217 28.5347 49.0059 27.9378 49.2439 27.4394C49.4818 26.941 50.0787 26.7299 50.5771 26.9678C52.8811 28.0678 54.7686 29.4398 56.0675 31.0232C57.3731 32.6149 58.066 34.4029 57.9951 36.2586C57.9242 38.1107 57.0986 39.8693 55.6854 41.418C54.277 42.9614 52.2968 44.287 49.9163 45.3352C49.4108 45.5578 48.8206 45.3285 48.5981 44.823C48.3755 44.3176 48.6048 43.7274 49.1102 43.5048C51.3107 42.5359 53.0356 41.3547 54.2081 40.0698C55.3758 38.7902 55.9475 37.4641 55.9965 36.1821Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M42.4255 46.4862C42.5023 47.0331 42.1211 47.5387 41.5742 47.6155C37.9213 48.1282 34.0787 48.1282 30.4258 47.6155C29.8789 47.5387 29.4977 47.0331 29.5745 46.4862C29.6513 45.9393 30.1569 45.5581 30.7038 45.6349C34.1722 46.1217 37.8277 46.1217 41.2962 45.6349C41.8431 45.5581 42.3487 45.9393 42.4255 46.4862Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M35.2929 8.29289C35.6834 7.90237 36.3166 7.90237 36.7071 8.29289L38.7071 10.2929C39.0976 10.6834 39.0976 11.3166 38.7071 11.7071C38.3166 12.0976 37.6834 12.0976 37.2929 11.7071L36 10.4142L34.7071 11.7071C34.3166 12.0976 33.6834 12.0976 33.2929 11.7071C32.9024 11.3166 32.9024 10.6834 33.2929 10.2929L35.2929 8.29289Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M63.7071 35.2929C64.0976 35.6834 64.0976 36.3166 63.7071 36.7071L61.7071 38.7071C61.3166 39.0976 60.6834 39.0976 60.2929 38.7071C59.9024 38.3166 59.9024 37.6834 60.2929 37.2929L61.5858 36L60.2929 34.7071C59.9024 34.3166 59.9024 33.6834 60.2929 33.2929C60.6834 32.9024 61.3166 32.9024 61.7071 33.2929L63.7071 35.2929Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M36.7071 63.7071C36.3166 64.0976 35.6834 64.0976 35.2929 63.7071L33.2929 61.7071C32.9024 61.3166 32.9024 60.6834 33.2929 60.2929C33.6834 59.9024 34.3166 59.9024 34.7071 60.2929L36 61.5858L37.2929 60.2929C37.6834 59.9024 38.3166 59.9024 38.7071 60.2929C39.0976 60.6834 39.0976 61.3166 38.7071 61.7071L36.7071 63.7071Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8.29289 36.7071C7.90237 36.3166 7.90237 35.6834 8.29289 35.2929L10.2929 33.2929C10.6834 32.9024 11.3166 32.9024 11.7071 33.2929C12.0976 33.6834 12.0976 34.3166 11.7071 34.7071L10.4142 36L11.7071 37.2929C12.0976 37.6834 12.0976 38.3166 11.7071 38.7071C11.3166 39.0976 10.6834 39.0976 10.2929 38.7071L8.29289 36.7071Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M36 29C32.134 29 29 32.134 29 36C29 39.866 32.134 43 36 43C37.5723 43 39.0236 42.4816 40.1922 41.6064L45.2929 46.7071C45.6834 47.0976 46.3166 47.0976 46.7071 46.7071C47.0976 46.3166 47.0976 45.6834 46.7071 45.2929L41.6064 40.1922C42.4816 39.0236 43 37.5723 43 36C43 32.134 39.866 29 36 29ZM31 36C31 33.2386 33.2386 31 36 31C38.7614 31 41 33.2386 41 36C41 38.7614 38.7614 41 36 41C33.2386 41 31 38.7614 31 36Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default PanTiltZoomIcon
