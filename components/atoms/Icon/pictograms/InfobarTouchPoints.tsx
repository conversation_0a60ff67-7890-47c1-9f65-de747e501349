const InfobarTouchPointsIcon = ({ ...props }) => {
    return (
        <svg
            width="80"
            height="32"
            viewBox="0 0 80 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M26.667 9.33301C28.1396 9.33318 29.333 10.5273 29.333 12V20C29.333 21.4727 28.1396 22.6668 26.667 22.667H5.33301C3.8604 22.6668 2.66699 21.4726 2.66699 20V12C2.66699 10.5273 3.8604 9.33318 5.33301 9.33301H26.667ZM5.33301 11.333C4.96497 11.3332 4.66699 11.6319 4.66699 12V20C4.66699 20.3681 4.96497 20.6668 5.33301 20.667H26.667C27.035 20.6668 27.333 20.3681 27.333 20V12C27.333 11.6319 27.035 11.3332 26.667 11.333H5.33301ZM11 14.667C11.5523 14.667 12 15.1147 12 15.667C11.9998 16.2191 11.5522 16.667 11 16.667H7.66699C7.11482 16.667 6.66717 16.2191 6.66699 15.667C6.66699 15.1147 7.11471 14.667 7.66699 14.667H11ZM24.333 14.667C24.8853 14.667 25.333 15.1147 25.333 15.667C25.3328 16.2191 24.8852 16.667 24.333 16.667H21C20.4478 16.667 20.0002 16.2191 20 15.667C20 15.1147 20.4477 14.667 21 14.667H24.333Z"
                fill="black"
            />
            <g clipPath="url(#clip0_8078_117024)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M61.3749 5.66699C62.8246 5.66699 63.9999 6.84225 63.9999 8.29199V11.6496C64.2735 11.5529 64.5682 11.5004 64.8749 11.5003C65.8965 11.5003 66.7824 12.0838 67.2162 12.9359C67.5656 12.7637 67.959 12.667 68.3749 12.667C69.5853 12.667 70.6053 13.4861 70.9087 14.6004C71.2077 14.482 71.5337 14.417 71.8749 14.417C73.3246 14.417 74.4999 15.5922 74.4999 17.042V19.4038C74.4999 24.7034 70.2041 29.0001 64.9045 29.0003C61.167 29.0003 57.7694 26.8302 56.1978 23.4393L53.7551 18.1688C53.1337 16.8275 53.7241 15.2372 55.0699 14.6255C56.3737 14.033 57.9121 14.5774 58.5528 15.8582L58.7499 16.2524V8.29199C58.7499 6.84233 59.9252 5.66712 61.3749 5.66699ZM61.3749 7.41699C60.8917 7.41712 60.4999 7.80882 60.4999 8.29199V19.9393C60.5008 19.9823 60.4984 20.0257 60.493 20.068C60.4609 20.3257 60.3173 20.5489 60.1114 20.6867C60.0259 20.7438 59.9296 20.7866 59.8265 20.8109C59.7236 20.8352 59.6184 20.8396 59.5166 20.8268C59.2588 20.7948 59.0347 20.6501 58.8968 20.444C58.8734 20.4089 58.8524 20.372 58.8342 20.3335L56.9885 16.641C56.7689 16.2017 56.2404 16.015 55.7933 16.2183C55.3322 16.428 55.1305 16.973 55.3433 17.4328L57.7849 22.7033C59.0698 25.476 61.8485 27.2503 64.9045 27.2503C69.2376 27.2501 72.7499 23.7369 72.7499 19.4038V17.042C72.7499 16.5587 72.3581 16.167 71.8749 16.167C71.3917 16.1671 70.9999 16.5588 70.9999 17.042V17.6253C70.9999 18.1086 70.6081 18.5003 70.1249 18.5003C69.6417 18.5002 69.2499 18.1085 69.2499 17.6253V15.292C69.2499 14.8087 68.8581 14.417 68.3749 14.417C67.8917 14.4171 67.4999 14.8088 67.4999 15.292V17.042C67.4999 17.5252 67.1081 17.917 66.6249 17.917C66.1417 17.9169 65.7499 17.5252 65.7499 17.042V14.1253C65.7499 13.6421 65.3581 13.2503 64.8749 13.2503C64.3917 13.2505 63.9999 13.6422 63.9999 14.1253V16.4587C63.9999 16.9419 63.6081 17.3337 63.1249 17.3337C62.6417 17.3335 62.2499 16.9418 62.2499 16.4587V8.29199C62.2499 7.80874 61.8581 7.41699 61.3749 7.41699Z"
                    fill="black"
                />
                <path
                    d="M61.6757 2.17497C62.3771 2.20946 63.0683 2.36373 63.7185 2.63298L63.9942 2.75488C64.6291 3.05522 65.2077 3.46374 65.7054 3.96143L65.9139 4.17904C66.3855 4.69943 66.7646 5.29823 67.0339 5.94841L67.1421 6.22868C67.3787 6.89007 67.4999 7.58805 67.4999 8.29199C67.4999 8.77524 67.1081 9.16699 66.6249 9.16699C66.1417 9.16691 65.7499 8.77519 65.7499 8.29199C65.7499 7.78942 65.6635 7.29107 65.4947 6.81885L65.4172 6.61719C65.2249 6.15314 64.9539 5.7263 64.6174 5.35482L64.4681 5.19873C64.1127 4.84327 63.6991 4.55192 63.2456 4.3374L63.0497 4.24967C62.5853 4.05736 62.0911 3.94738 61.5902 3.92269L61.3749 3.91699C60.8724 3.91701 60.3739 4.00336 59.9017 4.1722L59.7001 4.24967C59.2361 4.44194 58.8092 4.71299 58.4377 5.04948L58.2816 5.19873C57.9261 5.55421 57.6348 5.96776 57.4203 6.42122L57.3326 6.61719C57.1127 7.14797 56.9999 7.71748 56.9999 8.29199C56.9999 8.77524 56.6081 9.16699 56.1249 9.16699C55.6417 9.16691 55.2499 8.77519 55.2499 8.29199C55.2499 7.48781 55.4082 6.6914 55.7159 5.94841L55.8378 5.67269C56.1381 5.03779 56.5466 4.45913 57.0443 3.96143L57.2619 3.75293C57.7823 3.28141 58.3811 2.90229 59.0313 2.63298L59.3116 2.52474C59.9729 2.28813 60.671 2.16701 61.3749 2.16699L61.6757 2.17497Z"
                    fill="black"
                />
            </g>
            <defs>
                <clipPath id="clip0_8078_117024">
                    <rect
                        width="28"
                        height="28"
                        fill="white"
                        transform="translate(50 1)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default InfobarTouchPointsIcon
