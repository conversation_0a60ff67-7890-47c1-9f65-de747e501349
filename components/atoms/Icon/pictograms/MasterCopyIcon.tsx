const MasterCopyIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M16.1213 18.2929C16.3089 18.1054 16.5632 18 16.8284 18H53C53.5523 18 54 18.4477 54 19V21C54 21.5523 54.4477 22 55 22C55.5523 22 56 21.5523 56 21V19C56 17.3431 54.6569 16 53 16H16.8284C16.0328 16 15.2697 16.3161 14.7071 16.8787L8.87868 22.7071C8.31607 23.2697 8 24.0328 8 24.8284V45C8 46.6569 9.34315 48 11 48H13C13.5523 48 14 47.5523 14 47C14 46.4477 13.5523 46 13 46H11C10.4477 46 10 45.5523 10 45V24.8284C10 24.5632 10.1054 24.3089 10.2929 24.1213L16.1213 18.2929Z"
                fill="currentColor"
            />
            <path
                d="M53 30C53.5523 30 54 30.4477 54 31V38.5858L56.2929 36.2929C56.6834 35.9024 57.3166 35.9024 57.7071 36.2929C58.0976 36.6834 58.0976 37.3166 57.7071 37.7071L53.7071 41.7071C53.3166 42.0976 52.6834 42.0976 52.2929 41.7071L48.2929 37.7071C47.9024 37.3166 47.9024 36.6834 48.2929 36.2929C48.6834 35.9024 49.3166 35.9024 49.7071 36.2929L52 38.5858V31C52 30.4477 52.4477 30 53 30Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24.8284 24C24.0328 24 23.2697 24.3161 22.7071 24.8787L16.8787 30.7071C16.3161 31.2697 16 32.0328 16 32.8284V53C16 54.6569 17.3431 56 19 56H61C62.6569 56 64 54.6569 64 53V27C64 25.3431 62.6569 24 61 24H24.8284ZM24.1213 26.2929C24.3089 26.1054 24.5632 26 24.8284 26H61C61.5523 26 62 26.4477 62 27V53C62 53.5523 61.5523 54 61 54H19C18.4477 54 18 53.5523 18 53V32.8284C18 32.5632 18.1054 32.3089 18.2929 32.1213L24.1213 26.2929Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default MasterCopyIcon
