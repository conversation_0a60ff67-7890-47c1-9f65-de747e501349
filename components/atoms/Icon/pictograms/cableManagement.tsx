const cableManagement = ({ ...props }) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g id="IconUSB">
                <path
                    id="Path"
                    d="M15.4666 2.0445C15.7333 1.68894 16.2666 1.68894 16.5333 2.0445L19.1999 5.60005C19.5295 6.03955 19.2159 6.66672 18.6666 6.66672H16.9999V17.0487L22.8157 14.1408C22.9286 14.0844 22.9999 13.9689 22.9999 13.8427V12.0001H22.6666C21.9302 12.0001 21.3333 11.4031 21.3333 10.6667V8.00005C21.3333 7.26367 21.9302 6.66672 22.6666 6.66672H25.3333C26.0696 6.66672 26.6666 7.26367 26.6666 8.00005V10.6667C26.6666 11.4031 26.0696 12.0001 25.3333 12.0001H24.9999V13.8427C24.9999 14.7265 24.5006 15.5344 23.7101 15.9297L16.9999 19.2848V21.3133C17.0002 21.3272 17.0002 21.3411 16.9999 21.355V24.1939C17.9772 24.5895 18.6666 25.5476 18.6666 26.6667C18.6666 28.1395 17.4727 29.3334 15.9999 29.3334C14.5272 29.3334 13.3333 28.1395 13.3333 26.6667C13.3333 25.5476 14.0226 24.5895 14.9999 24.1939V21.9514L8.28975 18.5963C7.49926 18.2011 6.99992 17.3931 6.99992 16.5093V14.4729C6.02264 14.0773 5.33325 13.1192 5.33325 12.0001C5.33325 10.5273 6.52716 9.33339 7.99992 9.33339C9.47268 9.33339 10.6666 10.5273 10.6666 12.0001C10.6666 13.1192 9.9772 14.0773 8.99992 14.4729V16.5093C8.99992 16.6356 9.07125 16.751 9.18418 16.8075L14.9999 19.7154V18.6884C14.9996 18.6744 14.9996 18.6605 14.9999 18.6466V6.66672H13.3333C12.7839 6.66672 12.4703 6.03955 12.7999 5.60005L15.4666 2.0445Z"
                    fill="#0C2588"
                />
            </g>
        </svg>
    )
}

export default cableManagement
