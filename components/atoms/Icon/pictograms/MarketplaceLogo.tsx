const MarketplaceLogo = ({ ...props }) => {
    return (
        <svg
            width="76"
            height="25"
            viewBox="0 0 76 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g clipPath="url(#clip0_1498_27222)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M56.4635 11.592L54.3976 8.45554L53.5532 9.29199V11.592H51.1405V0.584229H53.5532V6.99191L56.2373 3.76592H58.7856L55.9357 7.14131L59.0269 11.592H56.4635ZM8.0158 6.39369L8.0064 6.38665H8.00633L8.0068 6.38712L6.48916 5.51631L5.72964 5.95551V9.34866L6.4901 9.78363L8.64086 8.53944L7.902 8.11574L6.46709 8.94569V6.35386L10.6292 8.74417L10.6268 8.7592C10.4018 9.44163 9.97631 10.0292 9.39624 10.4581C8.80396 10.8958 8.10212 11.1269 7.36615 11.1269C5.47232 11.1269 3.93168 9.58015 3.93168 7.67928C3.93168 5.7784 5.47232 4.23166 7.36615 4.23166C8.81609 4.23166 10.1172 5.15514 10.6029 6.529C10.639 6.6309 10.6705 6.73569 10.6968 6.84087L11.4591 6.39745C11.4267 6.29273 11.3896 6.18841 11.3497 6.08699C11.0355 5.29554 10.4996 4.62062 9.7992 4.13493C9.08241 3.63797 8.24119 3.37494 7.36615 3.37494C6.22057 3.37494 5.14402 3.82259 4.33425 4.63564C3.52449 5.44869 3.07825 6.52994 3.07825 7.67928C3.07825 8.82862 3.52449 9.90987 4.33433 10.7229C5.14457 11.536 6.22112 11.9836 7.36623 11.9836C8.38033 11.9836 9.36384 11.6215 10.136 10.9644C10.896 10.3176 11.4093 9.42332 11.5846 8.44443L8.0158 6.39369ZM32.2771 11.592H34.6898V11.5918V6.59166C34.6898 6.26437 34.6596 5.92957 34.5993 5.58726C34.539 5.24495 34.4259 4.92494 34.26 4.62739C34.0941 4.32984 33.8579 4.09162 33.5513 3.91303C33.2445 3.73444 32.84 3.64514 32.3374 3.64514C31.714 3.64514 31.1536 3.77662 30.656 4.03958C30.1585 4.30253 29.7589 4.672 29.4573 5.14814C29.3567 4.7415 29.1331 4.38948 28.7862 4.0917C28.4395 3.79415 27.9393 3.64522 27.2859 3.64522C26.6123 3.64522 26.0268 3.76942 25.5292 4.01727C25.0316 4.26536 24.6369 4.62747 24.3455 5.10369V3.7659H22.1741V11.5919H24.5867V7.11264C24.7273 6.70435 24.9535 6.37613 25.2652 6.12828C25.5767 5.88043 25.8884 5.75623 26.2001 5.75623C26.5017 5.75623 26.7479 5.87236 26.939 6.10449C27.1299 6.33653 27.2255 6.70928 27.2255 7.22244V11.5919H29.6382V7.14848C29.7687 6.73386 29.99 6.39835 30.3017 6.1415C30.6132 5.88473 30.9249 5.7563 31.2366 5.7563C31.5582 5.7563 31.812 5.87479 31.9981 6.11184C32.184 6.34889 32.2771 6.71899 32.2771 7.22252V11.592ZM35.7751 9.25398C35.7751 8.74739 35.9207 8.29786 36.2124 7.90539C36.5039 7.51283 36.906 7.20487 37.4187 6.98128C37.9314 6.75793 38.5196 6.64602 39.183 6.64602C39.5046 6.64602 39.8213 6.6738 40.133 6.72897C40.4445 6.7843 40.7159 6.86233 40.9473 6.96274V6.61682C40.9473 6.18921 40.8165 5.86591 40.5552 5.64725C40.2936 5.42859 39.8966 5.31903 39.3639 5.31903C38.9316 5.31903 38.5195 5.39439 38.1275 5.5452C37.7354 5.69601 37.3231 5.91232 36.8909 6.19359L36.1671 4.65553C36.6897 4.31392 37.2351 4.05989 37.8032 3.89405C38.3711 3.72822 38.9719 3.64526 39.6052 3.64526C40.7912 3.64526 41.7137 3.92192 42.3722 4.47506C43.0306 5.02829 43.3599 5.83805 43.3599 6.90467V8.91957C43.3599 9.15662 43.3976 9.32457 43.473 9.42333C43.5484 9.52233 43.6765 9.57649 43.8575 9.58635V11.5919C43.6563 11.642 43.4703 11.6748 43.2996 11.6898C43.1285 11.7049 42.9777 11.7125 42.8472 11.7125C42.3949 11.7125 42.0529 11.622 41.8218 11.4411C41.5904 11.2601 41.4397 11.0139 41.3695 10.7022L41.3092 10.3553C40.9673 10.8077 40.5526 11.1522 40.0651 11.3882C39.5774 11.6243 39.0673 11.7426 38.5346 11.7426C38.0118 11.7426 37.5417 11.6356 37.1246 11.4222C36.7073 11.2088 36.3782 10.913 36.137 10.5356C35.8957 10.1581 35.775 9.73074 35.775 9.25382L35.7751 9.25398ZM40.5703 9.53978C40.6808 9.45213 40.7713 9.35196 40.8417 9.23981C40.9119 9.12766 40.9473 9.02772 40.9473 8.93984V8.25185C40.7461 8.17382 40.5199 8.11286 40.2688 8.06903C40.0174 8.0252 39.7862 8.00306 39.5751 8.00306C39.1428 8.00306 38.7809 8.10088 38.4894 8.29591C38.1977 8.49101 38.0521 8.7398 38.0521 9.04212C38.0521 9.20796 38.1023 9.36652 38.2029 9.51779C38.3032 9.66907 38.4416 9.78615 38.6175 9.86911C38.7933 9.95206 39.002 9.99346 39.2433 9.99346C39.4846 9.99346 39.7259 9.95198 39.9671 9.86911C40.2083 9.78615 40.4093 9.67635 40.5702 9.53971L40.5703 9.53978ZM48.5473 6.02038C49.0197 5.83443 49.5475 5.74138 50.1306 5.74138V3.57004C50.0802 3.5601 50.03 3.55493 49.9798 3.55493H49.7385C49.2659 3.56503 48.8136 3.72077 48.3814 4.02238C47.9491 4.32392 47.5922 4.74121 47.3108 5.27393V3.766H45.1093V11.5922H47.522V6.8422C47.733 6.48032 48.0747 6.20648 48.5473 6.02038ZM61.3191 11.4223C61.8418 11.6358 62.44 11.7427 63.1136 11.7427L63.1135 11.7429C63.7167 11.7429 64.2671 11.6498 64.7647 11.4639C65.2622 11.278 65.692 11.0318 66.0539 10.725C66.4158 10.4185 66.6971 10.0592 66.8984 9.64684L64.8476 9.05879C64.7369 9.3403 64.5234 9.56647 64.2067 9.73731C63.8901 9.90831 63.5456 9.99362 63.1738 9.99362C62.8923 9.99362 62.6209 9.93516 62.3595 9.81784C62.0981 9.70045 61.882 9.52468 61.7111 9.29005C61.5401 9.05558 61.4447 8.76751 61.4246 8.42535H67.2753C67.2853 8.32612 67.298 8.20951 67.3131 8.07545C67.3281 7.94162 67.3357 7.80498 67.3357 7.66599C67.3357 6.96101 67.1722 6.30089 66.8456 5.68544C66.5188 5.07 66.0389 4.57594 65.4055 4.20365C64.7722 3.83137 64.0083 3.64526 63.1135 3.64526C62.2187 3.64526 61.4548 3.83356 60.8215 4.21007C60.1882 4.58658 59.7031 5.0844 59.3664 5.7036C59.0294 6.3228 58.8612 7.00891 58.8612 7.76194C58.8612 8.28871 58.9566 8.79318 59.1477 9.27526C59.3386 9.75727 59.6175 10.1824 59.9846 10.5501C60.3514 10.9179 60.7964 11.2086 61.3191 11.4223ZM64.8024 6.94755H61.3795C61.4096 6.61839 61.5001 6.33751 61.6509 6.10523C61.8017 5.87296 62.0027 5.69131 62.254 5.56054C62.5052 5.42977 62.7767 5.36426 63.0684 5.36426C63.39 5.36426 63.6715 5.42977 63.9128 5.56054C64.1541 5.69131 64.355 5.8753 64.5159 6.11259C64.6766 6.3498 64.7723 6.62809 64.8024 6.94755ZM73.458 11.1547C73.2469 11.2553 73.008 11.3508 72.7418 11.4412C72.4753 11.5317 72.199 11.6045 71.9124 11.6599C71.6259 11.715 71.3368 11.7429 71.0454 11.7429C70.6432 11.7429 70.2764 11.676 69.9446 11.5424C69.6128 11.4085 69.3464 11.1882 69.1454 10.881C68.9442 10.574 68.8438 10.1678 68.8438 9.66238V5.60566H67.8335V3.76598H68.8438V1.2326H71.2565V3.76591H72.8549V5.60558H71.2565V8.80425C71.2664 9.06956 71.3318 9.25871 71.4525 9.3718C71.5731 9.48489 71.739 9.54116 71.9501 9.54116C72.131 9.54116 72.3143 9.50868 72.5005 9.44317C72.6864 9.3779 72.8549 9.32015 73.0057 9.26975L73.458 11.1547V11.1547ZM34.6898 13.1572H32.2771V19.0079C32.2771 19.6917 32.463 20.2168 32.835 20.5837C33.2068 20.9508 33.7398 21.1341 34.4334 21.1341C34.7751 21.1341 35.1421 21.0889 35.5342 20.9984C35.9263 20.9079 36.2732 20.7974 36.5747 20.6667L36.2581 18.8421C36.1374 18.9227 35.9991 18.983 35.8434 19.023C35.6874 19.0633 35.5392 19.0834 35.3985 19.0834C35.1671 19.0834 34.9914 19.0131 34.8707 18.8722C34.7501 18.7315 34.6898 18.4952 34.6898 18.1635V13.1572ZM36.9062 18.6453C36.9062 18.1387 37.0518 17.6892 37.3435 17.2967C37.635 16.9041 38.0372 16.5962 38.5498 16.3726C39.0625 16.1492 39.6507 16.0373 40.3141 16.0373C40.6357 16.0373 40.9524 16.0651 41.2641 16.1203C41.5756 16.1756 41.847 16.2536 42.0784 16.354V16.0081C42.0784 15.5805 41.9476 15.2572 41.6863 15.0385C41.4247 14.8199 41.0277 14.7103 40.495 14.7103C40.0627 14.7103 39.6506 14.7857 39.2586 14.9365C38.8665 15.0873 38.4542 15.3036 38.022 15.5849L37.2982 14.0468C37.8208 13.7052 38.3662 13.4512 38.9343 13.2854C39.5022 13.1195 40.103 13.0366 40.7363 13.0366C41.9223 13.0366 42.8448 13.3132 43.5034 13.8664C44.1617 14.4196 44.491 15.2293 44.491 16.296V18.3109C44.491 18.5479 44.5287 18.7159 44.6041 18.8146C44.6795 18.9136 44.8076 18.9678 44.9886 18.9776V20.9832C44.7874 21.0333 44.6014 21.0661 44.4307 21.0811C44.2596 21.0962 44.1088 21.1038 43.9783 21.1038C43.526 21.1038 43.184 21.0133 42.9529 20.8323C42.7215 20.6514 42.5708 20.4052 42.5006 20.0935L42.4403 19.7466C42.0984 20.199 41.6837 20.5435 41.1962 20.7795C40.7085 21.0156 40.1984 21.1339 39.6657 21.1339C39.1429 21.1339 38.6728 21.0269 38.2557 20.8135C37.8384 20.6001 37.5093 20.3043 37.2681 19.9269C37.0268 19.5494 36.9061 19.122 36.9061 18.6451L36.9062 18.6453ZM41.7014 18.9311C41.8119 18.8434 41.9024 18.7433 41.9728 18.6311C42.043 18.519 42.0784 18.419 42.0784 18.3311V17.6431C41.8772 17.5651 41.651 17.5042 41.3999 17.4603C41.1485 17.4165 40.9173 17.3944 40.7062 17.3944C40.2739 17.3944 39.912 17.4922 39.6205 17.6872C39.3288 17.8823 39.1832 18.1311 39.1832 18.4334C39.1832 18.5993 39.2334 18.7578 39.334 18.9091C39.4343 19.0604 39.5727 19.1774 39.7486 19.2604C39.9244 19.3434 40.1331 19.3848 40.3744 19.3848C40.6157 19.3848 40.857 19.3433 41.0982 19.2604C41.3394 19.1774 41.5404 19.0676 41.7013 18.931L41.7014 18.9311ZM47.7662 20.8065C48.2888 21.0247 48.887 21.1341 49.5606 21.1341L49.5604 21.1341C50.1735 21.1341 50.7214 21.0411 51.204 20.8551C51.6866 20.6693 52.1035 20.4231 52.4555 20.1163C52.8073 19.8097 53.0837 19.4504 53.285 19.0381L50.9477 18.3294C50.8672 18.4703 50.7541 18.6008 50.6084 18.7214C50.4626 18.8421 50.3017 18.9352 50.1259 19.0004C49.9499 19.0659 49.7565 19.0984 49.5453 19.0984C49.2235 19.0984 48.9271 19.0152 48.6557 18.8487C48.3843 18.6821 48.1705 18.4446 48.0148 18.136C47.8588 17.8273 47.7811 17.472 47.7811 17.0705C47.7811 16.669 47.8588 16.3187 48.0148 16.02C48.1706 15.7212 48.3817 15.4861 48.6482 15.3145C48.9144 15.143 49.2136 15.0572 49.5453 15.0572C49.8368 15.0572 50.1007 15.125 50.337 15.2607C50.5731 15.3965 50.7668 15.5849 50.9176 15.8262L53.2699 15.1175C52.9582 14.4943 52.4882 13.9917 51.8601 13.6095C51.2317 13.2276 50.4601 13.0365 49.5454 13.0365C48.6608 13.0365 47.9019 13.2226 47.2685 13.5947C46.6352 13.9669 46.1501 14.4582 45.8134 15.0685C45.4764 15.6787 45.3082 16.3461 45.3082 17.0704C45.3082 17.6164 45.4036 18.1324 45.5947 18.6187C45.7856 19.105 46.0645 19.5366 46.4317 19.9136C46.7985 20.2906 47.2434 20.5883 47.7662 20.8065ZM58.017 21.134C57.3434 21.134 56.7452 21.0271 56.2226 20.8136C55.6998 20.5999 55.2549 20.3092 54.8881 19.9414C54.521 19.5737 54.242 19.1486 54.0512 18.6666C53.8601 18.1845 53.7646 17.68 53.7646 17.1532C53.7646 16.4002 53.9328 15.7141 54.2698 15.0949C54.6065 14.4757 55.0916 13.9779 55.7249 13.6014C56.3583 13.2249 57.1221 13.0366 58.0169 13.0366C58.9118 13.0366 59.6756 13.2227 60.309 13.5949C60.9423 13.9672 61.4222 14.4613 61.749 15.0767C62.0756 15.6922 62.2391 16.3523 62.2391 17.0573C62.2391 17.1963 62.2315 17.3329 62.2165 17.4667C62.2014 17.6008 62.1887 17.7174 62.1788 17.8166H56.3281C56.3481 18.1588 56.4435 18.4469 56.6146 18.6814C56.7854 18.916 57.0015 19.0917 57.263 19.2091C57.5243 19.3265 57.7957 19.3849 58.0773 19.3849C58.449 19.3849 58.7935 19.2996 59.1102 19.1286C59.4268 18.9578 59.6403 18.7316 59.751 18.4501L61.8018 19.0381C61.6006 19.4505 61.3192 19.8098 60.9574 20.1163C60.5954 20.4231 60.1657 20.6693 59.6681 20.8552C59.1705 21.0411 58.6201 21.1342 58.0169 21.1342L58.017 21.134ZM56.2829 16.3389H59.7059C59.6758 16.0194 59.58 15.7411 59.4194 15.5039C59.2585 15.2666 59.0575 15.0826 58.8162 14.9518C58.5749 14.8211 58.2934 14.7556 57.9718 14.7556C57.6801 14.7556 57.4086 14.8211 57.1575 14.9518C56.9061 15.0826 56.7051 15.2643 56.5543 15.4965C56.4035 15.7288 56.3131 16.0097 56.2829 16.3389ZM24.2963 14.358C25.0331 13.4936 26.1178 12.9471 27.3275 12.9471V12.9472C29.5471 12.9472 31.3465 14.7867 31.3465 17.0558C31.3465 19.3248 29.5471 21.1643 27.3275 21.1643C26.2682 21.1643 25.3049 20.7452 24.5869 20.0606V24.1955H22.1742V14.7205H22.1741V13.1575H24.2963V14.358ZM24.5354 17.0557C24.5354 18.1857 25.4429 19.1017 26.5625 19.1017C27.6821 19.1017 28.5897 18.1857 28.5897 17.0557C28.5897 15.9257 27.6821 15.0096 26.5625 15.0096C25.4429 15.0096 24.5354 15.9257 24.5354 17.0557Z"
                    fill="white"
                />
                <path
                    d="M16.8913 12.7664C14.6221 12.7664 12.7826 14.6059 12.7826 16.875V20.9837H16.8913C19.1605 20.9837 21 19.1442 21 16.875C21 14.6059 19.1605 12.7664 16.8913 12.7664Z"
                    fill="url(#paint0_linear_1498_27222)"
                />
                <path
                    d="M21 10.4185C21 11.2829 20.2993 11.9837 19.4348 11.9837C19.1172 11.9837 18.8217 11.8891 18.575 11.7266C18.3282 11.564 18.1301 11.3335 18.0075 11.0617L16.8741 8.55046C16.8435 8.48253 16.7752 8.43518 16.6958 8.43518C16.6163 8.43518 16.548 8.48261 16.5174 8.55062L15.384 11.0617C15.1387 11.6054 14.5919 11.9837 13.9567 11.9837C13.0923 11.9837 12.3914 11.2829 12.3914 10.4185C12.3914 10.1009 12.4861 9.80537 12.6486 9.55862C12.8111 9.31186 13.0417 9.11386 13.3135 8.99115L15.8247 7.85777C15.8927 7.8271 15.9399 7.75878 15.9399 7.67934C15.9399 7.59991 15.8926 7.53158 15.8246 7.50098L13.3134 6.36761C12.7697 6.12226 12.3914 5.57546 12.3914 4.94021C12.3914 4.07582 13.0922 3.375 13.9566 3.375C14.2742 3.375 14.5697 3.46962 14.8165 3.63216C15.0633 3.79471 15.2613 4.02519 15.384 4.29707L16.5173 6.8083C16.548 6.87623 16.6163 6.9235 16.6957 6.9235C16.7751 6.9235 16.8435 6.87615 16.8741 6.80814L18.0075 4.29707C18.2529 3.75339 18.7997 3.375 19.4348 3.375C20.2993 3.375 21 4.07582 21 4.94021C21 5.2578 20.9054 5.55331 20.7429 5.80014C20.5803 6.0469 20.3498 6.24498 20.078 6.36761L17.567 7.50098C17.499 7.53158 17.4515 7.59991 17.4515 7.67934C17.4515 7.76856 17.5085 7.8314 17.5668 7.85777L20.078 8.99115C20.6216 9.23649 21 9.7833 21 10.4185Z"
                    fill="url(#paint1_linear_1498_27222)"
                />
                <path
                    d="M3.97834 13.1577C3.43803 13.1577 3.00008 13.5957 3.00008 14.136C3.00008 14.6763 3.43803 15.1142 3.97834 15.1142H8.90258L3.7117 18.4977C3.28323 18.777 3 19.2603 3 19.8099C3 20.6743 3.70075 21.3751 4.56521 21.3751C5.11476 21.3751 5.5981 21.0918 5.87733 20.6634L9.26094 15.4726V20.3968C9.26094 20.9371 9.69888 21.3751 10.2392 21.3751C10.7795 21.3751 11.2175 20.9371 11.2175 20.3968V13.1577H3.97834Z"
                    fill="url(#paint2_linear_1498_27222)"
                />
            </g>
            <defs>
                <linearGradient
                    id="paint0_linear_1498_27222"
                    x1="21"
                    y1="12.7664"
                    x2="12.7826"
                    y2="20.9837"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop offset="0.3281" stopColor="#FBDB00" />
                    <stop offset="1" stopColor="#FF9959" />
                </linearGradient>
                <linearGradient
                    id="paint1_linear_1498_27222"
                    x1="21"
                    y1="3.375"
                    x2="12.3914"
                    y2="11.9837"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop offset="0.2969" stopColor="#4DE36E" />
                    <stop offset="1" stopColor="#31AFA7" />
                </linearGradient>
                <linearGradient
                    id="paint2_linear_1498_27222"
                    x1="11.2175"
                    y1="13.1577"
                    x2="3.00008"
                    y2="21.3752"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop offset="0.3385" stopColor="#FF60D0" />
                    <stop offset="1" stopColor="#A638FE" />
                </linearGradient>
                <clipPath id="clip0_1498_27222">
                    <rect
                        width="76"
                        height="24"
                        fill="white"
                        transform="translate(0 0.375)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default MarketplaceLogo
