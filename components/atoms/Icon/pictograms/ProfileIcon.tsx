const ProfileIcon = ({ ...props }) => {
    return (
        <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.5998 12.7998C9.5998 11.0325 11.0325 9.5998 12.7998 9.5998H19.1998C20.9671 9.5998 22.3998 11.0325 22.3998 12.7998V19.1998C22.3998 20.9671 20.9671 22.3998 19.1998 22.3998H12.7998C11.0325 22.3998 9.5998 20.9671 9.5998 19.1998V12.7998ZM12.7998 11.1998H15.1998V15.1998H11.1998V12.7998C11.1998 11.9161 11.9161 11.1998 12.7998 11.1998ZM20.7998 15.1998V12.7998C20.7998 11.9161 20.0835 11.1998 19.1998 11.1998H16.7998V15.1998H20.7998ZM16.7998 16.7998H20.7998V19.1998C20.7998 20.0835 20.0835 20.7998 19.1998 20.7998H16.7998V16.7998ZM15.1998 16.7998V20.7998H12.7998C11.9161 20.7998 11.1998 20.0835 11.1998 19.1998V16.7998H15.1998Z"
                fill="#151515"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M4.7998 7.9998C4.7998 6.23249 6.23249 4.7998 7.9998 4.7998H23.9998C25.7671 4.7998 27.1998 6.23249 27.1998 7.9998V23.9998C27.1998 25.7671 25.7671 27.1998 23.9998 27.1998H7.9998C6.23249 27.1998 4.7998 25.7671 4.7998 23.9998V7.9998ZM7.9998 6.3998H23.9998C24.8835 6.3998 25.5998 7.11615 25.5998 7.9998V23.9998C25.5998 24.8835 24.8835 25.5998 23.9998 25.5998H7.9998C7.11615 25.5998 6.3998 24.8835 6.3998 23.9998V7.9998C6.3998 7.11615 7.11615 6.3998 7.9998 6.3998Z"
                fill="#151515"
            />
        </svg>
    )
}

export default ProfileIcon
