const SonyStarvisSensorIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M26 29C26 27.3431 27.3431 26 29 26H43C44.6569 26 46 27.3431 46 29V43C46 44.6569 44.6569 46 43 46H29C27.3431 46 26 44.6569 26 43V29ZM29 28C28.4477 28 28 28.4477 28 29V43C28 43.5523 28.4477 44 29 44H43C43.5523 44 44 43.5523 44 43V29C44 28.4477 43.5523 28 43 28H29Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M27 22C24.2386 22 22 24.2386 22 27V45C22 47.7614 24.2386 50 27 50H45C47.7614 50 50 47.7614 50 45V27C50 24.2386 47.7614 22 45 22H27ZM24 27C24 25.3431 25.3431 24 27 24H45C46.6569 24 48 25.3431 48 27V45C48 46.6569 46.6569 48 45 48H27C25.3431 48 24 46.6569 24 45V27Z"
                fill="currentColor"
            />
            <path
                d="M45 19C45 19.5523 45.4477 20 46 20C46.5523 20 47 19.5523 47 19V17C47 16.4477 46.5523 16 46 16C45.4477 16 45 16.4477 45 17V19Z"
                fill="currentColor"
            />
            <path
                d="M46 56C45.4477 56 45 55.5523 45 55V53C45 52.4477 45.4477 52 46 52C46.5523 52 47 52.4477 47 53V55C47 55.5523 46.5523 56 46 56Z"
                fill="currentColor"
            />
            <path
                d="M55 27C55.5523 27 56 26.5523 56 26C56 25.4477 55.5523 25 55 25L53 25C52.4477 25 52 25.4477 52 26C52 26.5523 52.4477 27 53 27L55 27Z"
                fill="currentColor"
            />
            <path
                d="M20 26C20 26.5523 19.5523 27 19 27L17 27C16.4477 27 16 26.5523 16 26C16 25.4477 16.4477 25 17 25L19 25C19.5523 25 20 25.4477 20 26Z"
                fill="currentColor"
            />
            <path
                d="M25 19C25 19.5523 25.4477 20 26 20C26.5523 20 27 19.5523 27 19V17C27 16.4477 26.5523 16 26 16C25.4477 16 25 16.4477 25 17V19Z"
                fill="currentColor"
            />
            <path
                d="M26 56C25.4477 56 25 55.5523 25 55V53C25 52.4477 25.4477 52 26 52C26.5523 52 27 52.4477 27 53V55C27 55.5523 26.5523 56 26 56Z"
                fill="currentColor"
            />
            <path
                d="M29 55C29 55.5523 29.4477 56 30 56C30.5523 56 31 55.5523 31 55V53C31 52.4477 30.5523 52 30 52C29.4477 52 29 52.4477 29 53V55Z"
                fill="currentColor"
            />
            <path
                d="M56 46C56 46.5523 55.5523 47 55 47H53C52.4477 47 52 46.5523 52 46C52 45.4477 52.4477 45 53 45H55C55.5523 45 56 45.4477 56 46Z"
                fill="currentColor"
            />
            <path
                d="M19 47C19.5523 47 20 46.5523 20 46C20 45.4477 19.5523 45 19 45H17C16.4477 45 16 45.4477 16 46C16 46.5523 16.4477 47 17 47H19Z"
                fill="currentColor"
            />
            <path
                d="M30 20C29.4477 20 29 19.5523 29 19V17C29 16.4477 29.4477 16 30 16C30.5523 16 31 16.4477 31 17V19C31 19.5523 30.5523 20 30 20Z"
                fill="currentColor"
            />
            <path
                d="M33 19C33 19.5523 33.4477 20 34 20C34.5523 20 35 19.5523 35 19V17C35 16.4477 34.5523 16 34 16C33.4477 16 33 16.4477 33 17V19Z"
                fill="currentColor"
            />
            <path
                d="M38 20C37.4477 20 37 19.5523 37 19V17C37 16.4477 37.4477 16 38 16C38.5523 16 39 16.4477 39 17V19C39 19.5523 38.5523 20 38 20Z"
                fill="currentColor"
            />
            <path
                d="M41 19C41 19.5523 41.4477 20 42 20C42.5523 20 43 19.5523 43 19V17C43 16.4477 42.5523 16 42 16C41.4477 16 41 16.4477 41 17V19Z"
                fill="currentColor"
            />
            <path
                d="M56 42C56 42.5523 55.5523 43 55 43H53C52.4477 43 52 42.5523 52 42C52 41.4477 52.4477 41 53 41H55C55.5523 41 56 41.4477 56 42Z"
                fill="currentColor"
            />
            <path
                d="M19 43C19.5523 43 20 42.5523 20 42C20 41.4477 19.5523 41 19 41H17C16.4477 41 16 41.4477 16 42C16 42.5523 16.4477 43 17 43H19Z"
                fill="currentColor"
            />
            <path
                d="M38 56C37.4477 56 37 55.5523 37 55V53C37 52.4477 37.4477 52 38 52C38.5523 52 39 52.4477 39 53V55C39 55.5523 38.5523 56 38 56Z"
                fill="currentColor"
            />
            <path
                d="M41 55C41 55.5523 41.4477 56 42 56C42.5523 56 43 55.5523 43 55V53C43 52.4477 42.5523 52 42 52C41.4477 52 41 52.4477 41 53V55Z"
                fill="currentColor"
            />
            <path
                d="M56 30C56 30.5523 55.5523 31 55 31H53C52.4477 31 52 30.5523 52 30C52 29.4477 52.4477 29 53 29H55C55.5523 29 56 29.4477 56 30Z"
                fill="currentColor"
            />
            <path
                d="M55 35C55.5523 35 56 34.5523 56 34C56 33.4477 55.5523 33 55 33H53C52.4477 33 52 33.4477 52 34C52 34.5523 52.4477 35 53 35H55Z"
                fill="currentColor"
            />
            <path
                d="M20 30C20 30.5523 19.5523 31 19 31H17C16.4477 31 16 30.5523 16 30C16 29.4477 16.4477 29 17 29H19C19.5523 29 20 29.4477 20 30Z"
                fill="currentColor"
            />
            <path
                d="M19 39C19.5523 39 20 38.5523 20 38C20 37.4477 19.5523 37 19 37H17C16.4477 37 16 37.4477 16 38C16 38.5523 16.4477 39 17 39H19Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17 12C14.2386 12 12 14.2386 12 17V55C12 57.7614 14.2386 60 17 60H55C57.7614 60 60 57.7614 60 55V17C60 14.2386 57.7614 12 55 12H17ZM14 17C14 15.3431 15.3431 14 17 14H55C56.6569 14 58 15.3431 58 17V55C58 56.6569 56.6569 58 55 58H17C15.3431 58 14 56.6569 14 55V17Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default SonyStarvisSensorIcon
