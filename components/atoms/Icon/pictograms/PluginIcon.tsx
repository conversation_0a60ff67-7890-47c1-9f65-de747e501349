const PluginIcon = ({ ...props }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M16.0004 4.8002C15.1167 4.8002 14.4004 5.51654 14.4004 6.4002C14.4004 8.16751 12.9677 9.6002 11.2004 9.6002H9.60039C8.71674 9.6002 8.00039 10.3165 8.00039 11.2002V12.8002C8.00039 13.6839 8.71674 14.4002 9.60039 14.4002C11.3677 14.4002 12.8004 15.8329 12.8004 17.6002C12.8004 19.3675 11.3677 20.8002 9.60039 20.8002C8.71674 20.8002 8.00039 21.5165 8.00039 22.4002V24.0002C8.00039 24.8839 8.71674 25.6002 9.60039 25.6002H22.4004C23.284 25.6002 24.0004 24.8839 24.0004 24.0002V11.2002C24.0004 10.3165 23.284 9.6002 22.4004 9.6002H20.8004C19.0331 9.6002 17.6004 8.16751 17.6004 6.4002C17.6004 5.51654 16.884 4.8002 16.0004 4.8002ZM12.8004 6.4002C12.8004 4.63288 14.2331 3.2002 16.0004 3.2002C17.7677 3.2002 19.2004 4.63288 19.2004 6.4002C19.2004 7.28385 19.9167 8.0002 20.8004 8.0002H22.4004C24.1677 8.0002 25.6004 9.43288 25.6004 11.2002V24.0002C25.6004 25.7675 24.1677 27.2002 22.4004 27.2002H9.60039C7.83308 27.2002 6.40039 25.7675 6.40039 24.0002V22.4002C6.40039 20.6329 7.83308 19.2002 9.60039 19.2002C10.484 19.2002 11.2004 18.4839 11.2004 17.6002C11.2004 16.7165 10.484 16.0002 9.60039 16.0002C7.83308 16.0002 6.40039 14.5675 6.40039 12.8002V11.2002C6.40039 9.43288 7.83308 8.0002 9.60039 8.0002H11.2004C12.084 8.0002 12.8004 7.28385 12.8004 6.4002Z"
                fill="#151515"
            />
        </svg>
    )
}

export default PluginIcon
