const TransparentPricingIcon = ({ ...props }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M53.0152 22.9941C54.2961 22.2527 55.9267 22.5562 56.855 23.709L62.3668 30.5537C63.5212 31.9874 63.1239 34.1138 61.5298 35.0342L58.5005 36.7832C58.0222 37.0593 57.4105 36.8953 57.1343 36.417C56.8582 35.9387 57.0223 35.3269 57.5005 35.0508L60.5298 33.3018C61.0612 32.995 61.1939 32.2865 60.8091 31.8086L55.2964 24.9629C54.987 24.5789 54.444 24.4777 54.0171 24.7246L37.5074 34.2832L43.7076 42.0332C44.0162 42.419 44.5599 42.5214 44.9878 42.2744L53.5005 37.3604C53.8099 37.182 54.1913 37.1818 54.5005 37.3604C54.8098 37.539 55.0005 37.8694 55.0005 38.2266V48.8232C55.0005 49.9127 54.409 50.9172 53.4566 51.4463L36.4859 60.874C36.1762 61.0461 35.7981 61.042 35.4927 60.8623C35.1874 60.6826 35.0005 60.3543 35.0005 60V43C35.0006 42.4478 35.4483 42 36.0005 42C36.5526 42.0003 37.0005 42.4479 37.0005 43V58.3008L52.4859 49.6973C52.8032 49.5209 53.0005 49.1863 53.0005 48.8232V39.959L45.9878 44.0068C44.7042 44.7478 43.0721 44.4404 42.146 43.2832L36.0025 35.6045L29.9078 43.2725C28.9831 44.4358 27.3461 44.7469 26.0591 44.0039L18.0005 39.3506V47.8477C18.0006 48.2043 18.19 48.5341 18.4986 48.7129L32.5015 56.8252C32.9794 57.1021 33.1426 57.7145 32.8658 58.1924C32.5889 58.6702 31.9764 58.8325 31.4986 58.5557L17.4957 50.4434C16.5699 49.9069 16.0006 48.9176 16.0005 47.8477V38.1963L10.5162 35.0303C8.92542 34.1119 8.52593 31.9919 9.67339 30.5576L15.1441 23.7188C16.071 22.5601 17.7057 22.2537 18.9898 22.9971L36.0005 32.8447L53.0152 22.9941ZM17.9878 24.7275C17.5598 24.4799 17.0155 24.5826 16.7066 24.9688L11.2349 31.8076C10.8527 32.2857 10.986 32.9918 11.5162 33.2979L27.0591 42.2725C27.4881 42.5201 28.0341 42.4161 28.3423 42.0283L34.4966 34.2852L17.9878 24.7275Z"
                fill="currentColor"
            />
            <path
                d="M42.3697 10.2246C42.7625 9.9045 43.3415 9.92694 43.7076 10.293C44.0734 10.659 44.096 11.2382 43.7759 11.6309L43.7076 11.707L29.7076 25.707C29.3171 26.0975 28.6841 26.0973 28.2935 25.707C27.903 25.3165 27.903 24.6835 28.2935 24.293L42.2935 10.293L42.3697 10.2246Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M40.5005 19C42.4333 19.0002 44.0005 20.5672 44.0005 22.5C44.0005 24.4328 42.4333 25.9998 40.5005 26C38.5675 26 37.0005 24.433 37.0005 22.5C37.0005 20.567 38.5675 19 40.5005 19ZM40.5005 21C39.6721 21 39.0005 21.6716 39.0005 22.5C39.0005 23.3284 39.6721 24 40.5005 24C41.3288 23.9998 42.0005 23.3283 42.0005 22.5C42.0005 21.6717 41.3288 21.0002 40.5005 21Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M31.5005 10C33.4333 10.0002 35.0005 11.5672 35.0005 13.5C35.0005 15.4328 33.4333 16.9998 31.5005 17C29.5675 17 28.0005 15.433 28.0005 13.5C28.0005 11.567 29.5675 10 31.5005 10ZM31.5005 12C30.6721 12 30.0005 12.6716 30.0005 13.5C30.0005 14.3284 30.6721 15 31.5005 15C32.3288 14.9998 33.0005 14.3283 33.0005 13.5C33.0005 12.6717 32.3288 12.0002 31.5005 12Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default TransparentPricingIcon
