const Multidevice4k60Icon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M49.7071 10.1213C49.3166 9.7308 48.6834 9.7308 48.2929 10.1213L22.4142 36L25.2071 38.7929C25.5976 39.1834 25.5976 39.8166 25.2071 40.2071C24.8166 40.5976 24.1834 40.5976 23.7929 40.2071L15.7071 32.1213C15.3166 31.7308 14.6834 31.7308 14.2929 32.1213L12.7071 33.7071C12.3166 34.0976 11.6834 34.0976 11.2929 33.7071C10.9024 33.3166 10.9024 32.6834 11.2929 32.2929L12.8787 30.7071C14.0503 29.5355 15.9497 29.5355 17.1213 30.7071L21 34.5858L46.8787 8.70711C48.0503 7.53554 49.9497 7.53554 51.1213 8.70711L60.2929 17.8787C61.4645 19.0503 61.4645 20.9497 60.2929 22.1213L58.2071 24.2071C57.8166 24.5976 57.1834 24.5976 56.7929 24.2071C56.4024 23.8166 56.4024 23.1834 56.7929 22.7929L58.8787 20.7071C59.2692 20.3166 59.2692 19.6834 58.8787 19.2929L49.7071 10.1213ZM49.7071 23.1213C49.3166 22.7308 48.6834 22.7308 48.2929 23.1213L22.4142 49L33 59.5858L58.8787 33.7071C59.2692 33.3166 59.2692 32.6834 58.8787 32.2929L49.7071 23.1213ZM34.4142 61L35.7071 62.2929C36.0976 62.6834 36.0976 63.3166 35.7071 63.7071C35.3166 64.0976 34.6834 64.0976 34.2929 63.7071L30 59.4142L29.1213 60.2929C27.9497 61.4645 26.0503 61.4645 24.8787 60.2929L19.7071 55.1213C18.5355 53.9498 18.5355 52.0503 19.7071 50.8787L20.5858 50L15.7071 45.1213C15.3166 44.7308 14.6834 44.7308 14.2929 45.1213L12.7071 46.7071C12.3166 47.0976 11.6834 47.0976 11.2929 46.7071C10.9024 46.3166 10.9024 45.6834 11.2929 45.2929L12.8787 43.7071C14.0503 42.5355 15.9497 42.5355 17.1213 43.7071L21 47.5858L46.8787 21.7071C48.0503 20.5355 49.9497 20.5355 51.1213 21.7071L60.2929 30.8787C61.4645 32.0503 61.4645 33.9498 60.2929 35.1213L54.4142 41L55.2929 41.8787C56.4645 43.0503 56.4645 44.9497 55.2929 46.1213L49.1213 52.2929C47.9497 53.4645 46.0503 53.4645 44.8787 52.2929L44 51.4142L42.4142 53L43.2929 53.8787C44.4645 55.0503 44.4645 56.9497 43.2929 58.1213L41.1213 60.2929C39.9497 61.4645 38.0503 61.4645 36.8787 60.2929L36 59.4142L34.4142 61ZM37.4142 58L38.2929 58.8787C38.6834 59.2692 39.3166 59.2692 39.7071 58.8787L41.8787 56.7071C42.2692 56.3166 42.2692 55.6834 41.8787 55.2929L41 54.4142L37.4142 58ZM45.4142 50L46.2929 50.8787C46.6834 51.2692 47.3166 51.2692 47.7071 50.8787L53.8787 44.7071C54.2692 44.3166 54.2692 43.6834 53.8787 43.2929L53 42.4142L45.4142 50ZM22 51.4142L21.1213 52.2929C20.7308 52.6834 20.7308 53.3166 21.1213 53.7071L26.2929 58.8787C26.6834 59.2692 27.3166 59.2692 27.7071 58.8787L28.5858 58L22 51.4142ZM49.7071 32.2929C50.0976 32.6834 50.0976 33.3166 49.7071 33.7071L40.7071 42.7071C40.3166 43.0976 39.6834 43.0976 39.2929 42.7071C38.9024 42.3166 38.9024 41.6834 39.2929 41.2929L48.2929 32.2929C48.6834 31.9024 49.3166 31.9024 49.7071 32.2929ZM35 44C33.3431 44 32 45.3432 32 47C32 48.6569 33.3431 50 35 50C36.6569 50 38 48.6569 38 47C38 45.3432 36.6569 44 35 44ZM30 47C30 44.2386 32.2386 42 35 42C37.7614 42 40 44.2386 40 47C40 49.7614 37.7614 52 35 52C32.2386 52 30 49.7614 30 47Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default Multidevice4k60Icon
