const DiscountIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M31.5 34C29.567 34 28 35.567 28 37.5C28 39.433 29.567 41 31.5 41C33.433 41 35 39.433 35 37.5C35 35.567 33.433 34 31.5 34ZM30 37.5C30 36.6716 30.6716 36 31.5 36C32.3284 36 33 36.6716 33 37.5C33 38.3284 32.3284 39 31.5 39C30.6716 39 30 38.3284 30 37.5Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M44.5 35C42.567 35 41 36.567 41 38.5C41 40.433 42.567 42 44.5 42C46.433 42 48 40.433 48 38.5C48 36.567 46.433 35 44.5 35ZM43 38.5C43 37.6716 43.6716 37 44.5 37C45.3284 37 46 37.6716 46 38.5C46 39.3284 45.3284 40 44.5 40C43.6716 40 43 39.3284 43 38.5Z"
                fill="currentColor"
            />
            <path
                d="M35.7575 29.0299C36.2933 28.8959 36.8362 29.2217 36.9702 29.7575L40.9702 45.7575C41.1041 46.2933 40.7783 46.8362 40.2425 46.9702C39.7068 47.1041 39.1638 46.7783 39.0299 46.2425L35.0299 30.2425C34.8959 29.7068 35.2217 29.1638 35.7575 29.0299Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M11.4804 12.3382C10.3357 12.8754 9.60159 13.9326 9.26084 15.2235C8.92328 16.5023 8.96227 18.0205 9.30731 19.5983C9.65412 21.1842 10.3084 22.8242 11.1934 24.3527C11.9851 25.7201 12.9459 26.971 14 27.9985V34.1716C14 34.9672 14.3161 35.7303 14.8787 36.2929L39.8787 61.2929C41.0503 62.4645 42.9498 62.4645 44.1213 61.2929L61.2929 44.1213C62.4645 42.9498 62.4645 41.0503 61.2929 39.8787L36.2929 14.8787C35.7303 14.3161 34.9672 14 34.1716 14H17.8647C17.004 13.285 16.1021 12.7284 15.2001 12.3807C13.9232 11.8884 12.612 11.807 11.4804 12.3382ZM17.1083 16H17C16.4477 16 16 16.4477 16 17V34.1716C16 34.4368 16.1054 34.6912 16.2929 34.8787L41.2929 59.8787C41.6834 60.2692 42.3166 60.2692 42.7071 59.8787L59.8787 42.7071C60.2692 42.3166 60.2692 41.6834 59.8787 41.2929L34.8787 16.2929C34.6912 16.1054 34.4368 16 34.1716 16H19.8351C20.361 16.641 20.85 17.3344 21.2907 18.0627C21.5209 18.0215 21.758 18 22 18C24.2092 18 26 19.7909 26 22C26 24.2092 24.2092 26 22 26C19.7909 26 18 24.2092 18 22C18 20.7541 18.5696 19.6413 19.4626 18.9077C18.8761 17.9679 18.2039 17.105 17.4842 16.3688C17.3592 16.241 17.2338 16.118 17.1083 16ZM15.2131 14.59C14.9636 14.4532 14.7188 14.3386 14.4807 14.2468C13.5408 13.8845 12.8232 13.9172 12.3302 14.1486C11.8503 14.3739 11.4248 14.8618 11.1946 15.7339C10.9612 16.6181 10.9613 17.7998 11.2611 19.171C11.5592 20.5341 12.1316 21.9817 12.9242 23.3505C13.2547 23.9215 13.6161 24.4657 14 24.9741V17C14 16.0127 14.477 15.1367 15.2131 14.59ZM20.4481 20.7384C20.1679 21.0825 20 21.5217 20 22C20 22.9246 20.6274 23.7027 21.4797 23.9317C21.293 22.9096 20.9433 21.8208 20.4481 20.7384ZM23.4168 23.4117C23.7772 23.05 24 22.551 24 22C24 21.0046 23.2729 20.1791 22.3208 20.0256C22.8252 21.1499 23.199 22.2983 23.4168 23.4117Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default DiscountIcon
