const CommentaryIcon = ({ ...props }) => {
    return (
        <svg
            width="72"
            height="72"
            viewBox="0 0 72 72"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15 17C15 14.2386 17.2386 12 20 12C22.7614 12 25 14.2386 25 17C25 19.7615 22.7614 22 20 22C17.2386 22 15 19.7615 15 17ZM20 14C18.3431 14 17 15.3432 17 17C17 18.6569 18.3431 20 20 20C21.6569 20 23 18.6569 23 17C23 15.3432 21.6569 14 20 14Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M52 28C49.2386 28 47 30.2386 47 33C47 35.7615 49.2386 38 52 38C54.7614 38 57 35.7615 57 33C57 30.2386 54.7614 28 52 28ZM49 33C49 31.3432 50.3432 30 52 30C53.6569 30 55 31.3432 55 33C55 34.6569 53.6569 36 52 36C50.3432 36 49 34.6569 49 33Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15 49C15 46.2386 17.2386 44 20 44C22.7614 44 25 46.2386 25 49C25 51.7615 22.7614 54 20 54C17.2386 54 15 51.7615 15 49ZM20 46C18.3431 46 17 47.3432 17 49C17 50.6569 18.3431 52 20 52C21.6569 52 23 50.6569 23 49C23 47.3432 21.6569 46 20 46Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M8 20C8 13.3726 13.3726 8.00005 20 8.00005C26.6274 8.00005 32 13.3726 32 20C32 21.5742 31.6969 23.0776 31.1459 24.455L41.7499 29.7569C43.8571 26.3046 47.6594 24 52 24C58.6274 24 64 29.3726 64 36C64 42.6275 58.6274 48 52 48C47.6594 48 43.8571 45.6955 41.7499 42.2431L31.1459 47.5451C31.6969 48.9225 32 50.4259 32 52C32 58.6275 26.6274 64.0001 20 64.0001C13.3726 64.0001 8 58.6275 8 52C8 45.3726 13.3726 40 20 40C24.3406 40 28.1429 42.3046 30.2501 45.7569L40.8541 40.455C40.3031 39.0776 40 37.5742 40 36C40 34.4259 40.3031 32.9225 40.8541 31.5451L30.2501 26.2431C28.1429 29.6955 24.3406 32 20 32C13.3726 32 8 26.6275 8 20ZM20 10C14.4772 10 10 14.4772 10 20C10 22.4719 10.8969 24.7343 12.383 26.4796C13.1883 24.4417 15.1758 23 17.5 23H22.5C24.8242 23 26.8117 24.4417 27.617 26.4796C29.1031 24.7343 30 22.4719 30 20C30 14.4772 25.5229 10 20 10ZM25.968 28.0247C25.7361 26.3166 24.2718 25 22.5 25H17.5C15.7282 25 14.2639 26.3166 14.032 28.0247C15.6978 29.2656 17.7631 30 20 30C22.2369 30 24.3022 29.2656 25.968 28.0247ZM42 36C42 30.4772 46.4772 26 52 26C57.5229 26 62 30.4772 62 36C62 38.4719 61.1031 40.7343 59.617 42.4796C58.8117 40.4417 56.8242 39 54.5 39H49.5C47.1758 39 45.1883 40.4417 44.383 42.4796C42.8969 40.7343 42 38.4719 42 36ZM54.5 41C56.2718 41 57.7361 42.3166 57.968 44.0247C56.3022 45.2656 54.2369 46 52 46C49.7631 46 47.6978 45.2656 46.032 44.0247C46.2639 42.3166 47.7282 41 49.5 41H54.5ZM10 52C10 46.4772 14.4772 42 20 42C25.5229 42 30 46.4772 30 52C30 54.4719 29.1031 56.7344 27.617 58.4796C26.8117 56.4417 24.8242 55 22.5 55H17.5C15.1758 55 13.1883 56.4417 12.383 58.4796C10.8969 56.7344 10 54.4719 10 52ZM14.032 60.0247C15.6978 61.2656 17.7631 62.0001 20 62.0001C22.2369 62.0001 24.3022 61.2656 25.968 60.0247C25.7361 58.3166 24.2718 57.0001 22.5 57.0001H17.5C15.7282 57.0001 14.2639 58.3166 14.032 60.0247Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default CommentaryIcon
