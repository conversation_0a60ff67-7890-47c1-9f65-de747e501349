import React, { FC } from 'react'
import s from './Tooltip.module.scss'
import cn from 'classnames'
import unescape from 'lodash.unescape'

export type TooltipProps = {
    text: string
    color?: 'dark' | 'light'
    transparent?: boolean
}

export const Tooltip: FC<TooltipProps> = (props) => {
    const { text, color = 'dark', transparent = true } = props
    return (
        <div
            className={cn(
                s['tooltip'],
                s[`tooltip--${color}`],
                transparent ? s[`tooltip--transparent`] : '',
                'xs-copy'
            )}
            dangerouslySetInnerHTML={{ __html: unescape(text) }}
        >
            {text}
        </div>
    )
}
