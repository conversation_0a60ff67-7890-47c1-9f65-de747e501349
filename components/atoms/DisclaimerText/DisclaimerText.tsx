import React, { <PERSON> } from 'react'
import cn from 'classnames'
import s from './DisclaimerText.module.scss'
import unescape from 'lodash.unescape'

export type DisclaimerTextProps = {
    text: string
    className?: string
}

export const DisclaimerText: FC<DisclaimerTextProps> = (props) => {
    const { text, className } = props
    return (
        <div
            className={cn(
                s['disclaimer-text'],
                'xs-copy',
                'text-mid-grey-2',
                className
            )}
            dangerouslySetInnerHTML={{
                __html: unescape(text)
            }}
        />
    )
}
