import React, { FC } from 'react'
import cn from 'classnames'
import s from './DetailMediaText.module.scss'
import unescape from 'lodash.unescape'
import {
    HorizontalAlignmentEnum,
    VerticalAlignmentEnum
} from '@components/molecules/PrimaryText/PrimaryText'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import Image from '@corsairitshopify/corsair-image'
import Video from '@components/molecules/Video/Video'
export type DetailMediaTextInverseProps = {
    text?: string
    className?: string
    preHeadline?: string
    headline?: string
    textColor?: 'dark' | 'light'
    textAlignment?: HorizontalAlignmentEnum
    textVerticalAlignment?: VerticalAlignmentEnum
    icon?: string
    media?: ImageType | VideoType
    posterImage?: ImageType
}

export const DetailMediaTextInverse: FC<DetailMediaTextInverseProps> = (
    props
) => {
    const {
        text,
        className,
        preHeadline,
        headline,
        textColor = 'dark',
        textAlignment = HorizontalAlignmentEnum.LEFT,
        textVerticalAlignment = VerticalAlignmentEnum.BOTTOM,
        posterImage,
        media
    } = props
    const isVideo = media?.file?.contentType?.includes('video') ?? false
    const image = !isVideo ? (media as ImageType) : null
    return (
        <div
            className={cn(
                s['detail-media-text-inverse'],
                s[`detail-media-text--text-align-${textAlignment}`],
                {
                    [s[
                        `detail-media-text--text-align-${textVerticalAlignment}`
                    ]]: VerticalAlignmentEnum.CENTER
                },
                s[`detail-media-text--text-color-${textColor}`],
                className
            )}
        >
            <div
                className={cn(
                    'flex flex-col gap-8px self-stretch justify-center items-center'
                )}
            >
                {preHeadline && <h6>{preHeadline}</h6>}
                <div className="flex flex-col gap-6px md:gap-8px items-start">
                    {headline && <h4>{headline}</h4>}
                    {text && (
                        <div
                            className="text-small-copy"
                            dangerouslySetInnerHTML={{
                                __html: unescape(text.replace(/\n/g, `</br>`))
                            }}
                        />
                    )}
                </div>
            </div>
            {!!image && (
                <div className={s['detail-media-text__image']}>
                    <Image
                        src={image.file.url}
                        alt={image.description || headline || ''}
                        objectFit="cover"
                        // eslint-disable-next-line i18next/no-literal-string
                        objectPosition="center"
                        width={436}
                        height={440}
                    />
                </div>
            )}
            {isVideo && media && (
                <Video
                    // eslint-disable-next-line i18next/no-literal-string
                    videoClasses="object-cover h-full w-full"
                    video={media}
                    options={{
                        autoPlay: true,
                        preload: 'none',
                        muted: true,
                        loop: true
                    }}
                    fallbackImgUrl={posterImage?.file.url}
                />
            )}
        </div>
    )
}
