.detail-media-text {
    @apply p-16px;
    @screen md {
        max-width: calc(100vw - 150px);
        width: 100%;
    }

    &__button {
        color: var(--dark) !important;
        border-color: var(--dark) !important;
    }

    @screen md-max {
        max-width: 87vw;
        width: calc(90vh - 318px);
        max-height: calc(100vh - 300px);
    }

    &__body {
        max-width: 586px;
    }
    &--text-color-light {
        @apply text-white;
    }

    &--text-color-dark {
        @apply text-charcoal;
    }

    &__media {
        width: 70vh;
        @screen md-max {
            width: 100%;
            height: 100%;
        }
        video, img {
            border-radius: 12px
        }
    }

    &__video {
        position: relative;
        border-radius: 12px;
    }

    &__media-video {
        width: 100%;
        height: 100%;
        
    }

    &__embed-wrapper {
        @apply relative w-full h-full;
        @apply rounded-xxl overflow-hidden;
        @screen md-max {
            height: 100%;
        }
    }

    &__embed {
        height: 100%;
        width: 100%;
        :global {
            iframe {
                @apply w-full h-full;
            }
        }
    }
    &__embed__play {
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: 200;
    }
    &__myIframe {
        @apply w-full h-full;
    }
    &__play-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
