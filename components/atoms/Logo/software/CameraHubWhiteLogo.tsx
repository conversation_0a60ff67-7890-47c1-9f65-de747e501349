const CameraHubWhiteLogo = ({ ...props }) => {
    return (
        <svg
            width="99"
            height="100"
            viewBox="0 0 99 100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <rect
                x="19.0566"
                y="19.0566"
                width="60.8557"
                height="60.8557"
                rx="15.5"
                fill="white"
                stroke="#C4C4C4"
            />
            <g filter="url(#filter0_d_1134_2102)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M65.3987 34.2019C67.1595 34.2019 68.8058 34.8047 70.1153 35.8354C71.4248 36.866 72.3976 38.3246 72.8114 40.036C73.5704 43.1755 73.949 46.315 73.949 49.4544C73.949 52.5938 73.5704 55.7333 72.8114 58.8728C72.3976 60.5842 71.4248 62.0428 70.1153 63.0734C68.8058 64.1041 67.1595 64.7069 65.3987 64.7069H33.6913C31.9305 64.7069 30.2842 64.1041 28.9747 63.0734C27.6651 62.0428 26.6923 60.5842 26.2786 58.8728C25.5196 55.7333 25.141 52.5938 25.141 49.4544C25.141 46.315 25.5196 43.1755 26.2786 40.036C26.6923 38.3246 27.6651 36.866 28.9747 35.8354C30.2842 34.8047 31.9305 34.2019 33.6913 34.2019H65.3987Z"
                    fill="#204CFE"
                />
            </g>
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M33.6967 62.4115C31.2324 62.4115 29.0886 60.7246 28.5096 58.3299C27.7941 55.3714 27.4364 52.4129 27.4364 49.4544C27.4364 46.4959 27.7941 43.5373 28.5096 40.5788C29.0886 38.1842 31.2324 36.4973 33.6967 36.4973H65.3933C67.8575 36.4973 70.0013 38.1842 70.5804 40.5788C71.2958 43.5373 71.6535 46.4959 71.6535 49.4544C71.6535 52.4129 71.2958 55.3714 70.5804 58.3299C70.0013 60.7246 67.8575 62.4115 65.3933 62.4115H33.6967Z"
                fill="url(#paint0_linear_1134_2102)"
            />
            <mask
                id="mask0_1134_2102"
                style={{ maskType: 'alpha' }}
                maskUnits="userSpaceOnUse"
                x="27"
                y="36"
                width="45"
                height="27"
            >
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M33.6967 62.4115C31.2324 62.4115 29.0886 60.7246 28.5096 58.3299C27.7941 55.3714 27.4364 52.4129 27.4364 49.4544C27.4364 46.4959 27.7941 43.5373 28.5096 40.5788C29.0886 38.1842 31.2324 36.4973 33.6967 36.4973H65.3933C67.8575 36.4973 70.0013 38.1842 70.5804 40.5788C71.2958 43.5373 71.6535 46.4959 71.6535 49.4544C71.6535 52.4129 71.2958 55.3714 70.5804 58.3299C70.0013 60.7246 67.8575 62.4115 65.3933 62.4115H33.6967Z"
                    fill="white"
                />
            </mask>
            <g mask="url(#mask0_1134_2102)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M49.5752 60.2974C55.5636 60.2974 60.4181 55.4428 60.4181 49.4545C60.4181 43.4661 55.5636 38.6116 49.5752 38.6116C43.5869 38.6116 38.7324 43.4661 38.7324 49.4545C38.7324 55.4428 43.5869 60.2974 49.5752 60.2974ZM49.5752 60.9014C55.8972 60.9014 61.0221 55.7765 61.0221 49.4545C61.0221 43.1325 55.8972 38.0076 49.5752 38.0076C43.2533 38.0076 38.1283 43.1325 38.1283 49.4545C38.1283 55.7765 43.2533 60.9014 49.5752 60.9014Z"
                    fill="url(#paint1_linear_1134_2102)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M49.5449 62.5927C56.7843 62.5927 62.653 56.724 62.653 49.4846C62.653 42.2452 56.7843 36.3765 49.5449 36.3765C42.3055 36.3765 36.4368 42.2452 36.4368 49.4846C36.4368 56.724 42.3055 62.5927 49.5449 62.5927ZM49.5449 63.1968C57.1179 63.1968 63.2571 57.0576 63.2571 49.4846C63.2571 41.9116 57.1179 35.7725 49.5449 35.7725C41.9719 35.7725 35.8328 41.9116 35.8328 49.4846C35.8328 57.0576 41.9719 63.1968 49.5449 63.1968Z"
                    fill="url(#paint2_linear_1134_2102)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M49.5751 64.8881C58.313 64.8881 65.3713 57.978 65.3713 49.4846C65.3713 40.9912 58.313 34.0811 49.5751 34.0811C40.8373 34.0811 33.779 40.9912 33.779 49.4846C33.779 57.978 40.8373 64.8881 49.5751 64.8881ZM49.5751 65.4922C58.6327 65.4922 65.9753 58.3254 65.9753 49.4846C65.9753 40.6439 58.6327 33.4771 49.5751 33.4771C40.5176 33.4771 33.1749 40.6439 33.1749 49.4846C33.1749 58.3254 40.5176 65.4922 49.5751 65.4922Z"
                    fill="url(#paint3_linear_1134_2102)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M49.545 67.1836C59.7425 67.1836 67.9688 59.2376 67.9688 49.4846C67.9688 39.7317 59.7425 31.7857 49.545 31.7857C39.3474 31.7857 31.1211 39.7317 31.1211 49.4846C31.1211 59.2376 39.3474 67.1836 49.545 67.1836ZM49.545 67.7876C60.0538 67.7876 68.5728 59.5931 68.5728 49.4846C68.5728 39.3762 60.0538 31.1816 49.545 31.1816C39.0362 31.1816 30.5171 39.3762 30.5171 49.4846C30.5171 59.5931 39.0362 67.7876 49.545 67.7876Z"
                    fill="url(#paint4_linear_1134_2102)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M49.545 69.4186C61.1836 69.4186 70.5663 60.4522 70.5663 49.4544C70.5663 38.4567 61.1836 29.4903 49.545 29.4903C37.9064 29.4903 28.5237 38.4567 28.5237 49.4544C28.5237 60.4522 37.9064 69.4186 49.545 69.4186ZM49.545 70.0227C61.4883 70.0227 71.1703 60.814 71.1703 49.4544C71.1703 38.0949 61.4883 28.8862 49.545 28.8862C37.6017 28.8862 27.9197 38.0949 27.9197 49.4544C27.9197 60.814 37.6017 70.0227 49.545 70.0227Z"
                    fill="url(#paint5_linear_1134_2102)"
                />
                <path
                    d="M58.7267 49.4545C58.7267 54.5087 54.6294 58.606 49.5752 58.606C44.521 58.606 40.4237 54.5087 40.4237 49.4545C40.4237 44.4002 44.521 40.303 49.5752 40.303C54.6294 40.303 58.7267 44.4002 58.7267 49.4545Z"
                    fill="#204CFE"
                />
                <path
                    d="M54.1358 49.4544C54.1358 51.9732 52.094 54.0151 49.5752 54.0151C47.0564 54.0151 45.0145 51.9732 45.0145 49.4544C45.0145 46.9357 47.0564 44.8938 49.5752 44.8938C52.094 44.8938 54.1358 46.9357 54.1358 49.4544Z"
                    fill="white"
                />
            </g>
            <rect
                x="30.6379"
                y="48.3369"
                width="3.98679"
                height="2.29543"
                rx="0.8"
                fill="#204CFE"
            />
            <defs>
                <filter
                    id="filter0_d_1134_2102"
                    x="21.141"
                    y="34.2019"
                    width="56.808"
                    height="38.5049"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                >
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix
                        in="SourceAlpha"
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                        result="hardAlpha"
                    />
                    <feOffset dy="4" />
                    <feGaussianBlur stdDeviation="2" />
                    <feColorMatrix
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"
                    />
                    <feBlend
                        mode="normal"
                        in2="BackgroundImageFix"
                        result="effect1_dropShadow_1134_2102"
                    />
                    <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_dropShadow_1134_2102"
                        result="shape"
                    />
                </filter>
                <linearGradient
                    id="paint0_linear_1134_2102"
                    x1="111.285"
                    y1="49.6588"
                    x2="87.3933"
                    y2="-6.61791"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DFDFDF" />
                </linearGradient>
                <linearGradient
                    id="paint1_linear_1134_2102"
                    x1="64.8335"
                    y1="45.7575"
                    x2="46.106"
                    y2="34.1216"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint2_linear_1134_2102"
                    x1="67.8227"
                    y1="45.0561"
                    x2="45.3891"
                    y2="31.1175"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint3_linear_1134_2102"
                    x1="71.436"
                    y1="44.3147"
                    x2="44.9707"
                    y2="27.4679"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint4_linear_1134_2102"
                    x1="74.9083"
                    y1="43.5734"
                    x2="44.4631"
                    y2="23.9078"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint5_linear_1134_2102"
                    x1="78.3707"
                    y1="42.8116"
                    x2="44.0003"
                    y2="20.3588"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
            </defs>
        </svg>
    )
}

export default CameraHubWhiteLogo
