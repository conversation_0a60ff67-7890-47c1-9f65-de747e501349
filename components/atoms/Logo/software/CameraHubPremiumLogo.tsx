const CameraHubPremiumLogo = ({ ...props }) => {
    return (
        <svg
            width="96"
            height="100"
            viewBox="0 0 96 100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <ellipse cx="48" cy="50" rx="30" ry="31.25" fill="#204CFE" />
            <g filter="url(#filter0_d_1134_2146)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M63.0105 35.2488C64.6634 35.2488 66.2088 35.8382 67.438 36.846C68.6673 37.8538 69.5805 39.28 69.9689 40.9535C70.6814 44.0233 71.0367 47.0933 71.0367 50.163C71.0367 53.2328 70.6814 56.3027 69.9689 59.3725C69.5805 61.046 68.6673 62.4722 67.438 63.48C66.2088 64.4878 64.6634 65.0772 63.0105 65.0772H33.2464C31.5936 65.0772 30.0482 64.4878 28.8189 63.48C27.5897 62.4722 26.6765 61.046 26.2881 59.3725C25.5756 56.3027 25.2202 53.2328 25.2202 50.163C25.2202 47.0933 25.5756 44.0234 26.2881 40.9535C26.6765 39.28 27.5896 37.8538 28.8189 36.846C30.0482 35.8382 31.5936 35.2488 33.2464 35.2488H63.0105Z"
                    fill="white"
                />
            </g>
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M33.2513 62.8328C30.9381 62.8328 28.9257 61.1834 28.3821 58.8418C27.7105 55.9489 27.3748 53.056 27.3748 50.1631C27.3748 47.2702 27.7105 44.3773 28.3821 41.4844C28.9257 39.1429 30.9381 37.4934 33.2513 37.4934H63.0052C65.3184 37.4934 67.3308 39.1429 67.8744 41.4844C68.546 44.3773 68.8818 47.2702 68.8818 50.1631C68.8818 53.056 68.546 55.9489 67.8744 58.8418C67.3308 61.1834 65.3184 62.8328 63.0052 62.8328H33.2513Z"
                fill="url(#paint0_linear_1134_2146)"
            />
            <mask
                id="mask0_1134_2146"
                style={{ maskType: 'alpha' }}
                maskUnits="userSpaceOnUse"
                x="27"
                y="37"
                width="42"
                height="26"
            >
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M33.2513 62.8328C30.9381 62.8328 28.9257 61.1834 28.3821 58.8418C27.7105 55.9489 27.3748 53.056 27.3748 50.1631C27.3748 47.2702 27.7105 44.3773 28.3821 41.4844C28.9257 39.1429 30.9381 37.4934 33.2513 37.4934H63.0052C65.3184 37.4934 67.3308 39.1429 67.8744 41.4844C68.546 44.3773 68.8818 47.2702 68.8818 50.1631C68.8818 53.056 68.546 55.9489 67.8744 58.8418C67.3308 61.1834 65.3184 62.8328 63.0052 62.8328H33.2513Z"
                    fill="white"
                />
            </mask>
            <g mask="url(#mask0_1134_2146)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M48.1567 60.7654C53.778 60.7654 58.335 56.0186 58.335 50.163C58.335 44.3075 53.778 39.5606 48.1567 39.5606C42.5354 39.5606 37.9784 44.3075 37.9784 50.163C37.9784 56.0186 42.5354 60.7654 48.1567 60.7654ZM48.1567 61.3561C54.0912 61.3561 58.9021 56.3448 58.9021 50.163C58.9021 43.9813 54.0912 38.97 48.1567 38.97C42.2222 38.97 37.4114 43.9813 37.4114 50.163C37.4114 56.3448 42.2222 61.3561 48.1567 61.3561Z"
                    fill="url(#paint1_linear_1134_2146)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M48.1283 63.0099C54.924 63.0099 60.433 57.2713 60.433 50.1925C60.433 43.1136 54.924 37.3751 48.1283 37.3751C41.3326 37.3751 35.8236 43.1136 35.8236 50.1925C35.8236 57.2713 41.3326 63.0099 48.1283 63.0099ZM48.1283 63.6005C55.2372 63.6005 61 57.5975 61 50.1925C61 42.7874 55.2372 36.7844 48.1283 36.7844C41.0195 36.7844 35.2566 42.7874 35.2566 50.1925C35.2566 57.5975 41.0195 63.6005 48.1283 63.6005Z"
                    fill="url(#paint2_linear_1134_2146)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M48.1568 65.2547C56.3591 65.2547 62.9848 58.4979 62.9848 50.1928C62.9848 41.8878 56.3591 35.1309 48.1568 35.1309C39.9544 35.1309 33.3288 41.8878 33.3288 50.1928C33.3288 58.4979 39.9544 65.2547 48.1568 65.2547ZM48.1568 65.8454C56.6592 65.8454 63.5518 58.8375 63.5518 50.1928C63.5518 41.5482 56.6592 34.5403 48.1568 34.5403C39.6543 34.5403 32.7617 41.5482 32.7617 50.1928C32.7617 58.8375 39.6543 65.8454 48.1568 65.8454Z"
                    fill="url(#paint3_linear_1134_2146)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M48.1282 67.4992C57.7007 67.4992 65.4228 59.7294 65.4228 50.1927C65.4228 40.6561 57.7007 32.8863 48.1282 32.8863C38.5557 32.8863 30.8336 40.6561 30.8336 50.1927C30.8336 59.7294 38.5557 67.4992 48.1282 67.4992ZM48.1282 68.0898C57.993 68.0898 65.9899 60.077 65.9899 50.1927C65.9899 40.3085 57.993 32.2957 48.1282 32.2957C38.2635 32.2957 30.2666 40.3085 30.2666 50.1927C30.2666 60.077 38.2635 68.0898 48.1282 68.0898Z"
                    fill="url(#paint4_linear_1134_2146)"
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M48.1283 69.6847C59.0535 69.6847 67.8611 60.9172 67.8611 50.1633C67.8611 39.4095 59.0535 30.6419 48.1283 30.6419C37.203 30.6419 28.3954 39.4095 28.3954 50.1633C28.3954 60.9172 37.203 69.6847 48.1283 69.6847ZM48.1283 70.2754C59.3396 70.2754 68.4282 61.2709 68.4282 50.1633C68.4282 39.0557 59.3396 30.0513 48.1283 30.0513C36.9169 30.0513 27.8284 39.0557 27.8284 50.1633C27.8284 61.2709 36.9169 70.2754 48.1283 70.2754Z"
                    fill="url(#paint5_linear_1134_2146)"
                />
                <path
                    d="M56.7471 50.1631C56.7471 55.1053 52.901 59.1117 48.1565 59.1117C43.4121 59.1117 39.5659 55.1053 39.5659 50.1631C39.5659 45.221 43.4121 41.2146 48.1565 41.2146C52.901 41.2146 56.7471 45.221 56.7471 50.1631Z"
                    fill="#204CFE"
                />
                <path
                    d="M52.4377 50.1631C52.4377 52.626 50.521 54.6226 48.1566 54.6226C45.7922 54.6226 43.8755 52.626 43.8755 50.1631C43.8755 47.7002 45.7922 45.7036 48.1566 45.7036C50.521 45.7036 52.4377 47.7002 52.4377 50.1631Z"
                    fill="white"
                />
            </g>
            <rect
                x="30.3801"
                y="49.0703"
                width="3.74244"
                height="2.24452"
                rx="0.774194"
                fill="#204CFE"
            />
            <defs>
                <filter
                    id="filter0_d_1134_2146"
                    x="21.3492"
                    y="35.2488"
                    width="53.5585"
                    height="37.5703"
                    filterUnits="userSpaceOnUse"
                    colorInterpolationFilters="sRGB"
                >
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix
                        in="SourceAlpha"
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                        result="hardAlpha"
                    />
                    <feOffset dy="3.87097" />
                    <feGaussianBlur stdDeviation="1.93548" />
                    <feColorMatrix
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"
                    />
                    <feBlend
                        mode="normal"
                        in2="BackgroundImageFix"
                        result="effect1_dropShadow_1134_2146"
                    />
                    <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_dropShadow_1134_2146"
                        result="shape"
                    />
                </filter>
                <linearGradient
                    id="paint0_linear_1134_2146"
                    x1="106.084"
                    y1="50.363"
                    x2="82.061"
                    y2="-3.95991"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DFDFDF" />
                </linearGradient>
                <linearGradient
                    id="paint1_linear_1134_2146"
                    x1="62.4798"
                    y1="46.5481"
                    x2="44.5077"
                    y2="35.8281"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint2_linear_1134_2146"
                    x1="65.2858"
                    y1="45.8621"
                    x2="43.7571"
                    y2="33.0209"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint3_linear_1134_2146"
                    x1="68.6777"
                    y1="45.1376"
                    x2="43.2599"
                    y2="29.6047"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint4_linear_1134_2146"
                    x1="71.9371"
                    y1="44.4126"
                    x2="42.6826"
                    y2="26.272"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
                <linearGradient
                    id="paint5_linear_1134_2146"
                    x1="75.1872"
                    y1="43.6678"
                    x2="42.1486"
                    y2="22.9484"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FEF9F9" />
                    <stop offset="1" stopColor="#DCDCDC" />
                </linearGradient>
            </defs>
        </svg>
    )
}

export default CameraHubPremiumLogo
