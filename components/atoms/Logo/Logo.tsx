import React, { FC, JSXElementConstructor } from 'react'
type ComponentType = string | JSXElementConstructor<any>
import ElgatoLogo from '@components/atoms/Logo/ElgatoLogo'
// Partner Logos
import * as partners from '@components/atoms/Logo/partners'
import * as platform from '@components/atoms/Logo/platform'
import * as software from '@components/atoms/Logo/software'
import * as additional from '@components/atoms/Logo/additionals'
import ElgatoMarkLogo from '@components/atoms/Logo/ElgatoMarkLogo'
export type LogoComponentsMapping = {
    [key: string]: ComponentType
}

const components = {
    ...partners,
    ...platform,
    ...software,
    ...additional,
    elgato: ElgatoLogo,
    elgatoMark: ElgatoMarkLogo
} as LogoComponentsMapping

export type LogoProps = {
    name: string
    className?: string
}

export const Logo: FC<LogoProps> = (props) => {
    const { name, className, ...rest } = props
    const component = components[name]
    if (!component) {
        return <div data-title="Logo not found">{name}</div>
    }
    const LogoComponent = component
    return <LogoComponent className={className} {...rest} />
}
