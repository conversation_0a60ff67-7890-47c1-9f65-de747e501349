const ScufLogo = ({ ...props }) => {
    return (
        <svg
            width="170"
            height="60"
            viewBox="0 0 170 60"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M25.3403 33.2657C25.3403 34.3713 25.5029 35.3236 25.8312 36.1193C26.1579 36.9151 26.5772 37.5934 27.086 38.1577C27.5963 38.7202 28.1701 39.167 28.8056 39.4964C29.4411 39.8242 30.0799 40.0884 30.717 40.2808C31.3541 40.4765 31.9555 40.602 32.5179 40.6607C33.0819 40.7194 33.5451 40.7488 33.9092 40.7488C35.0014 40.7488 36.0839 40.6265 37.1583 40.3851C38.2326 40.1422 39.1965 39.7443 40.0514 39.1899C40.9063 38.6371 41.5987 37.9049 42.1253 36.9917C42.6536 36.0802 42.9153 34.9453 42.9153 33.5853C42.9153 32.6346 42.7625 31.8242 42.4553 31.154C42.1448 30.4854 41.7304 29.9228 41.2119 29.4663C40.6934 29.0113 40.1066 28.6363 39.4516 28.346C38.7966 28.0541 38.1221 27.803 37.4297 27.5894C36.7405 27.3953 36.0611 27.216 35.3964 27.048C34.7332 26.8849 34.1384 26.7088 33.6101 26.5262C33.0819 26.3403 32.6512 26.1267 32.3131 25.8853C31.9766 25.6424 31.7995 25.3374 31.7816 24.9689C31.7816 24.677 31.8531 24.4389 31.9994 24.2547C32.1457 24.0704 32.3277 23.9302 32.5455 23.8323C32.7633 23.7361 33.0006 23.6676 33.2558 23.6285C33.5093 23.5894 33.7369 23.5698 33.9368 23.5698C34.2456 23.5698 34.5561 23.6089 34.8665 23.6872C35.1753 23.7655 35.4565 23.9008 35.7117 24.0949C35.9669 24.2694 36.1635 24.4928 36.2985 24.7651C36.435 25.0358 36.4756 25.3652 36.4204 25.7549H42.0993C42.0619 24.4928 41.8165 23.4296 41.3598 22.5653C40.9063 21.7027 40.3066 21.0032 39.5605 20.4683C38.8145 19.9367 37.9579 19.5519 36.9957 19.3203C36.0303 19.0871 35.0193 18.9714 33.9661 18.9714C32.9616 18.9714 31.9913 19.1002 31.0437 19.3627C30.0977 19.6236 29.2607 20.0378 28.5341 20.6004C27.806 21.1646 27.2192 21.8772 26.7723 22.7414C26.3253 23.604 26.1042 24.6379 26.1042 25.8413C26.1042 26.8882 26.3042 27.7492 26.704 28.4177C27.1055 29.0863 27.6191 29.6407 28.2465 30.0778C28.8738 30.5148 29.5841 30.8637 30.3757 31.1263C31.1672 31.3872 31.9441 31.635 32.7097 31.8682C33.5451 32.121 34.2375 32.3395 34.7836 32.5237C35.3297 32.708 35.7572 32.8809 36.0676 33.0472C36.3765 33.2135 36.5943 33.388 36.7227 33.5706C36.8494 33.7565 36.9128 33.9815 36.9128 34.2539C36.9128 34.6436 36.8299 34.9583 36.6674 35.2013C36.5032 35.4442 36.2936 35.6383 36.0384 35.7834C35.7848 35.9286 35.5118 36.0264 35.2208 36.0753C34.9267 36.1242 34.665 36.1487 34.4293 36.1487C33.8653 36.1487 33.2981 36.0508 32.7373 35.8568C32.1733 35.6628 31.7816 35.2567 31.5638 34.6338C31.4175 34.2261 31.3444 33.7696 31.3444 33.2657H25.3403ZM36.4171 8C47.4874 9.14635 56.118 18.534 56.118 29.9421C56.118 41.3485 47.4874 50.7346 36.4171 51.881V47.2336C44.9405 46.1084 51.5199 38.7982 51.5199 29.9421C51.5199 21.0828 44.9405 13.7742 36.4171 12.6457V8ZM12.1409 29.9421C12.1409 18.534 20.7699 9.14635 31.8401 8V12.6457C23.3168 13.7742 16.7374 21.0828 16.7374 29.9421C16.7374 38.7982 23.3168 46.1084 31.8401 47.2336V51.881C28.0807 51.4929 24.6024 50.1492 21.6492 48.1027L12.2644 52L16.1392 42.6205C13.6216 39.033 12.1409 34.6612 12.1409 29.9421ZM138.393 43.6821H145.94V32.8105H155.713V26.6597H145.94V22.8994H157.324V16.318H138.393V43.6821ZM135.949 16.318H128.405V31.1701C128.405 31.8288 128.386 32.5121 128.352 33.2198C128.319 33.9259 128.188 34.5732 127.958 35.157C127.729 35.744 127.375 36.2235 126.895 36.6034C126.416 36.9801 125.73 37.1692 124.836 37.1692C123.923 37.1692 123.214 36.9801 122.712 36.6034C122.208 36.2235 121.842 35.744 121.613 35.157C121.382 34.5732 121.254 33.9259 121.22 33.2198C121.186 32.5121 121.168 31.8288 121.168 31.1701V16.318H113.623V32.9621C113.623 36.5969 114.541 39.2907 116.384 41.0453C118.224 42.8032 121.031 43.6821 124.804 43.6821C128.576 43.6821 131.376 42.8032 133.205 41.0453C135.035 39.2907 135.949 36.5969 135.949 32.9621V16.318ZM111.074 26.6711C110.754 23.3788 109.675 20.835 107.833 19.0445C105.993 17.2508 103.383 16.3409 100.001 16.318C98.1008 16.318 96.3909 16.6474 94.8728 17.3062C93.3499 17.9634 92.048 18.8912 90.9606 20.0865C89.8749 21.2801 89.041 22.72 88.4575 24.4012C87.8757 26.0857 87.5831 27.9511 87.5831 29.9992C87.5831 31.9267 87.8562 33.7253 88.4055 35.3967C88.9549 37.0665 89.7611 38.5113 90.8241 39.731C91.8887 40.9507 93.1841 41.9144 94.7184 42.6205C96.2479 43.3282 98.0097 43.6821 100.001 43.6821C101.784 43.6821 103.37 43.3641 104.765 42.7314C106.161 42.0971 107.331 41.2589 108.28 40.2235C109.23 39.188 109.948 38.0237 110.442 36.7306C110.933 35.4391 111.178 34.133 111.178 32.817H103.77C103.658 33.4285 103.502 33.9992 103.307 34.5374C103.115 35.0706 102.863 35.5369 102.553 35.9267C102.245 36.3148 101.867 36.6213 101.423 36.8382C100.976 37.06 100.432 37.1692 99.7944 37.1692C98.9004 37.1692 98.156 36.9621 97.5644 36.548C96.9695 36.1338 96.4965 35.5859 96.1406 34.9026C95.7863 34.2194 95.5294 33.4513 95.3685 32.5969C95.2093 31.7424 95.1296 30.8765 95.1296 29.9992C95.1296 29.1219 95.2093 28.2561 95.3685 27.4032C95.5294 26.5488 95.7863 25.7807 96.1406 25.0975C96.4965 24.4159 96.9695 23.868 97.5644 23.4505C98.156 23.038 98.9004 22.8309 99.7944 22.8309C100.57 22.8309 101.204 22.989 101.698 23.3054C102.189 23.6217 102.583 23.987 102.879 24.4012C103.177 24.8186 103.383 25.2394 103.495 25.665C103.612 26.0906 103.69 26.4281 103.737 26.6711H111.074ZM70.5966 34.2797C70.5966 34.914 70.6876 35.4864 70.8713 35.9984C71.146 36.7811 71.6369 37.2915 72.3455 37.5361C73.0525 37.7807 73.7644 37.9014 74.4715 37.9014C74.7689 37.9014 75.1005 37.8704 75.4662 37.8101C75.8303 37.7481 76.1765 37.6258 76.495 37.4448C76.8152 37.2606 77.0785 37.0176 77.285 36.7126C77.4897 36.4077 77.5905 36.0131 77.5905 35.5239C77.5905 35.1815 77.5125 34.8961 77.3532 34.6629C77.1923 34.433 76.9193 34.2129 76.5308 34.0058C76.1407 33.797 75.6043 33.5785 74.9184 33.347C74.2325 33.1171 73.363 32.8398 72.3114 32.5235C71.3524 32.23 70.374 31.9202 69.3809 31.5908C68.3845 31.2614 67.4922 30.8227 66.7039 30.2748C65.9156 29.7253 65.2704 29.029 64.7665 28.1892C64.2643 27.3478 64.0123 26.2667 64.0123 24.9507C64.0123 23.4391 64.2919 22.1395 64.8526 21.0551C65.4118 19.9707 66.1497 19.0738 67.0647 18.3661C67.9782 17.6584 69.0298 17.1399 70.2196 16.8105C71.4077 16.4811 72.6316 16.318 73.8896 16.318C75.2143 16.318 76.4837 16.4648 77.6962 16.7567C78.9071 17.0486 79.9814 17.5329 80.9192 18.1998C81.8554 18.8733 82.6112 19.7506 83.1833 20.835C83.7539 21.921 84.0643 23.2565 84.1098 24.8415H76.9761C77.046 24.3539 76.9924 23.9381 76.8217 23.5973C76.6511 23.2565 76.404 22.9744 76.0854 22.7559C75.762 22.5129 75.4093 22.3417 75.0208 22.2438C74.6324 22.1476 74.2439 22.0971 73.8538 22.0971C73.6019 22.0971 73.3175 22.1215 72.9973 22.1705C72.6771 22.221 72.3813 22.3058 72.105 22.4281C71.8319 22.5488 71.6027 22.7249 71.4207 22.9564C71.2354 23.1896 71.146 23.488 71.146 23.8549C71.1688 24.318 71.3914 24.7012 71.814 25.0078C72.2366 25.3111 72.7795 25.5785 73.4426 25.8117C74.1058 26.0433 74.8551 26.2618 75.6905 26.4689C76.5243 26.6776 77.376 26.9026 78.2455 27.1456C79.1135 27.4163 79.9587 27.731 80.7827 28.0979C81.6051 28.4632 82.343 28.9328 82.9948 29.5068C83.6466 30.0791 84.1667 30.7852 84.5568 31.6266C84.9436 32.4681 85.1386 33.4872 85.1386 34.6825C85.1386 36.3898 84.8071 37.8166 84.1439 38.9613C83.4808 40.1093 82.6112 41.029 81.5385 41.7237C80.4625 42.4199 79.2516 42.9206 77.901 43.2239C76.5519 43.5288 75.1915 43.6804 73.8197 43.6804C73.363 43.6804 72.7795 43.6446 72.0708 43.5728C71.3638 43.4978 70.608 43.3396 69.8067 43.095C69.007 42.8521 68.2074 42.5227 67.4077 42.1085C66.608 41.6943 65.8864 41.1334 65.246 40.4273C64.6072 39.7179 64.0806 38.8667 63.6694 37.8655C63.2582 36.8659 63.0518 35.669 63.0518 34.2797H70.5966Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default ScufLogo
