import { useTranslation } from 'next-i18next'

const <PERSON><PERSON><PERSON><PERSON><PERSON> = ({ ...props }) => {
    const { t } = useTranslation(['common'])

    return (
        // eslint-disable-next-line prettier/prettier
        <svg
            width="167"
            height="33"
            viewBox="0 0 167 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            xmlnsXlink="http://www.w3.org/1999/xlink"
            {...props}
        >
            <path
                d="M0 8C0 3.58172 3.58172 0 8 0H159C163.418 0 167 3.58172 167 8V25C167 29.4183 163.418 33 159 33H8C3.58172 33 0 29.4183 0 25V8Z"
                fill="url(#pattern0_8821_55)"
            />
            <defs>
                <pattern
                    id="pattern0_8821_55"
                    patternContentUnits="objectBoundingBox"
                    width="1"
                    height="1"
                >
                    <use
                        xlinkHref="#image0_8821_55"
                        transform="matrix(0.00294118 0 0 0.0148841 0 -0.0135027)"
                    />
                </pattern>
                <image
                    id="image0_8821_55"
                    width="340"
                    height="69"
                    xlinkHref="data:image/png;base64,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"
                />
            </defs>
        </svg>
    )
}
export default BaloloLogo
