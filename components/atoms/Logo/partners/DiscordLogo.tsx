const DiscordLogo = ({ ...props }) => {
    return (
        <svg
            width="253"
            height="48"
            viewBox="0 0 154 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M51.4763 6.9231H59.6924C61.6727 6.9231 63.348 7.23208 64.7183 7.85004C65.98 8.38046 67.0513 9.28075 67.7911 10.4322C68.4836 11.5698 68.8385 12.8807 68.8143 14.2122C68.8295 15.5499 68.4608 16.8638 67.7519 17.9982C66.9612 19.1966 65.8304 20.1315 64.5046 20.6827C63.052 21.3448 61.2513 21.6749 59.1025 21.6729H51.4763V6.9231ZM59.0182 17.92C60.3525 17.92 61.3777 17.5869 62.094 16.9208C62.4572 16.5686 62.7409 16.1429 62.9262 15.6721C63.1115 15.2013 63.1941 14.6964 63.1684 14.1912C63.1908 13.7221 63.1174 13.2535 62.9529 12.8137C62.7884 12.3739 62.5361 11.9722 62.2114 11.633C61.5693 10.995 60.6033 10.675 59.3132 10.673H56.743V17.92H59.0182Z"
                fill="currentColor"
            />
            <path
                d="M81.1165 21.6487C80.0326 21.3771 78.9957 20.9439 78.0408 20.3637V16.8666C78.8745 17.473 79.8132 17.9199 80.8096 18.1848C81.8995 18.5173 83.0317 18.6906 84.1712 18.6994C84.5645 18.7204 84.9574 18.6525 85.3209 18.5008C85.5797 18.3683 85.7091 18.1998 85.7091 18.0253C85.7119 17.9277 85.6946 17.8307 85.6584 17.7401C85.6221 17.6495 85.5677 17.5673 85.4984 17.4986C85.258 17.3137 84.9759 17.1907 84.6768 17.1404L82.1488 16.5716C80.7002 16.2346 79.6719 15.7681 79.064 15.1722C78.7588 14.8644 78.521 14.4965 78.3656 14.0919C78.2101 13.6872 78.1405 13.2547 78.1612 12.8217C78.151 12.0371 78.4368 11.2774 78.9617 10.694C79.584 10.0357 80.3687 9.55305 81.2369 9.29456C82.3542 8.94339 83.521 8.77569 84.6919 8.79798C85.7849 8.78772 86.8749 8.91512 87.9362 9.17719C88.7933 9.38028 89.6165 9.70671 90.3799 10.1463V13.4568C89.664 13.0414 88.8944 12.7265 88.0927 12.5208C87.224 12.2879 86.3285 12.1705 85.4292 12.1717C84.109 12.1717 83.4489 12.3964 83.4489 12.8458C83.4467 12.9459 83.4741 13.0444 83.5276 13.129C83.5812 13.2136 83.6585 13.2805 83.7499 13.3213C84.1106 13.4747 84.4885 13.5838 84.8755 13.6464L86.9821 14.0256C88.3505 14.2663 89.3707 14.6877 90.0428 15.2896C90.715 15.8915 91.052 16.7723 91.0541 17.932C91.0648 18.5418 90.9157 19.1438 90.6216 19.6781C90.3275 20.2124 89.8987 20.6604 89.3777 20.9776C88.2682 21.722 86.6842 22.0932 84.6257 22.0912C83.4419 22.093 82.2628 21.9443 81.1165 21.6487Z"
                fill="currentColor"
            />
            <path
                d="M96.0326 21.2064C94.9063 20.6888 93.957 19.8516 93.3029 18.7987C92.6928 17.7662 92.3803 16.585 92.4001 15.3859C92.3819 14.1875 92.7112 13.0094 93.3481 11.9941C94.0269 10.9578 94.9933 10.142 96.1289 9.64668C97.5095 9.04508 99.0053 8.75334 100.511 8.79197C102.617 8.79197 104.366 9.23437 105.756 10.1192V13.9804C105.227 13.6248 104.65 13.3481 104.041 13.1588C103.36 12.9437 102.649 12.8371 101.934 12.8428C100.628 12.8428 99.6059 13.0816 98.8676 13.5591C98.5357 13.7443 98.2588 14.0142 98.065 14.3412C97.8712 14.6682 97.7676 15.0407 97.7645 15.4208C97.7615 15.8009 97.8591 16.175 98.0476 16.5051C98.2361 16.8351 98.5086 17.1094 98.8375 17.3C99.5538 17.7835 100.593 18.0253 101.955 18.0253C102.659 18.0269 103.358 17.9255 104.032 17.7243C104.647 17.5481 105.237 17.2964 105.79 16.9749V20.7068C104.161 21.6562 102.302 22.1374 100.417 22.0972C98.9071 22.1384 97.4072 21.8337 96.0326 21.2064Z"
                fill="currentColor"
            />
            <path
                d="M111.002 21.2063C109.863 20.6862 108.897 19.8507 108.218 18.7986C107.578 17.7686 107.246 16.5772 107.261 15.3647C107.241 14.1667 107.574 12.9891 108.218 11.979C108.901 10.9537 109.862 10.1453 110.99 9.6496C113.759 8.52598 116.858 8.52598 119.627 9.6496C120.75 10.1415 121.708 10.9459 122.387 11.967C123.027 12.9808 123.357 14.1598 123.335 15.3587C123.35 16.5699 123.021 17.7606 122.387 18.7926C121.716 19.8454 120.754 20.6815 119.618 21.2002C118.257 21.784 116.791 22.0851 115.31 22.0851C113.829 22.0851 112.363 21.784 111.002 21.2002V21.2063ZM117.403 17.4684C117.664 17.1997 117.866 16.8806 117.999 16.5308C118.131 16.1809 118.191 15.8077 118.174 15.434C118.192 15.0637 118.133 14.6938 118 14.3476C117.868 14.0015 117.664 13.6868 117.403 13.4236C117.12 13.164 116.788 12.9639 116.426 12.8351C116.064 12.7064 115.68 12.6517 115.297 12.6742C114.913 12.654 114.53 12.7099 114.168 12.8385C113.807 12.9671 113.474 13.1659 113.19 13.4236C112.93 13.6874 112.728 14.0023 112.595 14.3483C112.463 14.6943 112.404 15.064 112.423 15.434C112.406 15.8074 112.465 16.1804 112.597 16.5301C112.729 16.8799 112.931 17.1991 113.19 17.4684C113.472 17.7312 113.803 17.9347 114.165 18.0665C114.527 18.1984 114.912 18.256 115.297 18.2358C115.682 18.2582 116.067 18.2017 116.429 18.0697C116.792 17.9378 117.123 17.7331 117.403 17.4684Z"
                fill="currentColor"
            />
            <path
                d="M135.996 9.57148V14.1279C135.367 13.7526 134.642 13.5696 133.91 13.6013C132.788 13.6013 131.921 13.9444 131.319 14.6245C130.717 15.3047 130.416 16.361 130.416 17.7936V21.6699H125.255V9.34576H130.311V13.2582C130.592 11.8256 131.045 10.7693 131.671 10.0891C131.975 9.75466 132.347 9.48998 132.763 9.31333C133.178 9.13668 133.627 9.05226 134.079 9.06587C134.753 9.04748 135.418 9.22296 135.996 9.57148Z"
                fill="currentColor"
            />
            <path
                d="M153.144 6.50171V21.6698H147.983V18.901C147.594 19.8868 146.894 20.7191 145.991 21.2726C144.993 21.841 143.858 22.1242 142.71 22.0912C141.63 22.1163 140.567 21.8199 139.656 21.2395C138.773 20.6662 138.07 19.8557 137.627 18.901C137.144 17.8473 136.903 16.6985 136.923 15.5394C136.889 14.3376 137.145 13.1453 137.669 12.0634C138.151 11.0724 138.903 10.2378 139.839 9.65571C140.801 9.07298 141.908 8.77359 143.032 8.79197C145.488 8.79197 147.138 9.85936 147.983 11.9941V6.50171H153.144ZM147.225 17.3963C147.488 17.1374 147.694 16.8265 147.83 16.4835C147.966 16.1404 148.03 15.7728 148.016 15.404C148.027 15.0462 147.963 14.69 147.826 14.3591C147.69 14.0281 147.485 13.7298 147.225 13.4839C146.634 13.0031 145.896 12.7406 145.134 12.7406C144.373 12.7406 143.635 13.0031 143.044 13.4839C142.785 13.7355 142.582 14.0389 142.447 14.3744C142.313 14.7099 142.251 15.0699 142.265 15.431C142.252 15.7955 142.315 16.1587 142.451 16.4972C142.587 16.8357 142.792 17.142 143.053 17.3963C143.33 17.6533 143.655 17.8525 144.009 17.9822C144.364 18.112 144.741 18.1696 145.118 18.1517C145.502 18.1729 145.886 18.1169 146.248 17.9872C146.61 17.8575 146.942 17.6565 147.225 17.3963Z"
                fill="currentColor"
            />
            <path
                d="M32.4707 3.27245C29.9567 2.12151 27.3028 1.30499 24.5766 0.84375C24.2036 1.51063 23.866 2.19676 23.5654 2.89927C20.6615 2.46169 17.7085 2.46169 14.8046 2.89927C14.5038 2.19683 14.1663 1.51071 13.7934 0.84375C11.0655 1.30888 8.40982 2.12734 5.89335 3.27847C0.897496 10.6699 -0.4568 17.8778 0.220348 24.9833C3.14604 27.145 6.42073 28.7889 9.90206 29.8438C10.686 28.7894 11.3796 27.6709 11.9756 26.5001C10.8436 26.0773 9.75088 25.5557 8.71028 24.9412C8.98415 24.7426 9.252 24.5379 9.51082 24.3393C12.5387 25.7632 15.8435 26.5015 19.1895 26.5015C22.5355 26.5015 25.8403 25.7632 28.8682 24.3393C29.1301 24.553 29.3979 24.7576 29.6688 24.9412C28.6262 25.5567 27.5315 26.0793 26.3974 26.5032C26.9927 27.6734 27.6864 28.791 28.471 29.8438C31.9553 28.7932 35.2325 27.15 38.1587 24.9863C38.9532 16.7462 36.8014 9.60454 32.4707 3.27245ZM12.8334 20.6135C10.9464 20.6135 9.38743 18.901 9.38743 16.7944C9.38743 14.6877 10.8922 12.9602 12.8273 12.9602C14.7625 12.9602 16.3094 14.6877 16.2763 16.7944C16.2432 18.901 14.7565 20.6135 12.8334 20.6135ZM25.5457 20.6135C23.6557 20.6135 22.1028 18.901 22.1028 16.7944C22.1028 14.6877 23.6075 12.9602 25.5457 12.9602C27.4838 12.9602 29.0187 14.6877 28.9856 16.7944C28.9525 18.901 27.4688 20.6135 25.5457 20.6135Z"
                fill="currentColor"
            />
            <path
                d="M73.1657 10.6578C74.5868 10.6578 75.7389 9.623 75.7389 8.34649C75.7389 7.06998 74.5868 6.03516 73.1657 6.03516C71.7446 6.03516 70.5925 7.06998 70.5925 8.34649C70.5925 9.623 71.7446 10.6578 73.1657 10.6578Z"
                fill="currentColor"
            />
            <path
                d="M70.5896 12.25C71.4047 12.5926 72.2801 12.7691 73.1643 12.7691C74.0485 12.7691 74.9238 12.5926 75.7389 12.25V21.7361H70.5896V12.25Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default DiscordLogo
