import { useTranslation } from 'next-i18next'

const DropLogo = ({ ...props }) => {
    return (
        <svg
            width="62"
            height="28"
            viewBox="0 0 62 28"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <path
                d="M24.196 13.8148C25.652 13.8148 26.1093 13.2778 26.1093 12.4444C26.1093 11.5926 25.68 11.0648 24.196 11.0648H22.0307V13.8056H24.196V13.8148ZM27.7147 16.3704L30.5053 20.037H25.876L23.608 16.9815H22.04V20.037H18.12V7.95371H25.1387C28.004 7.95371 30.0107 10.1296 30.0107 12.5556C30.0013 14.3611 28.9 15.8056 27.7147 16.3704ZM41.3413 13.9907C41.3413 12.2593 40.1093 11.1481 38.2053 11.1481C36.3013 11.1481 35.0693 12.2685 35.0693 13.9907C35.0693 15.7222 36.3013 16.8333 38.2053 16.8333C40.0813 16.8426 41.3413 15.6944 41.3413 13.9907ZM38.252 7.77778C42.368 7.77778 45.3547 10.3982 45.3547 14C45.3547 17.6019 42.368 20.2222 38.252 20.2222C34.136 20.2222 31.1493 17.6019 31.1493 14C31.1493 10.3889 34.136 7.77778 38.252 7.77778ZM53.176 13.8704C54.6507 13.8704 55.108 13.3241 55.108 12.4907C55.108 11.6389 54.6693 11.1019 53.176 11.1019H50.992V13.8704H53.176ZM54.1187 8C56.9933 8 59 10.1759 59 12.6019C59 14.4074 57.8987 15.8519 56.704 16.4167C55.6027 17.0278 52.5507 17.0278 52.5507 17.0278H50.992V20.0833H47.0813V8H54.1187ZM16.5053 14.0463C16.5053 19.5185 10.3547 20.0833 10.3547 20.0833L6.93867 16.7222L9.22533 16.713C12.6413 16.713 12.52 14.0463 12.52 14.0463C12.52 14.0463 12.6413 11.3796 9.22533 11.3796L6.93867 11.3704V16.7222L3 12.8426V8.00926H9.22533C9.22533 8 16.5053 8 16.5053 14.0463Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default DropLogo
