const ZoomLogo = ({ ...props }) => {
    return (
        <svg
            width="206"
            height="60"
            viewBox="0 0 206 60"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_7896_19000)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M147.542 23.596C148.085 24.5351 148.262 25.6035 148.321 26.8047L148.398 28.4057V39.601L148.477 41.2037C148.634 43.8211 150.559 45.7564 153.189 45.921L154.779 46V28.4057L154.858 26.8047C154.923 25.617 155.099 24.5284 155.65 23.5825C156.212 22.6124 157.018 21.8075 157.988 21.2487C158.958 20.6899 160.058 20.3968 161.176 20.3988C162.295 20.4008 163.393 20.698 164.361 21.2603C165.329 21.8227 166.132 22.6305 166.691 23.6027C167.233 24.5418 167.403 25.6304 167.468 26.8047L167.547 28.4007V39.601L167.625 41.2037C167.79 43.8345 169.699 45.7698 172.338 45.921L173.928 46V26.8047C173.928 23.4105 172.583 20.1551 170.191 17.7546C167.798 15.354 164.552 14.0047 161.167 14.0034C159.356 14.0014 157.564 14.3872 155.913 15.135C154.262 15.8827 152.789 16.9752 151.593 18.3394C150.396 16.9757 148.923 15.8835 147.272 15.1356C145.621 14.3876 143.83 14.0011 142.019 14.0017C139.368 14.0017 136.909 14.8081 134.872 16.2008C133.629 14.8097 130.848 14.0017 129.251 14.0017V46L130.848 45.921C133.518 45.7446 135.448 43.8614 135.552 41.2037L135.638 39.601V28.4057L135.716 26.8047C135.783 25.5968 135.951 24.5351 136.495 23.5893C137.057 22.6197 137.863 21.815 138.833 21.2555C139.802 20.696 140.9 20.4013 142.019 20.4007C143.138 20.4009 144.237 20.6963 145.207 21.2571C146.176 21.8179 146.982 22.6245 147.542 23.596ZM38.3089 45.9227L39.9054 46H63.8368L63.758 44.404C63.5419 41.7732 61.684 39.8513 59.0522 39.68L57.4556 39.601H43.0985L62.2402 20.399L62.1615 18.8047C62.0375 16.147 60.1009 14.2268 57.4556 14.0806L55.8591 14.0084L31.9277 14.0017L32.0065 15.6027C32.2159 18.2083 34.1006 20.1705 36.7107 20.3217L38.3089 20.4007H52.666L33.5243 39.6027L33.603 41.2037C33.7605 43.8412 35.6586 45.7514 38.3089 45.921V45.9227ZM122.988 18.6854C124.469 20.1711 125.645 21.9349 126.447 23.8761C127.249 25.8173 127.661 27.8979 127.661 29.9992C127.661 32.1004 127.249 34.181 126.447 36.1222C125.645 38.0635 124.469 39.8273 122.988 41.3129C119.994 44.3126 115.935 45.9976 111.703 45.9976C107.471 45.9976 103.412 44.3126 100.418 41.3129C94.1878 35.0651 94.1878 24.9332 100.418 18.6854C101.899 17.2003 103.656 16.0221 105.591 15.2182C107.525 14.4143 109.599 14.0003 111.693 14C113.79 13.9985 115.867 14.4118 117.805 15.216C119.744 16.0203 121.504 17.1998 122.988 18.6871V18.6854ZM118.473 23.2163C120.267 25.0173 121.275 27.4591 121.275 30.005C121.275 32.551 120.267 34.9928 118.473 36.7938C116.677 38.5934 114.242 39.6043 111.703 39.6043C109.164 39.6043 106.729 38.5934 104.933 36.7938C103.138 34.9928 102.13 32.551 102.13 30.005C102.13 27.4591 103.138 25.0173 104.933 23.2163C106.729 21.4167 109.164 20.4058 111.703 20.4058C114.242 20.4058 116.677 21.4167 118.473 23.2163ZM78.2073 14C80.3015 14.0006 82.375 14.4148 84.3095 15.219C86.2441 16.0232 88.0016 17.2017 89.4819 18.6871C95.714 24.9332 95.714 35.0668 89.4819 41.3129C86.4882 44.3126 82.4293 45.9976 78.1973 45.9976C73.9652 45.9976 69.9063 44.3126 66.9126 41.3129C60.6822 35.0651 60.6822 24.9332 66.9126 18.6854C68.393 17.2003 70.1507 16.0221 72.0852 15.2182C74.0197 14.4143 76.0932 14.0003 78.1872 14H78.2073ZM84.9671 23.2129C86.7622 25.014 87.7705 27.4561 87.7705 30.0025C87.7705 32.5489 86.7622 34.9911 84.9671 36.7921C83.1711 38.5917 80.7361 39.6026 78.1973 39.6026C75.6584 39.6026 73.2234 38.5917 71.4275 36.7921C69.6329 34.9911 68.6248 32.5493 68.6248 30.0034C68.6248 27.4574 69.6329 25.0156 71.4275 23.2146C73.2234 21.415 75.6584 20.4041 78.1973 20.4041C80.7361 20.4041 83.1711 21.415 84.9671 23.2146V23.2129Z"
                    fill="#2D8CFF"
                />
            </g>
            <defs>
                <clipPath id="clip0_7896_19000">
                    <rect
                        width="142"
                        height="32"
                        fill="white"
                        transform="translate(31.9277 14)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default ZoomLogo
