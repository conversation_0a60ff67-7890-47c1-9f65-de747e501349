.toggle-switch {
    @apply relative inline-flex flex-row-reverse items-center gap-4;

    &__label + &__switch {
        @apply font-univers55Roman;
    }

    &__switch {
        @apply relative inline-block;
        width: 61px;
        height: 32px;

        @screen md {
            width: 73px;
            height: 38px;
        }

        &:focus,
        &:active {
            outline: none;
        }
        &:focus-visible {
            outline: auto;
        }
    }

    &__slider {
        @apply absolute top-0 left-0 right-0 bottom-0;
        @apply cursor-pointer;
        @apply bg-light-grey-1 rounded-full;
        @apply duration-300;

        &::before {
            @apply absolute bg-charcoal duration-300 rounded-full;
            content: '';
            left: 3px;
            bottom: 3px;
            width: 26px;
            height: 26px;

            @screen md {
                width: 32px;
                height: 32px;
            }
        }
    }

    &__switch--toggled &__slider {
        @apply bg-white;
    }

    &__switch--toggled &__slider::before {
        transform: translateX(34px);
        @apply bg-content-blue;
    }

    /* Color */
    &__switch--dark {
        .toggle-switch__switch--toggled {
            & + .toggle-switch__slider {
                @apply bg-content-blue;

                &::before {
                    @apply bg-charcoal;
                }
            }
        }

        .toggle-switch__slider::before {
            @apply bg-charcoal;
        }
    }

    &__switch--primary {
        .toggle-switch__slider {
            @apply bg-light-grey-1;
        }

        .toggle-switch__slider::before {
            @apply bg-charcoal;
        }
    }

    &__switch--primary.toggle-switch__switch--toggled {
        .toggle-switch__slider {
            @apply bg-content-blue;
        }

        .toggle-switch__slider::before {
            @apply bg-white;
        }
    }

    /* Size */
    &__switch--small {
        width: 41px;
        height: 24px;

        .toggle-switch__slider::before {
            width: 18px;
            height: 18px;
        }

        &.toggle-switch__switch--toggled .toggle-switch__slider::before {
            transform: translateX(16px);
        }
    }
}
