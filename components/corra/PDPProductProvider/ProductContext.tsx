import { SimpleProduct } from '@pylot-data/pylotschema'
import { Dispatch, useState } from 'react'
import { createContext, FC, useContext, useMemo } from 'react'
import { DEFAULT_PRODUCT } from './constants'

export type PDPProviderProps = {
    product: SimpleProduct
}

export type PDPProduct = {
    product: SimpleProduct
    updateProduct: Dispatch<SimpleProduct>
}

const PDPContext = createContext<PDPProduct>({
    product: DEFAULT_PRODUCT,
    updateProduct: (product: SimpleProduct) => {
        //
    }
})

export const PDPProvider: FC<PDPProviderProps> = ({ children, product }) => {
    const [currentProduct, setCurrentProduct] = useState<SimpleProduct>(product)

    const value = useMemo(
        () => ({
            product: currentProduct,
            updateProduct: setCurrentProduct
        }),
        [currentProduct]
    )

    return <PDPContext.Provider value={value}>{children}</PDPContext.Provider>
}

export const usePDPProduct = (): PDPProduct => useContext(PDPContext)
