import { FC } from 'react'
import type { BannerItemType } from '../Carousel/Carousel'
import s from './BanneriCue.module.scss'
import Image from 'next/image'
import ChevronRightBanner from '@components/icons/ChevronRightBanner'
import { useTranslation } from 'next-i18next'

interface IcueBannerItemProps {
    bannerItem: BannerItemType
    classNameContainer?: string
}

export const IcueBannerItem: FC<IcueBannerItemProps> = ({
    bannerItem
}): JSX.Element => {
    const { t } = useTranslation(['common'])
    const desktopUrl =
        bannerItem?.desktopMedia?.backgroundImage?.file?.url || ''
    const mobileUrl = bannerItem?.mobileMedia?.backgroundImage?.file?.url || ''
    const desktopDescription =
        bannerItem?.desktopMedia?.backgroundImage?.description || ''
    const mobileDescription =
        bannerItem?.mobileMedia?.backgroundImage?.description || ''

    const { heading, subHeading, ctaButton, logo } = bannerItem
    const link = ctaButton?.url

    const innerContent = (
        <>
            <div className={s['extra-info-container']}>
                {logo && logo?.file && logo?.file?.url && (
                    <div className={s['logo-container']}>
                        <img
                            src={logo?.file?.url}
                            alt={logo?.description}
                            width={165}
                            height={86}
                        />
                    </div>
                )}
                <h1 className={s['heading']}>{heading}</h1>
                <p className={s['sub-heading']}>{subHeading}</p>
                {ctaButton && ctaButton?.displayText && (
                    <a
                        href={link}
                        className={s['cta']}
                        aria-label={`${
                            ctaButton?.displayText &&
                            `${ctaButton?.displayText} - `
                        }${t('ada|Opens in the current Tab')}`}
                    >
                        <p>{ctaButton?.displayText}</p>
                        <div className={s['right-arrow-wrapper']}>
                            <ChevronRightBanner />
                        </div>
                    </a>
                )}
            </div>

            {desktopUrl && (
                <div className="hidden md:block">
                    <Image
                        src={desktopUrl}
                        alt={desktopDescription || ''}
                        layout="fill"
                        objectFit="cover"
                    />
                </div>
            )}
            {(mobileUrl || desktopUrl) && (
                <div className="block md:hidden">
                    <Image
                        src={mobileUrl || desktopUrl}
                        alt={mobileDescription || desktopDescription || ''}
                        layout="fill"
                        objectFit="cover"
                    />
                </div>
            )}
        </>
    )

    const logodiv = logo?.file?.url
    return (
        <div className={logodiv ? s['inner-content-logo'] : s['inner-content']}>
            {innerContent}
        </div>
    )
}

export default IcueBannerItem
