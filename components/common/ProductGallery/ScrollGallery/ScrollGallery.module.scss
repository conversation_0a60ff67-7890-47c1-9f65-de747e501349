.scroll-image-container {
    @apply w-auto;
    .zoom-in-btn {
        &:focus {
            border: blue 3px solid;
        }
    }
}

.pdp-slider-main {
    :global {
        .swiper-wrapper {
            .swiper-slide {
                @apply md:w-auto md:max-w-full #{!important};
            }
        }
    }
}

.thumb-mask-more-items {
    @apply absolute w-full h-full flex justify-center items-center left-0 z-1;
    bottom: 0;

    &:focus {
        border: blue 3px solid;
    }

    > span {
        @apply rounded-full text-black text-base flex justify-center items-center shadow-xl transition-all duration-150 font-bold #{!important};
        width: 52px;
        background-color: #ffffffed;
        height: 52px;
        &:focus {
            border: blue 3px solid;
        }
    }

    &:hover > span {
        transform: scale(1.1);
    }
}

.slider-gallery {
    &__fullscreen {
        opacity: 0;
        transition: opacity 300ms;
        transition-delay: 200ms;
        z-index: 1;

        &--visible {
            opacity: 1;
        }
    }
}
