import type { Maybe, ProductImage, ProductVideo } from '@pylot-data/pylotschema'
import cn from 'classnames'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import React, { ReactElement, useEffect, useRef, useState } from 'react'
import SwiperCore, {
    FreeMode,
    Keyboard,
    Mousewheel,
    Navigation,
    Thumbs
} from 'swiper'
import { SwiperProps, SwiperRef, SwiperSlideProps } from 'swiper/react'
import { ThumbsEvents, ThumbsOptions } from 'swiper/types'
import { SLIDES_PER_VIEW } from '../FullscreenGallery/FullscreenGallery'
import type { TVideoPlayer } from '../VideoPlayer'
import ImageSlide from '../util/ImageSlide'
import s from './ScrollGallery.module.scss'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { renderProductImageAlt } from '../util/renderProductImageAlt'

const FullscreenGallery = dynamic(() => import('../FullscreenGallery'))
const VideoPlayer = dynamic<TVideoPlayer>(() =>
    import('../VideoPlayer').then((module) => module.VideoPlayer)
)

type ProducGalleryType = {
    mediaGalleryEntries: Maybe<ProductVideo | ProductImage>[]
    zoom: boolean
    loop?: boolean
    showFullscreen: boolean
    thumb?: boolean
    sliderProps?: {
        main?: {
            allowTouchMove?: boolean
            spaceBetween?: number
            maxSlides?: number
            navigation?: boolean
            pagination?:
                | boolean
                | {
                      clickable: boolean
                  }
        }
        thumbs?: {
            spaceBetween?: number
            slidesPerView?: number
        }
    }
    embedVideoUrl?: string
    setMobileBarPosition: React.Dispatch<React.SetStateAction<number>>
    specialVideo?: boolean
}

const defaultSliderProps = {
    main: {
        modules: [FreeMode, Keyboard, Mousewheel, Navigation, Thumbs],
        spaceBetween: 10,
        navigation: true,
        pagination: {
            clickable: true
        }
    },
    thumbs: {
        modules: [FreeMode, Keyboard, Mousewheel, Navigation, Thumbs],
        spaceBetween: 10,
        slidesPerView: 8
    }
}

const ScrollGallery = (props: ProducGalleryType): ReactElement => {
    const {
        mediaGalleryEntries,
        zoom,
        thumb = false,
        sliderProps,
        setMobileBarPosition
    } = props
    const { t } = useTranslation(['common'])
    const [isFullscreen, setFullscreen] = useState(false)
    const [sliderIndex, setSliderIndex] = useState(0)
    const [thumbsSwiper, setThumbsSwiper] = useState<ThumbsEvents | null>()
    const [swiperInstance, setSwiperInstance] = useState<SwiperCore | null>()
    const [initialSlide, setInitialSlide] = useState<boolean>(true)
    const galleryContainer = useRef<HTMLDivElement | null>(null)
    const [focusOnGallery, setFocusOnGallery] = useState<boolean>(false)
    const [SwiperComponent, setSwiperComponent] = useState<React.ComponentType<
        React.RefAttributes<SwiperRef> & SwiperProps
    > | null>(null)
    const [
        SwiperSlideComponent,
        setSwiperSlideComponent
    ] = useState<React.ComponentType<SwiperSlideProps> | null>(null)

    const { pageTheme } = useLayoutContext()
    const isNeo = pageTheme === 'neo'
    useEffect(() => {
        const html = document.getElementsByTagName('html')[0]
        html.classList.remove('zoom-open')
        if (isFullscreen) html.classList.add('zoom-open')
    }, [isFullscreen])

    useEffect(() => {
        import('swiper/react').then(({ Swiper, SwiperSlide }) => {
            setSwiperComponent(() => Swiper)
            setSwiperSlideComponent(() => SwiperSlide)
        })
    }, [])

    useEffect(() => {
        if (sliderIndex !== 0) {
            swiperInstance?.slideToLoop(0, 100, true)
            setSliderIndex(0)
        }
    }, [mediaGalleryEntries])

    if (!SwiperComponent || !SwiperSlideComponent) {
        return (
            <div className={cn('slider-gallery')}>
                <div className="scroll-image-container">
                    <div
                        className={cn('gallery-container', {
                            ['invisible']: isFullscreen,
                            ['gallery-container-focus']: focusOnGallery
                        })}
                        tabIndex={isFullscreen ? -1 : 0}
                        ref={galleryContainer}
                        role="region"
                        aria-label="carousel"
                        onFocus={() => {
                            setFocusOnGallery(true)
                        }}
                        onBlur={() => setFocusOnGallery(false)}
                    >
                        <div
                            className={cn('gallery-slider', {
                                ['invisible']: isFullscreen
                            })}
                        >
                            <div className="swiper swiper-initialized swiper-horizontal swiper-ios swiper-backface-hidden">
                                <div
                                    className="swiper-wrapper"
                                    style={{
                                        transitionDuration: '0ms'
                                    }}
                                >
                                    <div
                                        className="swiper-slide swiper-slide-active"
                                        data-swiper-slide-index="0"
                                        aria-label="image 1 of 5"
                                    >
                                        <div className="swiper-slide-image-container zoom-disabled">
                                            {!isFullscreen &&
                                            mediaGalleryEntries[0]
                                                ?.__typename ===
                                                'ProductVideo' ? (
                                                <VideoPlayer
                                                    url={`${mediaGalleryEntries[0]
                                                        .video_content
                                                        ?.video_url!}`}
                                                    index={0}
                                                    image={
                                                        mediaGalleryEntries[0]
                                                            ?.url
                                                    }
                                                    isThumbnail={false}
                                                    sliderIndex={sliderIndex}
                                                    onClick={() =>
                                                        isNeo &&
                                                        openFullscreen(0)
                                                    }
                                                />
                                            ) : (
                                                <ImageSlide
                                                    image={
                                                        mediaGalleryEntries[0]
                                                    }
                                                    width={1407}
                                                    height={794}
                                                    index={0}
                                                    zoom={zoom}
                                                    showFullscreen={!0}
                                                    s={s}
                                                    openFullscreen={() =>
                                                        !allowTouchMove &&
                                                        openFullscreen(0)
                                                    }
                                                />
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    const Swiper = SwiperComponent
    const SwiperSlide = SwiperSlideComponent

    const closeFullscreen = async (idx: number) => {
        await swiperInstance?.slideTo(idx + 1)
        setFullscreen(false)
    }
    const { maxSlides, allowTouchMove } = { ...sliderProps?.main }

    const openFullscreen = (i: number) => {
        setSliderIndex(i)
        setFullscreen(true)

        // setTimeout(() => swiperInstance?.slideTo(0), 200)
    }

    const syncThumbnail = (swiperCore: SwiperCore, delay: number) => {
        //Quick fix to swiper bug
        if (!initialSlide) {
            setTimeout(() => swiperCore.thumbs?.update(false), delay)
            setMobileBarPosition(swiperCore.realIndex)
        } else {
            setInitialSlide(false)
        }
    }

    const renderImages = (
        mediaGalleryData: Maybe<ProductVideo | ProductImage>[],
        width = 1050,
        height = 992,
        showFullscreenGallery = false,
        isThumbnail = false,
        maxSlides = 0,
        className?: string,
        isNeo?: boolean
    ) =>
        mediaGalleryData?.map((image, i) => {
            const isShowMoreButtonVisible =
                isThumbnail &&
                maxSlides > 0 &&
                i === maxSlides - 1 &&
                mediaGalleryData.length > maxSlides

            if (maxSlides > 0 && i >= maxSlides) {
                return
            }
            return (
                <div key={i} className="scroll-image-container">
                    <SwiperSlide
                        key={i}
                        onClick={() =>
                            !allowTouchMove &&
                            isShowMoreButtonVisible &&
                            openFullscreen(i)
                        }
                        {...(isThumbnail && {
                            onKeyPress: (e: React.KeyboardEvent) => {
                                if (e.key === 'Enter' || e.code === 'Space') {
                                    e.preventDefault()
                                    if (
                                        isShowMoreButtonVisible ||
                                        i === mediaGalleryData.length - 1
                                    ) {
                                        openFullscreen(0)
                                    } else {
                                        swiperInstance?.slideToLoop(
                                            i,
                                            100,
                                            true
                                        )
                                        setSliderIndex(i)
                                    }
                                }
                            },
                            tag: 'button'
                        })}
                        aria-label={`image ${i + 1} of ${
                            mediaGalleryData?.length
                        }`}
                        className={cn({
                            ['swiper-slide-last-item']:
                                isShowMoreButtonVisible ||
                                i === mediaGalleryData.length - 1
                        })}
                    >
                        <div
                            className={cn(
                                'swiper-slide-image-container',
                                className,
                                {
                                    'zoom-disabled': allowTouchMove,
                                    'video-thumbnail':
                                        image?.__typename === 'ProductVideo'
                                }
                            )}
                        >
                            {!isFullscreen &&
                            image?.__typename === 'ProductVideo' ? (
                                <VideoPlayer
                                    url={`${image.video_content?.video_url!}`}
                                    index={i}
                                    image={image?.url}
                                    isThumbnail={isThumbnail}
                                    sliderIndex={sliderIndex}
                                    onClick={() => isNeo && openFullscreen(i)}
                                    videoDescription={
                                        image?.videoDescription as
                                            | string
                                            | undefined
                                    }
                                />
                            ) : (
                                <>
                                    {isShowMoreButtonVisible && (
                                        <div
                                            className={
                                                s['thumb-mask-more-items']
                                            }
                                        >
                                            <span>{`+${
                                                mediaGalleryData.length -
                                                maxSlides
                                            }`}</span>
                                        </div>
                                    )}
                                    <ImageSlide
                                        image={image}
                                        width={width}
                                        height={height}
                                        index={i}
                                        zoom={zoom}
                                        showFullscreen={showFullscreenGallery}
                                        s={s}
                                        openFullscreen={() =>
                                            !allowTouchMove && openFullscreen(i)
                                        }
                                    />
                                </>
                            )}

                            {isNeo && (
                                <div className="swiper-slide-image-container-overlay" />
                            )}
                        </div>
                        {/* eslint-disable-next-line jsx-a11y/role-supports-aria-props,jsx-a11y/click-events-have-key-events,jsx-a11y/no-noninteractive-element-interactions */}
                        <div
                            className={cn('zoom-in-btn sr-only')}
                            onClick={() => openFullscreen(i)}
                            aria-expanded={isFullscreen}
                            //  eslint-disable-next-line jsx-a11y/no-interactive-element-to-noninteractive-role
                            role="tooltip"
                        />
                    </SwiperSlide>
                </div>
            )
        })
    return (
        <div className={cn('slider-gallery')}>
            <div
                className={cn(s['slider-gallery__fullscreen'], {
                    [s['slider-gallery__fullscreen--visible']]: isFullscreen
                })}
            >
                {isFullscreen && (
                    <FullscreenGallery
                        closeFullscreen={(idx) => closeFullscreen(idx)}
                        mediaGalleryEntries={mediaGalleryEntries}
                        initialSlideIndex={sliderIndex}
                        allowTouchMove={allowTouchMove}
                    />
                )}
            </div>
            <div
                className={cn('gallery-container', {
                    ['invisible']: isFullscreen,
                    ['gallery-container-focus']: focusOnGallery
                })}
                tabIndex={isFullscreen ? -1 : 0}
                ref={galleryContainer}
                role="region"
                aria-label="carousel"
                onFocus={() => {
                    setFocusOnGallery(true)
                }}
                onBlur={() => setFocusOnGallery(false)}
            >
                {thumb && (
                    <div className="gallery-thumbnails">
                        <div className="thumbnails-container">
                            <Swiper
                                onSwiper={(event) => setThumbsSwiper(event)}
                                {...{
                                    ...defaultSliderProps.thumbs,
                                    ...sliderProps?.thumbs
                                }}
                                nested
                                watchSlidesProgress
                                freeMode={{
                                    enabled: true,
                                    momentum: false
                                }}
                                threshold={1}
                                resistance={false}
                                direction="vertical"
                                slidesPerView={SLIDES_PER_VIEW}
                                spaceBetween={22}
                            >
                                {renderImages(
                                    mediaGalleryEntries,
                                    106,
                                    106,
                                    false,
                                    true,
                                    maxSlides,
                                    'cursor-pointer',
                                    isNeo
                                )}
                            </Swiper>
                        </div>
                    </div>
                )}
                <div
                    className={cn('gallery-slider', {
                        ['invisible']: isFullscreen
                    })}
                >
                    <Swiper
                        key={!swiperInstance ? 'Swiper0' : 'Swiper1'}
                        {...{
                            ...defaultSliderProps.main,
                            ...sliderProps?.main
                        }}
                        thumbs={{ swiper: thumbsSwiper } as ThumbsOptions}
                        onSwiper={(swiperCore: SwiperCore) => {
                            syncThumbnail(swiperCore, 300)
                            setSwiperInstance(swiperCore)
                            setMobileBarPosition(0)
                            setSliderIndex(swiperCore.realIndex)
                        }}
                        onSlideChange={(swiperCore: SwiperCore) => {
                            syncThumbnail(swiperCore, 100)
                            setSliderIndex(swiperCore.realIndex)
                        }}
                        spaceBetween={100}
                        loop
                        allowTouchMove={allowTouchMove}
                    >
                        {!!swiperInstance &&
                            renderImages(mediaGalleryEntries, 1407, 749, true)}
                    </Swiper>
                </div>
            </div>
        </div>
    )
}

export default ScrollGallery
