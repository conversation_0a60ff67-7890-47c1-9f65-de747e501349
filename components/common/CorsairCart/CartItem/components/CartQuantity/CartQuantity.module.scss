.cart-quantity {
    @apply flex flex-row justify-evenly content-center items-center my-1 text-charcoal;
    border-color: #6a6a6a;
    min-width: 78px;
    min-height: 32px;
    border: 1px solid #111111;
    border-radius: 6px;
    flex-direction: row-reverse;
    position: relative;

    // &:focus {
        //     border: darkblue solid 2px;
        //     filter: contrast(3.1);
        // }

                // button,
                // span {
                //     &:focus {
                //         border: darkblue solid 2px;
                //         filter: contrast(3.1);
                //     }
                // }

    svg {
        width: 16px;
        height: 16px;
    }

    __icon {
        @apply transition-transform duration-300;
    }

    .quantity-selector {
        overflow: auto;
        position: absolute;
        top: 105%;
        left: 0;
        z-index: 6;
        border: 1px solid #151515;
        width: 100%;
        background-color: #fff;
        border-radius: 0.5rem;

        li {
            padding: 3px 16px;

            span {
                font-size: 16px;
                font-weight: 400;
            }

            &:hover {
                background-color: #EAEAEA;
            }

            >button {
                @apply font-semibold text-sm text-left;
                width: 100%;
                // padding: 0 10px;

                &:focus-visible {
                    outline: 1px solid #fff !important;
                    outline-offset: 2px;
                    border-radius: 2px;
                }

                &:hover {
                    cursor: pointer;
                }
            }
        }
    }
}

.quantity-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 0 5px;

    >span {
        margin-right: 18px;
    }
}

.input-wrapper {
    max-width: 80px;
    padding: 3px 5px 3px 10px;
}