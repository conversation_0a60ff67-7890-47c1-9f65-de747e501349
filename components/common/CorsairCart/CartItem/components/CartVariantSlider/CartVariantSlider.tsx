import cn from 'classnames'
import Slider, { Settings as SliderConfigType } from 'react-slick'
import CorsairImage from '@corsairitshopify/corsair-image'
import {
    ConfigurableProduct,
    ConfigurableVariant
} from '@pylot-data/pylotschema'

import SliderArrow from '@components/atoms/SliderArrow/SliderArrow'

import s from './CartVariantSlider.module.scss'

const DEFAULT_IMAGE = '/images/default-product-image.png'

const sliderConfig: SliderConfigType = {
    infinite: false,
    autoplay: false,
    arrows: true,
    slidesToShow: 4.5,
    swipe: false,
    prevArrow: <SliderArrow />,
    nextArrow: <SliderArrow type="next" />
}

const CartVariantSlider = ({
    variants,
    activeVariant,
    setActiveVariant
}: {
    variants: ConfigurableVariant[]
    activeVariant?: ConfigurableProduct | null
    setActiveVariant: (newVariant: ConfigurableProduct) => void
}): JSX.Element => {
    return (
        <div className={s['container']}>
            <Slider {...sliderConfig}>
                {variants.map((variant) => {
                    const product = variant?.product

                    return (
                        product && (
                            <div
                                key={product.name}
                                title={product.name || ''}
                                className={cn(s['item'], 'relative z-0', {
                                    [s['active']]:
                                        activeVariant?.name === product?.name
                                })}
                            >
                                <button
                                    className={s['slider-activation']}
                                    onClick={() =>
                                        setActiveVariant(
                                            variant.product as ConfigurableProduct
                                        )
                                    }
                                />
                                <CorsairImage
                                    src={product.image?.url || DEFAULT_IMAGE}
                                    width={27}
                                    height={27}
                                    layout="fixed"
                                    objectFit="cover"
                                />
                            </div>
                        )
                    )
                })}
            </Slider>
        </div>
    )
}

export default CartVariantSlider
