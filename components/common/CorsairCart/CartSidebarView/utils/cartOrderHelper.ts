import { CartItemInterface, Maybe } from '@pylot-data/pylotschema'

export const orderCartFromLocalStorageSkuList = (
    itemsList: Maybe<Maybe<CartItemInterface>[]> | undefined
): Maybe<Maybe<CartItemInterface>[]> | undefined => {
    let skuList = undefined
    const skuListString = localStorage.getItem('skuCartOrder')
    if (skuListString) skuList = JSON.parse(skuListString)
    if (!skuList) return itemsList

    const finalList: Maybe<CartItemInterface>[] = []
    skuList.forEach((cartItemB: string) => {
        itemsList?.forEach((cartItemA: Maybe<CartItemInterface>) => {
            if (cartItemB === cartItemA?.product.sku && cartItemA.uid !== '0') {
                finalList.push(cartItemA)
            }
        })
    })
    itemsList?.forEach((cartItemA: Maybe<CartItemInterface>) => {
        if (cartItemA?.uid === '0') {
            finalList.push(cartItemA)
        }
    })
    if (finalList.length === itemsList?.length) return finalList
    return itemsList
}
