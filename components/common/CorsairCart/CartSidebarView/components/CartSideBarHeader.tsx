import { FC } from 'react'
import s from '../CartSidebarView.module.scss'
import { useUI } from '@corsairitshopify/pylot-ui/context'
import { useTranslation } from 'next-i18next'
import cn from 'classnames'
import CloseIcon from '@components/icons/CloseIcon'

interface Props {
    isEmpty: boolean
}

const CartSideBarHeader: FC<Props> = ({ isEmpty }) => {
    const { t } = useTranslation('common')
    const headerLabel = t('cart|Review your cart')

    const { closeSidebar } = useUI()

    const handleClose = () => {
        closeSidebar()
    }
    return (
        <header className={cn(s['cart-header'])}>
            <h2
                className={cn(
                    s['cart-header-label'],
                    isEmpty && s['cart-header-empty']
                )}
            >
                {headerLabel}
            </h2>
            <button
                onClick={handleClose}
                aria-label={t('Close panel')}
                className={cn(s['cart-btn-close'], 'cartSidebar-close-btn')}
            >
                <CloseIcon />
            </button>
        </header>
    )
}

export default CartSideBarHeader
