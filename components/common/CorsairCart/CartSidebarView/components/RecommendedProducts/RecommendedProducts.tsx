import s from './RecommendedProducts.module.scss'
import cn from 'classnames'
import { useCart } from '@lib/cart-manager'
import { FC, useCallback, useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { getProducts, Products } from '@pylot-data/api/operations/get-products'
import type { ProductInterface } from '@pylot-data/fwrdschema'
import type {
    ProductInterface as ProductInterfacePylot,
    ProductSkuInfo
} from '@pylot-data/pylotschema'
import { ConfigurableProduct, GiftCardProduct } from '@pylot-data/fwrdschema'
import CartDropdown from '@components/common/CorsairCart/CartDropdown'
import Link from 'next/link'
import { useAddToCart } from '@pylot-data/hooks/cart/use-add-to-cart'
import { useProductUrlBuilder } from '@lib/hooks/useBuildProductUrl'
import { ModelTypeEnum } from '@config/base'
import { ProductPrice } from '@components/common/ProductPrice/ProductPrice'
import snarkdown from 'snarkdown'
import Image from '@corsairitshopify/corsair-image'
import { chevronRight as ChevronRightIcon } from '@components/atoms/Icon/general'
import { ToastType, useUI } from '@corsairitshopify/pylot-ui/context'
import { useTranslation } from 'next-i18next'
import { ButtonLabel } from '@pylot-data/hooks/product/use-product-ui'
import { handleSidebarCartFocus } from 'helpers/AdaHelpers'

const PRODUCT_TO_GET_RECOMMENDED_PRODUCTS = 0
const MAX_NUMBER_OF_RECOMMENDED_PRODUCTS = 2

const TIMEOUT_AFTER_OPENING_SIDEBAR = 300

const RecommendedProducts: FC = () => {
    const [recommendedProductBlocks, setRecommendedProductBlocks] = useState<
        ConfigurableProduct[] & GiftCardProduct[]
    >([])
    const { locale } = useRouter()
    const { t } = useTranslation('common')
    const { openCartToast } = useUI()
    const { data } = useCart()
    const { addToCart } = useAddToCart()
    const cart = data?.data?.cart

    const productUrlBuilder = useProductUrlBuilder({
        page: ModelTypeEnum.PRODUCT
    })

    // get works_well_with from first item in cart
    const recommendedProductsSkus: string[] =
        cart?.items?.[
            PRODUCT_TO_GET_RECOMMENDED_PRODUCTS
        ]?.product.customers_also_bought
            ?.map((product: ProductSkuInfo) => product.sku)
            ?.slice(0, MAX_NUMBER_OF_RECOMMENDED_PRODUCTS) || []

    const addProductHandler = async (product: ProductInterface) => {
        const productData = product as ProductInterfacePylot

        const response = await addToCart(
            [
                {
                    sku: productData.sku as string,
                    uid: productData?.uid,
                    quantity: 1
                }
            ],
            [productData]
        )

        const errorMessage =
            response?.errors?.[0]?.message ??
            response?.user_errors?.[0]?.message

        if (errorMessage) {
            setTimeout(
                () => openCartToast(errorMessage, ToastType.Warning),
                TIMEOUT_AFTER_OPENING_SIDEBAR
            )
        }
    }

    const getRecommendedProducts = useCallback(
        async (skusInCart) => {
            if (recommendedProductsSkus?.length >= 1) {
                const recommendedProducts: Products[] = await getProducts(
                    recommendedProductsSkus,
                    null,
                    locale ?? ''
                )

                let productsData: (ConfigurableProduct &
                    GiftCardProduct)[] = recommendedProducts
                    .map(({ productData }) => productData?.[0])
                    .filter(
                        (data): data is ConfigurableProduct & GiftCardProduct =>
                            !!data
                    )
                productsData = productsData
                    ?.filter((p) => p)
                    .filter(
                        ({ sku, stock_status }) =>
                            sku &&
                            !skusInCart.includes(sku) &&
                            recommendedProductsSkus.includes(sku)
                    )

                setRecommendedProductBlocks(productsData)
            }
        },
        [cart]
    )

    const handleOnKeyPress = (product: ProductInterface) => {
        addProductHandler(product)
        handleSidebarCartFocus(`recommended-product-atc-btn-${product.uid}`)
    }

    useEffect(() => {
        const skusInCart = cart?.items?.map((item) => item?.product?.sku) || []
        getRecommendedProducts(skusInCart)
    }, [cart?.items])

    return recommendedProductBlocks.length > 0 ? (
        <CartDropdown title={t('cart|Add Ons')}>
            {recommendedProductBlocks.map((item, key) => {
                const {
                    url_key,
                    name,
                    price_range,
                    short_description,
                    description,
                    image
                } = item

                const productUrl = `/p/${url_key}`

                return (
                    <div className={s['container']} key={key}>
                        <div className={s['container-title']}>
                            <Link
                                href={productUrlBuilder({
                                    product: item,
                                    url_key: url_key
                                })}
                                aria-label={`${t('cart|ADD') + name} - ${t(
                                    'ada|Opens in the current Tab'
                                )}`}
                            >
                                <span
                                    className={cn(
                                        s['product-name'],
                                        !price_range && s['product-unavailable']
                                    )}
                                >
                                    {t('cart|ADD')} {name}
                                </span>
                            </Link>
                            <ProductPrice
                                className={`${s['price-text']} font-univers67BoldCondensed`}
                                priceRange={price_range}
                                splitSymbol
                            />
                        </div>
                        <Image
                            src={image?.url || ''}
                            width={50}
                            height={50}
                            alt={name || ''}
                        />
                        <div className={s['container-description']}>
                            <div>
                                <div
                                    className={s['container-short-description']}
                                    dangerouslySetInnerHTML={{
                                        __html: snarkdown(
                                            short_description?.html || ''
                                        )
                                    }}
                                />
                                <div
                                    className={s['container-short-description']}
                                    dangerouslySetInnerHTML={{
                                        __html: snarkdown(
                                            description?.html || ''
                                        )
                                    }}
                                />
                            </div>

                            <div className={s['container-button-add']}>
                                <Link href={productUrl} passHref>
                                    <a
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className={s['title-discover']}
                                        aria-label={`${t(
                                            'cart|DISCOVER'
                                        )} - ${t('ada|Opens in a new Tab')}`}
                                    >
                                        {t('cart|DISCOVER')}
                                        <ChevronRightIcon />
                                    </a>
                                </Link>
                                <button
                                    id={`recommended-product-atc-btn-${item.uid}`}
                                    type="button"
                                    className={s['btn-checkout']}
                                    onClick={() => addProductHandler(item)}
                                    onKeyPress={(
                                        e: React.KeyboardEvent<HTMLElement>
                                    ) => {
                                        if (
                                            e.key === 'Enter' ||
                                            e.key === ' '
                                        ) {
                                            e.preventDefault()
                                            handleOnKeyPress(item)
                                        }
                                    }}
                                >
                                    {t(`cart|${ButtonLabel.ADD_TO_CART}`)}
                                </button>
                            </div>
                        </div>
                    </div>
                )
            })}
        </CartDropdown>
    ) : null
}

export default RecommendedProducts
