import { Maybe } from '@pylot-data/pylotschema'
import React, {
    FC,
    useMemo,
    useContext,
    useReducer,
    createContext
} from 'react'

export interface State {
    cartError: Maybe<string>
    isLoading: Maybe<boolean>
    skusToRemoveQueue: string[]
}

const initialState: State = {
    cartError: null,
    isLoading: false,
    skusToRemoveQueue: []
}

type UseCartStateReturn = State & {
    setCartError: (cartError: Maybe<string>) => void
    setIsLoading: (isLoading: Maybe<boolean>) => void
    addToQueue: (sku: string) => void
    removeFromQueue: (sku: string) => void
    beforeCheckoutCb?: () => void
}

type Action =
    | {
          type: 'SET_CART_ERROR'
          cartError: string | null
      }
    | {
          type: 'SET_IS_LOADING'
          isLoading: boolean | null
      }
    | {
          type: 'ADD_TO_QUEUE'
          sku: string
      }
    | {
          type: 'REMOVE_FROM_QUEUE'
          sku: string
      }

type CartReducer = (state: State, action: Action) => State

const CartContext = createContext<State | any>(initialState)

const cartReducer: CartReducer = (state, action) => {
    switch (action.type) {
        case 'SET_CART_ERROR':
            return {
                ...state,
                cartError: action.cartError
            }
        case 'SET_IS_LOADING':
            return {
                ...state,
                isLoading: action.isLoading
            }
        case 'ADD_TO_QUEUE':
            return {
                ...state,
                skusToRemoveQueue: [...state.skusToRemoveQueue, action.sku]
            }
        case 'REMOVE_FROM_QUEUE':
            return {
                ...state,
                skusToRemoveQueue: state.skusToRemoveQueue.filter(
                    (prevSku) => prevSku !== action.sku
                )
            }
        default:
            return state
    }
}

export const CartProvider: FC<{
    beforeCheckoutCb?: () => void
}> = (props) => {
    const [state, dispatch] = useReducer(cartReducer, initialState)

    const setCartError = (cartError: string | null) =>
        dispatch({ type: 'SET_CART_ERROR', cartError })

    const setIsLoading = (isLoading: boolean | null) =>
        dispatch({ type: 'SET_IS_LOADING', isLoading })

    const addToQueue = (sku: string) =>
        dispatch({
            type: 'ADD_TO_QUEUE',
            sku
        })

    const removeFromQueue = (sku: string) =>
        dispatch({
            type: 'REMOVE_FROM_QUEUE',
            sku
        })

    const value = useMemo(
        () => ({
            ...state,
            setCartError,
            setIsLoading,
            addToQueue,
            removeFromQueue,
            ...(props?.beforeCheckoutCb
                ? { beforeCheckoutCb: props.beforeCheckoutCb }
                : {})
        }),
        [state]
    )

    return <CartContext.Provider value={value} {...props} />
}

export const CartContextView: FC<{
    beforeCheckoutCb?: () => void
}> = ({ children, beforeCheckoutCb }) => (
    <CartProvider beforeCheckoutCb={beforeCheckoutCb}>{children}</CartProvider>
)
export const useCartState = (): UseCartStateReturn => useContext(CartContext)
