.slider-container {
    //Image container must have position relative
    @apply relative block w-full overflow-hidden;
    height: 35vw !important;
    max-height: 795px;
    min-height: 600px;
}

.extra-info-container {
    @apply w-full absolute z-5 bottom-0 text-white;
    padding-left: 18px;
    padding-right: 18px;

    .heading {
        @apply uppercase font-bebas<PERSON>eue;
        font-size: 35px; //53px desktop
        max-width: 1959px;
        margin: 0 auto;
        letter-spacing: unset !important;
        line-height: 1.1em;
        font-weight: 100;
        text-shadow: 0 2px 13px rgb(0 0 0 / 80%);
    }

    .sub-heading {
        @apply font-helveticaRoman font-medium w-4/5;
        @apply lg:w-2/5 #{!important};
        font-size: 17px;
        letter-spacing: 0.03em;
        line-height: 1.3em;
        text-shadow: 0 2px 10px rgb(0 0 0);
        max-width: 1959px;
        margin-top: 8px;
        margin-bottom: 8px;
    }
}

.cta {
    @apply block font-gothamProMedium font-medium;
    font-size: 1em;
    padding-top: 48px;
    padding-bottom: 80px;

    a {
        @apply uppercase font-medium text-black;
        text-decoration: none;
        font-size: 1em;
        letter-spacing: 0.1em;
        margin: 0 auto;
        background-color: #e9e600;
        padding: 1.2rem 4.2rem;
        line-height: 1;
        transition: all 0.2s ease;

        &:focus {
            outline: auto;
            box-shadow: 0 0 0 0.1rem rgb(255, 255, 255);
        }
    }
}

@screen md {
    .extra-info-container {
        padding-left: 96px;
        padding-right: 96px;

        .heading {
            font-size: 53px !important;
        }

        .sub-heading {
            font-size: 22px !important;
        }
    }
}

.logo-wrapper-mobile {
    @apply md:hidden;
}

.logo-wrapper-desktop {
    @apply hidden md:block #{!important};
    margin-bottom: 15px;
}

.media-mobile,
.media-desktop {
    @apply w-full h-full;
}

.media-mobile {
    @apply md:hidden;
}

.media-desktop {
    @apply hidden md:block #{!important};
}

.slider-video {
    @apply object-cover w-full h-full;
}
