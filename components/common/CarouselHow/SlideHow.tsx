import Image from '@corsairitshopify/corsair-image'
import s from './SlideHow.module.scss'
import {
    CarouselHowItemType,
    Media
} from '@components/common/CarouselHow/CarouselHow'
import cn from 'classnames'
import snarkdown from 'snarkdown'
import { useTranslation } from 'next-i18next'

export const getSlide: React.FC<Media> = (media: Media) => {
    const { backgroundImage } = media

    return (
        <div className={s['media']}>
            {backgroundImage?.file?.url && (
                <Image
                    src={backgroundImage?.file?.url}
                    alt={backgroundImage?.description || ''}
                    layout="fill"
                    objectFit="cover"
                />
            )}
        </div>
    )
}

const SlideHow = ({
    content
}: {
    content: CarouselHowItemType
}): JSX.Element => {
    const { t } = useTranslation(['common'])
    const { heading, subHeading, desktopMedia, description } = content
    const link = heading?.url
    const subHeadingMd = snarkdown(subHeading)
    const descriptionMd = snarkdown(description)

    return (
        <div className={s['main-container']}>
            <div className={cn(s['slider-container'], 'slider-list')}>
                {desktopMedia && getSlide(desktopMedia)}
            </div>
            <div className={s['extra-info-container']}>
                <div className={s['heading']}>
                    <a
                        href={link}
                        aria-label={`${
                            heading?.displayText && `${heading?.displayText} - `
                        }${t('ada|Opens in the current Tab')}`}
                    >
                        {heading?.displayText}
                    </a>
                </div>
                <div
                    className={s['sub-heading']}
                    dangerouslySetInnerHTML={{ __html: subHeadingMd }}
                />
                <div
                    className={s['description']}
                    dangerouslySetInnerHTML={{ __html: descriptionMd }}
                />
            </div>
        </div>
    )
}

export default SlideHow
