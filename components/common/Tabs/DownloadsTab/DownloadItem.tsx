import { DocumentIcon } from '@components/icons/Document'
import { DowloadIcon } from '@components/icons/Dowload'
import cn from 'classnames'
import React, { VFC } from 'react'
import s from './DownloadTab.module.scss'
import { useTranslation } from 'next-i18next'

export enum DownloadIconType {
    DRIVER = 'driver',
    DOCUMENTATION = 'documentation'
}

export interface DownloadItemProps {
    url: string
    label: string
    icon: DownloadIconType
}

export const DownloadItem: VFC<DownloadItemProps> = ({ url, label, icon }) => {
    const { t } = useTranslation(['common'])
    return (
        <div className={cn(s['download-tab--item'], 'flex items-center')}>
            {icon === DownloadIconType.DOCUMENTATION ? (
                <DocumentIcon />
            ) : (
                <DowloadIcon />
            )}
            <a
                download
                href={url}
                className="font-helveticaRoman block font-normal ml-3 mt-1"
                aria-label={`${label} - ${t('ada|Opens in the current Tab')}`}
            >
                {label}
            </a>
        </div>
    )
}
