// @ts-ignore
import s from '@components/molecules/Video/Video.module.scss'
import React, {
    CSSProperties,
    ReactNode,
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
    VideoHTMLAttributes
} from 'react'
import cn from 'classnames'
import { Icon } from '@components/atoms/Icon/Icon'
import { Button } from '@components/molecules/Button/Button'
import { LinkResponse } from '@components/molecules/Link/Link'
import { useOnScreen } from '@lib/hooks/useOnScreen'
import { nanoid } from 'nanoid'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

interface VideoData {
    secure_url: string
    videoDescription?: string
    fallbackImgUrl?: string
    className?: string
    options?: VideoHTMLAttributes<never>
    buttonTitle?: string
    buttonIcon?: string
    link?: LinkResponse
    videoClasses?: string
    play?: boolean
    resetOnStartPlay?: boolean
    hidden?: boolean
    children?: ReactNode
    playEnded?: () => void
    loadRootMargin?: string
    visibleThreshold?: number
    onClick?: () => void
    customOptions?: any
    style?: CSSProperties
    playPauseButton?: boolean
    onKeyPress?: (e: React.KeyboardEvent) => void
}

const ElgatoVideo = ({
    secure_url,
    videoDescription,
    className,
    fallbackImgUrl,
    options,
    buttonTitle,
    buttonIcon,
    link,
    videoClasses,
    play = false,
    resetOnStartPlay = false,
    hidden = false,
    children,
    playEnded,
    loadRootMargin = '300px',
    visibleThreshold = 0.75,
    customOptions,
    style,
    playPauseButton = false,
    onClick,
    onKeyPress
}: VideoData): JSX.Element | null => {
    const videoRef = useRef() as React.MutableRefObject<HTMLVideoElement>
    const [videoPlayed, setVideoPlayed] = useState(false)
    const [canPause, setCanPause] = useState(false)
    const videoDescriptionId = useMemo(() => nanoid(), [])
    const { isAnimationStopped } = useAnimationAndVideosToggle()

    /* load video earlier */
    const { isOnScreen } = useOnScreen(videoRef, true, {
        threshold: 0,
        rootMargin: loadRootMargin
    })
    /* but play only if 95% visible */
    const { isOnScreen: visible } = useOnScreen(videoRef, true, {
        threshold: visibleThreshold
    })
    const playVideo = useCallback(() => {
        if (
            videoRef.current &&
            videoRef.current.paused &&
            videoRef.current.readyState >= 2
        ) {
            if (resetOnStartPlay) {
                videoRef.current.currentTime = 0
            }
            try {
                const playPromise = videoRef.current.play()
                if (playPromise !== undefined) {
                    playPromise
                        .then(() => {
                            setCanPause(true)
                        })
                        .catch(() => {
                            setCanPause(false)
                        })
                }
                setVideoPlayed(true)
            } catch (e) {
                setCanPause(false)
            }
        }
    }, [resetOnStartPlay])
    const pauseVideo = useCallback(() => {
        if (videoRef.current && !videoRef.current.paused) {
            if (resetOnStartPlay) {
                videoRef.current.currentTime = 0 && canPause
            }
            try {
                videoRef.current.pause()
                setVideoPlayed(false)
            } catch (e) {
                //
            }
        }
    }, [canPause, resetOnStartPlay])

    /**
     * If video was not loaded before, load it and after loading
     * play it, if autoplay is true
     * @param autoplay Whether to play video after loading or not
     */
    const loadVideo = useCallback(
        (autoplay: boolean) => {
            function onLoadedData() {
                if (videoRef.current && videoRef.current.readyState >= 2) {
                    // we play video already, but stop it again later,
                    // because otherwise it jumps on iphone
                    // https://www.reddit.com/r/Safari/comments/z9wa1w/using_objectfit_cover_on_safari_causes_the_video/
                    playVideo()
                    if (options?.autoPlay && !visible) {
                        // not visible, yet, so stop playing again (hacky solution)
                        setTimeout(() => {
                            if (options?.autoPlay && !visible) {
                                pauseVideo()
                                if (videoRef.current) {
                                    videoRef.current.currentTime = 0
                                }
                            }
                        }, 10)
                    }
                }
            }

            function onCanPlay() {
                if (videoRef.current) {
                    playVideo()
                }
            }

            if (videoRef.current?.readyState === 0) {
                // if autoplay, wait until video is loaded and then play it
                if (autoplay) {
                    videoRef.current.addEventListener(
                        'loadeddata',
                        onLoadedData
                    )
                    // somehow sometimes videos are not played on loadeddata, so we check again
                    videoRef.current.addEventListener('canplay', onCanPlay)
                }
                // load the video
                videoRef.current.load()
            } else if (autoplay) {
                // video does not to be loaded, but played
                playVideo()
            }

            return () => {
                videoRef.current?.removeEventListener(
                    'loadeddata',
                    onLoadedData
                )
                videoRef.current?.removeEventListener('canplay', onCanPlay)
            }
        },
        [options?.autoPlay, pauseVideo, playVideo, visible]
    )

    useEffect(() => {
        let cleanup: () => void
        if (!isAnimationStopped) {
            if (videoRef.current) {
                // if lazy load, load video
                if (isOnScreen && options?.preload === 'none' && !hidden) {
                    // and if autoplay, play it
                    if (options?.autoPlay && visible) {
                        // && visible // should only play if visible, but not working on iphone :(
                        cleanup = loadVideo(true)
                    } else {
                        cleanup = loadVideo(false)
                    }
                }

                // if it is loop, replay when ended
                if (options?.loop) {
                    videoRef.current.onended = function () {
                        cleanup = loadVideo(true)
                    }
                }
            }
        }

        return () => {
            cleanup?.()
        }
    }, [
        isOnScreen,
        hidden,
        secure_url,
        visible,
        options?.preload,
        options?.loop,
        options?.autoPlay,
        loadVideo,
        isAnimationStopped,
        pauseVideo,
        playVideo
    ])

    // hacky solution for iphone
    useEffect(() => {
        let cleanup: () => void
        if (!isAnimationStopped) {
            if (options?.autoPlay && visible) {
                // console.log('now it is fully visible')
                // videoRef.current?.style.border = '1px solid blue'
                cleanup = loadVideo(true)
            } else if (options?.autoPlay && !visible) {
                // console.log('now it is no longer visible, so stop playing')
                // videoRef.current?.style.border = 'none'
                pauseVideo()
            }
        } else {
            pauseVideo()
        }

        return () => {
            cleanup?.()
        }
    }, [visible, isAnimationStopped, options?.autoPlay, loadVideo, pauseVideo])

    useEffect(() => {
        let cleanup: () => void
        if (!isAnimationStopped) {
            if (play) {
                cleanup = loadVideo(true)
            } else {
                pauseVideo()
            }
        } else {
            pauseVideo()
        }
        return () => {
            cleanup?.()
        }
    }, [play, isAnimationStopped])

    useEffect(() => {
        if (
            !isAnimationStopped &&
            videoRef.current &&
            videoRef.current.paused &&
            videoRef.current.readyState >= 2
        ) {
            videoRef.current.play()
        } else {
            videoRef.current.pause()
        }
    }, [isAnimationStopped])

    useEffect(() => {
        if (!videoRef.current) {
            return
        }

        const currentVideo = videoRef.current
        const playEndedHandler = () => {
            playEnded?.call(this)
        }
        currentVideo.addEventListener('ended', playEndedHandler)

        return () => {
            currentVideo.removeEventListener('ended', playEndedHandler)
        }
    }, [playEnded])

    /**
     * https://github.com/facebook/react/issues/10389
     * This is due to a open bug in react, that the muted attribute is not set correctly
     */
    useEffect(() => {
        if (videoRef.current) {
            videoRef.current.muted = options?.muted ?? false
            videoRef.current.defaultMuted = options?.muted ?? false
        }
    }, [options?.muted, secure_url])

    if (!secure_url) {
        return null
    }

    // if we load the video later, autoplay should be set to false
    const autoPlay = options?.preload === 'none' ? false : options?.autoPlay
    let videoDescAttributes
    if (videoDescription) {
        videoDescAttributes = {
            tabIndex: 0,
            role: 'img',
            'aria-describedby': `video-description-${videoDescriptionId}`
        }
    }

    const handleVideoClick = () => {
        if (playPauseButton) {
            if (videoRef.current?.paused) {
                playVideo()
            } else {
                pauseVideo()
            }
        }
    }

    return (
        <div
            className={cn(s['video'], className)}
            style={style}
            {...videoDescAttributes}
            onClick={onClick}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
                if (e.key === 'Enter') {
                    onClick?.()
                }
            }}
            onKeyPress={onKeyPress}
        >
            {(customOptions?.transparentVideo && (
                <video
                    autoPlay={autoPlay}
                    playsInline={
                        options?.playsInline ? options.playsInline : true
                    }
                    loop={options?.loop}
                    preload={isAnimationStopped ? 'metadata' : options?.preload}
                    muted={options?.muted}
                    poster={fallbackImgUrl}
                    style={{
                        width: 'auto',
                        height: '85%',
                        position: 'relative',
                        top: '15px',
                        right: '-30px',
                        float: 'right',
                        marginTop: 'auto',
                        marginBottom: 'auto',
                        cursor: playPauseButton ? 'pointer' : 'default'
                    }}
                    ref={videoRef}
                    controls={options?.controls}
                    onMouseEnter={options?.onMouseEnter}
                    onMouseLeave={options?.onMouseLeave}
                    key={`video-${secure_url}`}
                    onClick={handleVideoClick}
                    src={secure_url}
                >
                    <track kind="captions" />
                    <source src={customOptions.videoWebm} type="video/webm" />
                    <source
                        src={customOptions.videoMp4}
                        type='video/mp4; codecs="hvc1"'
                    />
                </video>
            )) || (
                <video
                    autoPlay={autoPlay}
                    playsInline={
                        options?.playsInline ? options.playsInline : true
                    }
                    loop={options?.loop}
                    preload={isAnimationStopped ? 'metadata' : options?.preload}
                    poster={fallbackImgUrl}
                    muted={options?.muted}
                    ref={videoRef}
                    className={cn(videoClasses)}
                    controls={options?.controls}
                    key={`video-${secure_url}`}
                    onMouseEnter={options?.onMouseEnter}
                    onMouseLeave={options?.onMouseLeave}
                    onClick={handleVideoClick}
                    style={{
                        cursor: playPauseButton ? 'pointer' : undefined
                    }}
                >
                    <track kind="captions" />
                    <source src={secure_url} />
                </video>
            )}
            {videoDescription && videoDescAttributes && (
                <p
                    className="sr-only"
                    id={`video-description-${videoDescriptionId}`}
                >
                    {videoDescription}
                </p>
            )}
            {buttonTitle && (
                <Button
                    tracking={false}
                    variant="primary"
                    onClick={() => loadVideo(true)}
                    className={cn(s['video__button'], 'popup-youtube', {
                        [s['video__button--hidden']]: videoPlayed
                    })}
                    label={buttonTitle}
                >
                    {buttonIcon && <Icon name={buttonIcon} />}
                    {buttonTitle}
                </Button>
            )}
            {link && link.linkUrl && (
                <Button
                    variant="primary"
                    href={link.linkUrl}
                    className={s['video__button']}
                    label={link.linkTitle}
                >
                    {buttonIcon && <Icon name={buttonIcon} />}
                    {link.linkTitle}
                </Button>
            )}
            {children}
        </div>
    )
}

export default ElgatoVideo
