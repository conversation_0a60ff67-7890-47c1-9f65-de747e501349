import React, { ReactElement } from 'react'

import s from './SearchAutocomplete.module.scss'
import Link from 'next/link'
import { Aggregation, Maybe } from '@pylot-data/pylotschema'
import { useTranslation } from 'next-i18next'
export type ProductItemProp = {
    aggregations?: Maybe<Array<Maybe<Aggregation>>>
    searchTerm: string
}

const CategoryList = ({
    aggregations,
    searchTerm
}: ProductItemProp): ReactElement | null => {
    if (!aggregations?.length) return null
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const { t } = useTranslation(['common'])

    const result = aggregations.find((value: Maybe<Aggregation>) => {
        return value?.attribute_code == 'category_id'
    })

    return (
        <div className={s['autocomplete-category']}>
            <div className={s['search-category']}>
                {result?.options?.slice(0, 4).map((category, index) => (
                    <Link
                        key={index}
                        href={`/search/?q=${searchTerm}&category_id=${category?.value}`}
                        aria-label={`${`${searchTerm} in ${category?.label}`} - ${t(
                            'ada|Opens in the current Tab'
                        )}`}
                    >{`${searchTerm} in ${category?.label}`}</Link>
                ))}
            </div>
        </div>
    )
}
export default CategoryList
