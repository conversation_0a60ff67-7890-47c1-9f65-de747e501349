import { CTA } from '@components/common/CTA'
import { ProductStockStatus } from '@pylot-data/enums/ProductStockStatus.d'
import { useProductFetch } from '@pylot-data/hooks/product/use-product-fetch'
import Image from 'next/image'
import { VFC } from 'react'
import { IProductBlock } from '../types'
import { AddToCartCTA } from './AddToCartCTA'
import s from './ProductBlock.module.scss'
export type ProductBlockProps = {
    productBlock: IProductBlock
}

const c = /*tw*/ {
    commonClass:
        'mt-8 bg-white font-helveticaRoman text-xs lg:text-base rounded text-black leading-4 font-bold',
    ctaClass: 'py-3',
    addToCartClass: 'pt-1'
}

const ProductBlock: VFC<ProductBlockProps> = ({ productBlock }) => {
    const { product } = useProductFetch({ productUrl: productBlock.cta.url })
    const inStock = !!(
        product && product.stock_status === ProductStockStatus.InStock
    )

    const { format } = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    })

    return (
        <div className={s['product-block']}>
            <p className="text-2xl md:text-3xl mb-3 uppercase font-semibold tracking-widest">
                {productBlock.heading}
            </p>
            <CTA cta={productBlock.cta} showDisplayText={false}>
                <div className={`${s['image']}`}>
                    {productBlock.image ? (
                        <Image
                            src={productBlock.image.image.file.url}
                            width={346}
                            height={346}
                            layout="responsive"
                            alt={productBlock.image.image.description || ''}
                        />
                    ) : null}
                </div>
            </CTA>
            <p className="text-center text-white font-normal text-3xl mt-2">
                {format(productBlock.price)}
                <span
                    className="ml-4 text-2xl line-through"
                    style={{ color: '#A2A2A6' }}
                >
                    {format(productBlock.specialPrice)}
                </span>
            </p>
            <p className={s['product-block-text']}>{productBlock.text}</p>
            {inStock ? (
                <AddToCartCTA
                    product={product}
                    className={`${s['product-block-add-to-cart']} ${c.addToCartClass} ${c.commonClass}`}
                />
            ) : (
                <CTA
                    cta={productBlock.cta}
                    className={`${s['product-block-cta']} ${c.commonClass} ${c.ctaClass}`}
                    isLearnMoreBtn
                />
            )}
        </div>
    )
}

export default ProductBlock
