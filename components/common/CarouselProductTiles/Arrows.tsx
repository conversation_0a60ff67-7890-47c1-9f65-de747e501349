/* eslint-disable i18next/no-literal-string */
import { ChevronLeft, ChevronRight } from 'react-feather'
import { CustomArrowProps } from 'react-slick'

export const Arrow = ({
    onClick,
    direction,
    s
}: CustomArrowProps & { direction: string; s: any }): JSX.Element | null => {
    if (!s) return null
    return (
        // eslint-disable-next-line
        <>
            {direction === 'left' ? (
                <button
                    className={`${s['nav-arrow']} absolute left-2 z-50 md:block`}
                    onClick={onClick}
                >
                    <ChevronLeft color="#fff" className={s['chevron']} />
                </button>
            ) : (
                <button
                    className={`${s['nav-arrow']} absolute right-2 md:block`}
                    onClick={onClick}
                >
                    <ChevronRight color="#fff" className={s['chevron']} />
                </button>
            )}
        </>
    )
}
