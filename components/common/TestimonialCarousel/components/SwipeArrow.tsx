import { useTranslation } from 'next-i18next'
import { VFC } from 'react'
import s from '../AnimatedArrow.module.scss'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'
import cn from 'classnames'

export const SwipeArrow: VFC = () => {
    const { t } = useTranslation(['common'])
    const { isAnimationStopped } = useAnimationAndVideosToggle()
    return (
        <div className="relative mb-4 md:mb-10">
            <div className={s['animated-arrow']}>
                <div
                    className={cn(s['arrow'], {
                        'no-animation': isAnimationStopped
                    })}
                />
                <div
                    className={cn(s['line'], {
                        'no-animation': isAnimationStopped
                    })}
                />
            </div>
            <div className="relative text-center text-xl md:-left-5 uppercase">
                {t('Click & Drag')}
            </div>
        </div>
    )
}
