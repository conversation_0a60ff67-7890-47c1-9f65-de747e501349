import React from 'react'
import s from './CategorySlider.module.scss'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import Link from 'next/link'
import { useTranslation } from 'next-i18next'

export interface CategoryInterface {
    backgroundColor?: string
    collectionTitle: string
    identifier: string
    image: ImageType
    title: string
    url: string
}

const Category = ({
    content
}: {
    content: CategoryInterface
}): JSX.Element | null => {
    const { image: collectionImage, title, backgroundColor, url } = content
    const { description, file } = collectionImage
    const { t } = useTranslation(['common'])

    const categoryBackgroundImage = {
        backgroundImage: backgroundColor
    }

    return (
        <Link
            href={url}
            passHref
            aria-label={`${title} - ${t('ada|Opens in the current Tab')}`}
        >
            <button className={s.card} style={categoryBackgroundImage}>
                <div className={s.image}>
                    <img
                        alt={description}
                        height={400}
                        src={file.url}
                        width={400}
                    />
                </div>
                <h3 className={s.title}>{title}</h3>
            </button>
        </Link>
    )
}

export default Category
