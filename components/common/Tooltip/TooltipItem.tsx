import React from 'react'
import type { VFC } from 'react'
import cn from 'classnames'
import s from '@components/common/SmartHome/SmartHome.module.scss'
import type { ITooltip } from '@components/common/Tooltip/Tooltip'

type TToolTip = ITooltip

export interface ITooltipItem<T> {
    onClick: (content: T) => void
    content: T
    title: string
    top: string
    left: string
    className?: string
}

export const TooltipItem: VFC<ITooltipItem<TToolTip>> = ({
    onClick,
    content,
    title,
    top,
    left,
    className
}) => {
    return (
        <div
            title={title}
            style={{
                left: `${left}`,
                top: `${top}`
            }}
            className={cn(
                s['smart-home-dot-item'],
                'flex absolute opacity-100 z-50',
                className
            )}
        >
            <button
                className="w-6 h-6 border-white border border-solid border1 rounded-full bg-transparent flex items-center justify-center cursor-pointer opacity-100"
                onClick={() => onClick(content)}
            />
        </div>
    )
}
