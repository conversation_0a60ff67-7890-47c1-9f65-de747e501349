import { buildImageURLWithLayers } from '@components/common/PSDStateProvider/psdUtils'
import { useContext, useEffect, useState } from 'react'
import { PSDStateContext } from './PSDStateContext'

export const usePSDSource = (src: string, customLayers?: string[]): string => {
    const { layers, buildImageURL } = useContext(PSDStateContext)
    const [actualSrc, setActualSrc] = useState(src)

    const layersToUse = customLayers ?? layers

    useEffect(() => {
        const preloadImage = async () => {
            if (src.endsWith('.psd')) {
                if (!layersToUse?.length) {
                    setActualSrc('')
                    return
                }

                // Try loading with all layers first
                const withAllLayers = buildImageURLWithLayers(src, layersToUse)

                const img = new Image()
                try {
                    await new Promise((resolve, reject) => {
                        img.onload = resolve
                        img.onerror = reject
                        img.src = withAllLayers
                    })
                    setActualSrc(withAllLayers)
                    return
                } catch (e) {
                    // If loading with all layers fails, try with just base layer
                    const justBaseLayer = buildImageURL(src)
                    try {
                        await new Promise((resolve, reject) => {
                            img.onload = resolve
                            img.onerror = reject
                            img.src = justBaseLayer
                        })
                        setActualSrc(justBaseLayer)
                        return
                    } catch (e) {
                        // If both fail, return empty string
                        setActualSrc('')
                    }
                }
            } else {
                // For non-PSD files, use normal URL building
                setActualSrc(
                    layersToUse
                        ? buildImageURLWithLayers(src, layersToUse)
                        : buildImageURL(src)
                )
            }
        }

        preloadImage()
    }, [src, layersToUse, buildImageURL])

    if (src.endsWith('.psd')) {
        console.log('actualSrc', actualSrc)
    }

    return actualSrc
}
