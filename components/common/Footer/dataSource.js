export const footerSocialMedia = {
    data: {
        cmsBlocks: {
            items: [
                {
                    content:
                        '<div data-content-type="row" data-appearance="contained" data-element="main" class="footerSocialMedia"><div data-enable-parallax="0" data-parallax-speed="0.5" data-background-images="{}" data-element="inner" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px;"><div class="socialmedia-icons" data-content-type="text" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px;"><ul class="footer-sm-links">\r\n<li><a title="Facebook" href="#"><img src="https://fwrd.corrademo.com/media/wysiwyg/facebook.png" alt="Facebook" width="32px" height="32px"></a></li>\r\n<li><a title="Instagram" href="#"><img src="https://fwrd.corrademo.com/media/wysiwyg/instagram.png" width="32px" height="32px" alt="Instagram"></a></li>\r\n<li><a title="Twitter" href="#"><img src="https://fwrd.corrademo.com/media/wysiwyg/twitter.png" width="32px" height="32px" alt="Twitter"></a></li>\r\n<li><a title="Pinterest" href="#"><img src="https://fwrd.corrademo.com/media/wysiwyg/pinterest.png" width="32px" height="32px" alt="Pinterest"></a></li>\r\n<li><a title="Snapchat" href="#"><img src="https://fwrd.corrademo.com/media/wysiwyg/snapchat.png" width="32px" height="32px" alt="Snapchat"></a></li>\r\n</ul></div></div></div>',
                    identifier: 'footer-social-media',
                    title: 'Footer Social Media'
                }
            ]
        }
    }
}
export const footerCopyrightBlock = {
    data: {
        cmsBlocks: {
            items: [
                {
                    content:
                        '<div data-content-type="row" data-appearance="contained" data-element="main" class="footerCopyrightBlock"><div data-enable-parallax="0" data-parallax-speed="0.5" data-background-images="{}" data-element="inner" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px;"><div class="footer-bottom hide-only-in-desktop" data-content-type="text" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px;"><p class="footer-logo"><img src="/PylotSymbol.svg"></p>\r\n<ul class="privacy-terms">\r\n<li class="copy-right">© PYLOT 2021 | All Right Reserved</li>\r\n<li><a href="#" target="_blank" rel="noopener">Terms of Service</a> | <a href="#" target="_blank" rel="noopener">Privacy Policy</a></li>\r\n</ul></div></div></div>',
                    identifier: 'footer-copyright-block',
                    title: 'Footer Copyright Block'
                }
            ]
        }
    }
}
export const footerLinks = {
    data: {
        cmsBlocks: {
            items: [
                {
                    content:
                        '<div data-content-type="row" data-appearance="contained" data-element="main" class="footerLinks"><div data-enable-parallax="0" data-parallax-speed="0.5" data-background-images="{}" data-background-type="image" data-video-loop="true" data-video-play-only-visible="true" data-video-lazy-load="true" data-video-fallback-src="" data-element="inner" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px;"><div class="pagebuilder-column-group" style="display: flex;" data-content-type="column-group" data-grid-size="12" data-element="main"><div class="pagebuilder-column" data-content-type="column" data-appearance="full-height" data-background-images="{}" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; width: 50%; align-self: stretch;"><div data-content-type="text" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px;"><h3>ABOUT</h3></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="/about-us" target="" data-link-type="default" data-element="link"><span data-element="link_text">Our Story</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="/storelocator" target="" data-link-type="default" data-element="link"><span data-element="link_text">Store Locator</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="#" target="_blank" data-link-type="default" data-element="link"><span data-element="link_text">Press</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="#" target="_blank" data-link-type="default" data-element="link"><span data-element="link_text">Careers</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="#" target="_blank" data-link-type="default" data-element="link"><span data-element="link_text">Sustain</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="#" target="_blank" data-link-type="default" data-element="link"><span data-element="link_text">Affiliate</span></a></div></div></div><div class="pagebuilder-column" data-content-type="column" data-appearance="full-height" data-background-images="{}" data-element="main" style="justify-content: flex-start; display: flex; flex-direction: column; background-position: left top; background-size: cover; background-repeat: no-repeat; background-attachment: scroll; border-style: none; border-width: 1px; border-radius: 0px; width: 50%; align-self: stretch;"><div data-content-type="text" data-appearance="default" data-element="main" style="border-style: none; border-width: 1px; border-radius: 0px;"><h3>SUPPORT</h3></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; flex-direction: column;"><div data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="/contact" target="" data-link-type="default" data-element="link"><span data-element="link_text">Contact Us</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="#" target="_blank" data-link-type="default" data-element="link"><span data-element="link_text">FAQs</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="/order-status" target="" data-link-type="default" data-element="link"><span data-element="link_text">Orders and Returns</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="/customer-service" target="" data-link-type="default" data-element="link"><span data-element="link_text">Custom Service</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="/privacy-policy-cookie-restriction-mode" target="" data-link-type="default" data-element="link"><span data-element="link_text">Privacy Policy</span></a></div></div><div data-content-type="buttons" data-appearance="stacked" data-same-width="false" data-element="main" class="about-footer-link" style="border-style: none; border-width: 1px; border-radius: 0px; display: flex; margin: 0px; padding: 0px; flex-direction: column;"><div class="about-footer-link" data-content-type="button-item" data-appearance="default" data-element="main" style="display: inline-block;"><a class="pagebuilder-button-link" href="/catalogsearch/advanced/" target="" data-link-type="default" data-element="link"><span data-element="link_text">Advanced Search</span></a></div></div></div></div></div></div>',
                    identifier: 'footer_links',
                    title: 'Footer Links'
                }
            ]
        }
    }
}
