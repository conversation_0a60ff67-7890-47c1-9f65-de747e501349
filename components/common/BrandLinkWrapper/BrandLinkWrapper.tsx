import React from 'react'
import s from './BrandLinkWrapper.module.scss'
import {
    ImageLinkType,
    useContentJson
} from '@pylot-data/hooks/contentful/use-content-json'
import Skeleton from 'react-loading-skeleton'
import { useTranslation } from 'next-i18next'

const CONTENT_IDENTIFIER = ['header-brand-list']
const CONTENT_TYPE = 'brandLinks'

interface BrandLinks {
    title: string
    indentifier: string
    brandLogoList: ImageLinkType[]
}

const BrandLinkWrapper = (): JSX.Element | null => {
    const { t } = useTranslation(['common'])
    const { data } = useContentJson<BrandLinks>(
        {
            identifier: CONTENT_IDENTIFIER,
            contentType: CONTENT_TYPE
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true
        }
    )

    if (!data || !data.length) {
        return <Skeleton height={44} />
    }

    const { brandLogoList: logos } = data[0].parsedEntries

    return (
        <div className={s['brands-container']}>
            {logos.map((logo: ImageLinkType, index: number) => {
                return (
                    <div key={index}>
                        <a
                            href={logo.url}
                            target={logo.newTab ? '_blank' : '_self'}
                            rel="noreferrer"
                            style={{
                                width: logo.image.file.details.image.width,
                                height: logo.image.file.details.image.height
                            }}
                            aria-label={
                                logo.newTab
                                    ? t('ada|Opens in a new Tab')
                                    : t('ada|Opens in the current Tab')
                            }
                        >
                            <img
                                src={logo.image.file.url}
                                alt={logo.title}
                                width={logo.image.file.details.image.width}
                                height={logo.image.file.details.image.height}
                            />
                        </a>
                    </div>
                )
            })}
        </div>
    )
}

export default BrandLinkWrapper
