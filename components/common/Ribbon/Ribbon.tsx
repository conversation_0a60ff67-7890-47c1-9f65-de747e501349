import { FC } from 'react'
import cn from 'classnames'
import s from './Ribbon.module.scss'
import {
    useContentJson,
    ImageType,
    URLType
} from '@pylot-data/hooks/contentful/use-content-json'

const CONTENT_IDENTIFIER = ['ribbons']
const CONTENT_TYPE = 'ribbon'

interface RibbonData {
    active: boolean
    desktop: boolean
    mobile: boolean
    textsAndLinks: URLType[]
    background: ImageType
}

interface RibbonsData {
    ribbons: RibbonData[]
}

const Ribbon: FC = (): JSX.Element | null => {
    const { data } = useContentJson<RibbonsData>(
        {
            identifier: CONTENT_IDENTIFIER,
            contentType: CONTENT_TYPE,
            options: {
                level: 10
            }
        },
        {
            revalidateOnFocus: false,
            revalidateOnMount: true
        }
    )

    if (!Array.isArray(data) || !data.length) {
        return null
    }

    const ribbons = data[0].parsedEntries?.ribbons

    const ribbon = ribbons.find((currentRibbon) => currentRibbon.active)

    if (!ribbon) {
        return null
    }

    return (
        <div
            className={cn(
                s['ribbon'],
                ribbon.desktop && s['show-desktop'],
                ribbon.mobile && s['show-mobile']
            )}
            style={{
                backgroundImage: `url(${ribbon?.background?.file.url})`
            }}
        >
            {ribbon?.textsAndLinks?.map((link) => (
                <a
                    className={cn(s.link)}
                    style={{ color: link.color }}
                    key={link.text}
                    href={link.url}
                    target={`_${link.target.toLocaleLowerCase()}`}
                >
                    <span>{link.text}</span>
                    {link.icon && (
                        <span
                            className={s['icon']}
                            style={{
                                backgroundColor: `${link.icon.color}`,
                                maskImage: `url(${link.icon.image.file.url})`,
                                WebkitMaskImage: `url(${link.icon.image.file.url})`
                            }}
                        />
                    )}
                </a>
            ))}
        </div>
    )
}

export default Ribbon
