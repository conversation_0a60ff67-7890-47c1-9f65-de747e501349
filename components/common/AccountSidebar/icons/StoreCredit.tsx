import React from 'react'

const StoreCredit = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="22"
            height="20"
            viewBox="0 0 22 20"
        >
            <g
                fill="none"
                fillRule="evenodd"
                stroke="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1"
            >
                <g stroke="currentColor" transform="translate(-103 -565)">
                    <g transform="translate(-1)">
                        <g transform="translate(83 107)">
                            <g transform="translate(22 459)">
                                <g strokeWidth="2">
                                    <path d="M2 0h16a2 2 0 012 2v6c0 5.523-4.477 10-10 10S0 13.523 0 8V2a2 2 0 012-2z" />
                                    <path d="M6 7L10 11 14 7" />
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    )
}

export default React.memo(StoreCredit)
