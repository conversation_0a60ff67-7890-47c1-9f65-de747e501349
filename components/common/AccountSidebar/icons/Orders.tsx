import React from 'react'

const Orders = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="21"
            height="23"
            viewBox="0 0 21 23"
        >
            <g
                fill="none"
                fillRule="evenodd"
                stroke="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1"
            >
                <g stroke="currentColor" transform="translate(-104 -180)">
                    <g transform="translate(-1)">
                        <g transform="translate(83 107)">
                            <g transform="translate(23 74)">
                                <g strokeWidth="2">
                                    <path d="M13.596 7.43L4.596 2.24" />
                                    <path d="M18.096 14.03v-8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4a2 2 0 00-1 1.73v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4a2 2 0 001-1.73z" />
                                    <path d="M0.366 4.99L9.096 10.04 17.826 4.99" />
                                    <path d="M9.096 20.11L9.096 10.03" />
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    )
}

export default React.memo(Orders)
