import React from 'react'

const Contract = () => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="22"
            viewBox="0 0 18 22"
        >
            <g
                fill="none"
                fillRule="evenodd"
                stroke="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="1"
            >
                <g stroke="currentColor" transform="translate(-105 -699)">
                    <g transform="translate(-1)">
                        <g transform="translate(83 107)">
                            <g transform="translate(24 593)">
                                <g strokeWidth="2">
                                    <path d="M12 2h2a2 2 0 012 2v14a2 2 0 01-2 2H2a2 2 0 01-2-2V4a2 2 0 012-2h2" />
                                    <rect
                                        width="8"
                                        height="4"
                                        x="4"
                                        y="0"
                                        rx="1"
                                    />
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </svg>
    )
}

export default React.memo(Contract)
