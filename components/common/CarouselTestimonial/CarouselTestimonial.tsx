import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import cn from 'classnames'
import { ReactChild, ReactFragment, ReactPortal, useState } from 'react'
import Slider from 'react-slick'
import { Arrow } from '../CarouselProductTiles/Arrows'
import s from './CarouselTestimonial.module.scss'
import Slide from './Slide'

export type CSLTestimonialObject = {
    identifier: string
    subheading: string
    text: string
    logo: ImageType
    mainImage: ImageType
}

export type CslContainer = {
    identifier: string
    productSlide: CSLTestimonialObject[]
}

export type CarouselProps = {
    content: CslContainer
}

type CslData = CSLTestimonialObject[]

const CarouselTestimonial = ({ content: carouselContainer }: CarouselProps) => {
    const [currentSlider, setCurrentSlider] = useState<number>(1)

    if (!carouselContainer) return null
    const productSlides: CslData = carouselContainer.productSlide
    const renderCustomDots = (dots: React.ReactNode): JSX.Element => {
        const sliderLength = productSlides.length
        const currentActive = currentSlider === productSlides.length

        return (
            <div className={s['dot-container']}>
                <label className="opacity-100 text-white font-bold">
                    {currentSlider}
                </label>
                <ul
                    className={cn(
                        s['product-carousel-custom-dots'],
                        'w-full flex items-center justify-center'
                    )}
                >
                    {dots}
                </ul>
                <label
                    className={`text-white font-bold ${
                        currentActive ? 'opacity-100' : 'opacity-50'
                    }`}
                >
                    {sliderLength}
                </label>
            </div>
        )
    }

    const defaultSettings = {
        autoplay: false,
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        arrows: true,
        draggable: true,
        afterChange: (current: number) => setCurrentSlider(current + 1),
        prevArrow: <Arrow direction="left" s={s} />,
        nextArrow: <Arrow direction="right" s={s} />,
        appendDots: (
            dots:
                | boolean
                | ReactChild
                | ReactFragment
                | ReactPortal
                | null
                | undefined
        ) => renderCustomDots(dots)
    }

    return (
        <div>
            <Slider {...defaultSettings} className={s['slider']}>
                {productSlides.map((prod: any) => {
                    return <Slide cslData={prod} key={prod.identifier} />
                })}
            </Slider>
        </div>
    )
}

export default CarouselTestimonial
