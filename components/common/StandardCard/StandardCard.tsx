import Image from '@corsairitshopify/corsair-image'
import { VFC } from 'react'
import { IStandardCard } from '../types'

import { getThemeFromContent } from '@lib/getThemeFromContent'
import s from './StandardCard.module.scss'

export type StandardCardProps = {
    card: IStandardCard
}

const c = /*tw*/ {
    standardCard: `${s['standard-card']} mx-auto`,
    standardCardImage: `${s['standard-card-image']} relative`,
    standardCardText: `${s['standard-card-text']}`,
    standardCardHeading: `${s['standard-card-heading']} uppercase`
}

const StandardCard: VFC<StandardCardProps> = ({ card }) => {
    const theme = getThemeFromContent(card.theme)
    return (
        <div className={c.standardCard} style={{ ...theme }}>
            <div className="flex items-end w-full justify-between">
                <p className={c.standardCardHeading}>{card.heading}</p>
                <div className={c.standardCardImage}>
                    <Image
                        src={card.image.image.file.url}
                        layout="fill"
                        alt={card.image.image.description || ''}
                    />
                </div>
            </div>

            <p className={c.standardCardText}>{card.text}</p>
        </div>
    )
}

export default StandardCard
