import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'
import { VFC } from 'react'
import ProductItem from '../ProductItem'
import s from './FullCard.module.scss'
import { FullCardProps } from './types'

const c = /*tw*/ {
    fullCard: `${s['full-card']} relative mb-5 mx-auto`,
    fullCardContent: `${s['full-card-content']} w-full h-full absolute flex flex-col`,
    fullCardText: `${s['full-card-text']} text-white text-base flex-1`,
    fullCardHeading: `${s['full-card-heading']} text-white uppercase`,
    fullCardGrid: `${s['full-card-grid']}`
}

const cardElementId = 'full-card-text'

const FullCard: VFC<FullCardProps> = ({ card }) => {
    const hasProducts = Boolean(card.productCards)
    return (
        <div className={c.fullCard}>
            <Image
                src={card.backgroundImage?.image.file.url}
                layout="fill"
                alt={card.backgroundImage.image.description || ''}
            />
            <div
                className={cn(c.fullCardContent, {
                    [s['full-card-empty']]: !hasProducts
                })}
            >
                <p className={c.fullCardHeading}>{card.heading}</p>
                <p className={c.fullCardText} id={cardElementId}>
                    {card.text}
                </p>
                <div className={c.fullCardGrid}>
                    {card.productCards?.map((product, index) => (
                        <ProductItem
                            product={product}
                            key={product.title + index}
                        />
                    ))}
                </div>
            </div>
        </div>
    )
}

export default FullCard
