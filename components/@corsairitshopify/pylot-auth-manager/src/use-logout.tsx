import { useAccount } from './use-account'
import { useCart, createCart } from '@lib/cart-manager'
import { useRouter } from 'next/router'
import { getIsSignedIn } from './utils/getIsSignedIn'
import { removeCartIdCookie } from './utils/removeCartIdCookie'
import { clearCartIdRegionBackup } from 'helpers'

type UseLogoutReturn = { logout: () => Promise<void> }

export const useLogout = (): UseLogoutReturn => {
    const { locale } = useRouter()
    const { mutate: updateAccount } = useAccount()
    const { mutate: fetchNewCart } = useCart()

    return {
        logout: async () => {
            const isSignedIn = getIsSignedIn()
            if (isSignedIn) {
                window.localStorage.removeItem('pylot_token')
                window.localStorage.removeItem('cart_id')
                removeCartIdCookie()
                clearCartIdRegionBackup()
                // Delete customer data from SWR cache without triggering an unnecessary GQL query
                updateAccount({ data: { customer: undefined } }, false)
                await createCart({ locale })
                fetchNewCart()
            }
        }
    }
}
