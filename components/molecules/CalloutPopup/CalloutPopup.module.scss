.callout-popup {
    background: rgba(1, 1, 1, 0.7);
    backdrop-filter: blur(10px);
    @apply opacity-0 rounded-xxl p-24px flex gap-8 items-start text-white;
    @apply transition transition-opacity duration-150 -z-1;
    max-width: 343px;
    max-height: calc(100vh - 102px);
    overflow-y: auto;
    pointer-events: none;

    @screen md {
        max-width: 436px;
    }

    &--light {
        background: rgba(51, 51, 51, 0.7);
    }

    &--white {
        background: rgba(255, 255, 255, 0.9);
        @apply text-charcoal;
    }

    &--open {
        @apply opacity-100 z-10;
        pointer-events: auto;
    }

    &--positioned {
        @apply absolute;

        @screen md-max {
            top: 10% !important;
            left: 16px !important;
            right: 16px !important;
            transform: translate(0, -50%) !important;
        }
    }
}
