import React, {
    FC,
    HTMLAttributes,
    ReactNode,
    useEffect,
    useRef,
    useState
} from 'react'

import cn from 'classnames'
import s from './TablePopup.module.scss'
import PopupCloseIcon from '@components/atoms/Icon/general/PopupCloseIcon'
import { useTranslation } from 'next-i18next'
import {
    Container,
    ContainerSize
} from '@components/organisms/Container/Container'
import { Overlay } from '@components/organisms/Overlay/Overlay'

export type TablePopupProps = {
    position?: HTMLAttributes<HTMLSelectElement>['style']
    className?: string
    onClose?: () => void

    children?: ReactNode
    color?: 'dark' | 'light' | 'white' | 'transparent'
    open?: boolean
    closeClickOutside?: boolean
    richText?: string
    toolTip?: string
}

export const TablePopup: FC<TablePopupProps> = (props) => {
    const {
        position,
        className,
        onClose,
        color = 'dark',
        children,
        open = true,
        closeClickOutside = false,
        richText
    } = props
    const { t } = useTranslation(['common'])
    const wrapper = useRef<HTMLDivElement | null>(null)
    const [currentPosition, setCurrentPosition] = useState<
        HTMLAttributes<HTMLSelectElement>['style']
    >(position)

    // attach close click outside logic
    useEffect(() => {
        if (!open || !closeClickOutside) {
            return
        }
        const clickOutsideHandler = (event: MouseEvent) => {
            // check if clicked on the popup itself
            if (wrapper.current) {
                const rect = wrapper.current.getBoundingClientRect()
                if (
                    rect.left <= event.clientX &&
                    event.clientX <= rect.right &&
                    rect.top <= event.clientY &&
                    event.clientY <= rect.bottom
                ) {
                    return
                }
            }

            onClose?.()
        }

        window.addEventListener('click', clickOutsideHandler)

        return () => window.removeEventListener('click', clickOutsideHandler)
    }, [closeClickOutside, onClose, open])

    // repositioning logic if the popup extends the boundaries to the right
    useEffect(() => {
        setCurrentPosition(position)
        // delay by one frame to let rendering settle in
        setTimeout(() => {
            if (
                !wrapper.current ||
                !wrapper.current.parentElement ||
                !position?.left
            ) {
                return
            }
            const { left, ...other } = position
            const rect = wrapper.current.getBoundingClientRect()
            const parentRect = wrapper.current.parentElement.getBoundingClientRect()
            // only reposition if the right boundary of the popup reaches its parent right boundary
            // and if on the position is actually more to the right of the parents container
            if (
                rect.right >= parentRect.right &&
                Math.abs(parentRect.left - rect.left) >
                    Math.abs(parentRect.right - rect.left)
            ) {
                setCurrentPosition({
                    ...other,
                    right: `calc(100% - ${left})`
                })
            }
        })
    }, [])

    return (
        <Overlay
            // eslint-disable-next-line i18next/no-literal-string
            theme="black"
            isOpen={open}
            closable
            // eslint-disable-next-line i18next/no-literal-string
            closeButtonStyle="default"
            onClose={onClose}
            className={cn('overflow-hidden')}
        >
            <div className={cn('mx-5 flex items-center justify-center')}>
                <div
                    className={cn(
                        s['table-popup'],
                        s[`table-popup--${color}`],
                        {
                            [s['table-popup--open']]: open,
                            [s['table-popup--positioned']]: currentPosition
                        },
                        className
                    )}
                    style={currentPosition}
                >
                    <div
                        className={cn(
                            'flex flex-row gap-10 p-7 pt-1.5 bg-primitive-gray-120'
                        )}
                    >
                        <div className={cn('md-max:hidden')}>
                            <button
                                tabIndex={0}
                                onClick={onClose}
                                onKeyPress={onClose}
                                aria-label={t('Close')}
                            >
                                <PopupCloseIcon
                                    style={{ width: '32px', height: '32px' }}
                                />
                            </button>
                        </div>

                        {richText && (
                            <div
                                className="font-univers55Roman"
                                style={{ fontSize: '20px' }}
                                dangerouslySetInnerHTML={{
                                    __html: richText
                                }}
                            />
                        )}
                    </div>
                    <div className={cn('body-small rich-text px-5')}>
                        {/* eslint-disable-next-line i18next/no-literal-string */}
                        {children}
                    </div>
                </div>
            </div>
        </Overlay>
    )
}
