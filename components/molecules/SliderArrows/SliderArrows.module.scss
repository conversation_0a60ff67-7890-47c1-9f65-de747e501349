.slider-button-wrapper {

    z-index: 100;
    display: flex;
    justify-content: flex-end;
    gap: 1.6rem;
    width: auto;
    margin-top: 64px;

    :global {
        .swiper-button-disabled {
            display: block !important;
            pointer-events: none;

            span {
                background-color: var(--Grey<PERSON>le-Black-20, #CCC);
                ;
            }
        }
    }

    &__button {

        @screen md {
            width: 48px !important;
            height: 48px !important;
            padding: 10px !important;
        }

        @screen lg2 {
            width: 56px !important;
            height: 56px !important;
            padding: 14px !important;
        }


    }
}
