import React, { FC, ReactNode, useEffect } from 'react'
import cn from 'classnames'
import s from './ConsciousPackaging.module.scss'
import { Icon } from '@components/atoms/Icon/Icon'
import { useLayoutContext } from '@components/layouts/MainLayout/LayoutContext'
import { useTranslation } from 'next-i18next'
import { verifyAnchorTags } from 'helpers/AdaHelpers'

export type ConsciousPackagingProps = {
    icon?: string
    title?: string
    text?: string
    className?: string
    children?: ReactNode
}

export const ConsciousPackaging: FC<ConsciousPackagingProps> = (props) => {
    const { icon = 'elgatoEnvironment', title, children, className } = props
    const { pageTheme } = useLayoutContext()
    const { t } = useTranslation(['common'])
    useEffect(() => {
        verifyAnchorTags(
            t('ada|Opens in the current Tab'),
            `.${s['conscious-packaging__text']}`
        )
    }, [children, t])
    return (
        <div
            className={cn(
                s['conscious-packaging'],
                {
                    [s[`page-theme-${pageTheme}`]]: pageTheme
                },
                className
            )}
        >
            {icon && (
                <Icon name={icon} className={s['conscious-packaging__icon']} />
            )}
            {(title || children) && (
                <div className={cn(s['conscious-packaging__content'])}>
                    {title && (
                        <h5 className={s['conscious-packaging__title']}>
                            {title}
                        </h5>
                    )}
                    {children && (
                        <div
                            className={cn(
                                s['conscious-packaging__text'],
                                'xs-copy'
                            )}
                        >
                            {children}
                        </div>
                    )}
                </div>
            )}
        </div>
    )
}
