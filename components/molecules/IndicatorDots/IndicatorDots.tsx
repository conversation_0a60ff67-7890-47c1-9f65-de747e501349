// IndicatorDots.jsx
import React from 'react'
import cn from 'classnames'
import s from './IndicatorDots.module.scss'

interface IndicatorDotsProps {
    length: number
    activeIndex: number
    onDotClick: (index: number) => void
}

const IndicatorDots: React.FC<IndicatorDotsProps> = ({
    length,
    activeIndex,
    onDotClick
}) => {
    return (
        <div className={s['indicators']}>
            {Array.from({ length }, (_, index) => (
                <button
                    key={index}
                    className={cn(s['indicators-dots'], {
                        [s['indicators-dots__active']]: index === activeIndex
                    })}
                    onClick={() => onDotClick(index)}
                />
            ))}
        </div>
    )
}

export default IndicatorDots
