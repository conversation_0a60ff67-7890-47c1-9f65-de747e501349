import React, { FC, useRef } from 'react'
import cn from 'classnames'
import s from './HotspotPopup.module.scss'
import { Icon } from '@components/atoms/Icon/Icon'
import Image from '@corsairitshopify/corsair-image'
import { ImageType } from '@pylot-data/hooks/contentful/use-content-json'
import unescape from 'lodash.unescape'

export type HotspotPopupProps = {
    title?: string
    position?: string
    className?: string
    onClose?: () => void
    image?: ImageType
    text?: string
}

export const HotspotPopup: FC<HotspotPopupProps> = (props) => {
    const { title, position, className, onClose, image, text } = props
    const popupRef = useRef<HTMLDivElement | null>(null)
    if (position) {
        popupRef.current?.style.setProperty('position', position)
    }
    const icon = 'close'
    return (
        <div className={cn(s['hotspot-popup'], className)} ref={popupRef}>
            <div className={cn(s['hotspot-popup__header'])}>
                {title && (
                    <span
                        className={cn(s['hotspot-popup__title'], 'small-copy')}
                    >
                        {title}
                    </span>
                )}
                <button
                    className={cn(s['hotspot-popup__close'])}
                    type="button"
                    onClick={onClose}
                >
                    <Icon name={icon} className="w-16px h-16px" />
                </button>
            </div>
            {((image && image?.file?.url) || text) && (
                <div className={cn(s['hotspot-popup__content'], 'small-copy')}>
                    {image && image?.file?.url && (
                        <Image
                            src={image?.file?.url}
                            alt={image?.description || ''}
                            width={image.file.details?.image.width}
                            height={image.file.details?.image.height}
                        />
                    )}
                    <div
                        className="rich-text"
                        dangerouslySetInnerHTML={{ __html: unescape(text) }}
                    />
                </div>
            )}
        </div>
    )
}
