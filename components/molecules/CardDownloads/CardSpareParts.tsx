import React, { FC } from 'react'
import s from './CardDownloads.module.scss'
import { Button } from '@components/molecules/Button/Button'
import { Icon } from '@components/atoms/Icon/Icon'
import { Link, LinkResponse } from '@components/molecules/Link/Link'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
import { SwiperSlide } from 'swiper/react'
import { SwiperSliderTheme } from '@components/organisms/Slider/SwiperSlider'
import { A11y, Navigation, SwiperOptions } from 'swiper'
import classNames from 'classnames'
import { FeatureProps } from '@components/templates/FeatureList/FeatureList'
import {
    DownloadModal,
    useDownloadModal
} from '@components/organisms/DownloadModal/DownloadModal'
import CardList, {
    CardListProps
} from '@components/templates/CardList/CardList'
import { pushToDataLayer } from '@corsairitshopify/pylot-gtm/src/utils'
import { getNewsletterSignupEvent } from '@lib/gtm/newsletterSignup'
import { getSmsSignupEvent } from '@lib/gtm/smsSignup'
import { getDownloadSoftwareEvent } from '@lib/gtm/downloadSoftware'
import Image from '@corsairitshopify/corsair-image'
import cn from 'classnames'

const SwiperSlider = dynamic(
    () => import('@components/organisms/Slider/SwiperSlider'),
    {
        ssr: false
    }
)

export interface CardSparePartsProps {
    compatibleText: string
    compatibleActions?: LinkResponse[]
    additionalLinks?: FeatureProps[]
    downloadAction?: LinkResponse
    modalHeadline?: JSX.Element
    modalContent?: CardListProps
    disclaimerText?: string
    extraLinks?: LinkResponse[]
    requirement?: string
    betaLink?: LinkResponse
}

export const CardSpareParts: FC<CardSparePartsProps> = ({
    compatibleText,
    compatibleActions,
    additionalLinks,
    downloadAction,
    extraLinks,
    requirement,
    betaLink
}) => {
    const { t } = useTranslation(['common'])
    const modalState = useDownloadModal()
    const sliderSettings: SwiperOptions = {
        slidesPerView: 'auto',
        centeredSlides: true,
        centerInsufficientSlides: true,
        centeredSlidesBounds: true,
        spaceBetween: 8,
        loop: false,
        allowTouchMove: true,
        simulateTouch: true,
        modules: [A11y, Navigation]
    }

    const requirementAdditionalLink = additionalLinks
        ? additionalLinks.find((feature) => feature.label === 'requirement')
        : null
    return (
        <div
            className={classNames(
                'block items-center flex-col flex-grow',
                s['compatible-products']
            )}
        >
            <>
                {compatibleActions && compatibleActions.length > 0 && (
                    <div className="my-16px flex flex-col items-center w-full">
                        <div className="mb-8px">{compatibleText}</div>
                        <SwiperSlider
                            loop={false}
                            className="w-full px-24"
                            settings={sliderSettings}
                            navigationTheme={SwiperSliderTheme.LIGHT}
                            buttonClassPre={
                                s['compatible-products__swiper-btn-pre']
                            }
                            buttonClassNext={
                                s['compatible-products__swiper-btn-next']
                            }
                        >
                            {compatibleActions.map((action) => (
                                <SwiperSlide
                                    key={action.linkTitle}
                                    className={
                                        s['compatible-products__swiper-slide']
                                    }
                                >
                                    <Button
                                        variant="compatible-product"
                                        disabled={!action.linkUrl}
                                        href={action.linkUrl}
                                        className="box-border"
                                        label={action.linkTitle}
                                    >
                                        {action.linkTitle}
                                    </Button>
                                </SwiperSlide>
                            ))}
                        </SwiperSlider>
                    </div>
                )}
            </>
        </div>
    )
}

export default CardSpareParts
