import React, { FC } from 'react'
import cn from 'classnames'
import s from './SalesLabel.module.scss'
import unescape from 'lodash.unescape'

export type SalesLabelProps = {
    className?: string
    textClassName?: string
    background?: string
    mode?: 'default' | 'dark'
    backgroundImage?: string
    price?: string
    oldPrice?: string
    discount?: string
}

export const SalesLabel: FC<SalesLabelProps> = (props) => {
    const {
        mode = 'default',
        className,
        textClassName,
        price,
        oldPrice,
        discount
    } = props
    let textClass = mode === 'default' ? 'default' : 'dark'
    if (textClassName) {
        textClass += ` ${textClassName}`
    }
    const backgroundStyle = props.background
        ? { background: props.background }
        : props.backgroundImage
        ? {
              backgroundImage: `url(${props.backgroundImage}); background-position: center; background-size: cover;`
          }
        : {}

    return (
        <div className={cn(s['sales-label'], s[`sales-label--${mode}`])}>
            <div
                className={cn(s['sales-label__background'], className)}
                style={backgroundStyle}
            />
            <span className={cn(s['sales-label__text-container'], textClass)}>
                {oldPrice && <h5 className="line-through">{oldPrice}</h5>}
                {price && <h3>{price}</h3>}
                {discount && (
                    <h5
                        // needed for French translations
                        dangerouslySetInnerHTML={{ __html: unescape(discount) }}
                    />
                )}
            </span>
        </div>
    )
}
