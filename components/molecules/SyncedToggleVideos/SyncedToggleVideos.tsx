import { FC, useEffect, useRef, useState } from 'react'

import { CloudinaryMedia } from '@components/common/CloudinaryMedia/Cloudinary'
import { Toggle } from '@components/atoms/Toggle/Toggle'
import { Icon } from '@components/atoms/Icon/Icon'
import cn from 'classnames'
import { useMobile } from '@pylot-data/hooks/use-mobile'
import s from './SyncedToggleVideos.module.scss'
type SyncedToggleVideosProps = {
    video1: CloudinaryMedia
    video2: CloudinaryMedia
    toggleText?: string
}

export const SyncedToggleVideos: FC<SyncedToggleVideosProps> = ({
    video1,
    video2,
    toggleText
}) => {
    const { isTablet } = useMobile()
    const [activeVideo, setActiveVideo] = useState(0)
    const [fullMuted, setFullMuted] = useState(true)
    const [isPlaying, setIsPlaying] = useState(false)
    const video1Ref = useRef<HTMLVideoElement>(null)
    const video2Ref = useRef<HTMLVideoElement>(null)

    useEffect(() => {
        const video1 = video1Ref.current
        const video2 = video2Ref.current

        if (video1 && video2) {
            video1.play()
            video2.play()
            setIsPlaying(true)
        }
    }, [video1Ref, video2Ref])

    const onSwitchVideo = (video: number) => {
        setActiveVideo(video)
        setFullMuted(false)
    }

    const togglePlayPause = () => {
        const video1 = video1Ref.current
        const video2 = video2Ref.current

        if (video1 && video2) {
            if (isPlaying) {
                video1.pause()
                video2.pause()
            } else {
                video1.play()
                video2.play()
            }
            setIsPlaying(!isPlaying)
        }
    }

    return (
        <div
            className={cn(
                s['synced-toggle-videos'],
                'relative h-full w-full z-10'
            )}
        >
            <div
                className={cn(
                    s['synced-toggle-videos__controls'],
                    'absolute bottom-24px left-1/2 flex justify-center items-center z-1 bg-white rounded-xl p-8px px-16px gap-16px lg:gap-40px'
                )}
                style={{
                    WebkitTransform: 'translateX(-50%) translateZ(0)'
                }}
            >
                <div className="flex items-center gap-8px lg:gap-16px">
                    <div
                        className="cursor-pointer hover:text-primitive-blue-90"
                        onClick={togglePlayPause}
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                                togglePlayPause()
                            }
                        }}
                    >
                        <Icon
                            name={isPlaying ? 'pause' : 'play'}
                            className={cn({
                                'w-24px h-24px': !isTablet,
                                'w-16px h-16px': isTablet
                            })}
                        />
                    </div>
                    <div
                        className={cn('whitespace-nowrap', {
                            'font-bold': activeVideo === 1,
                            'text-h6-md-max': isTablet
                        })}
                    >
                        {toggleText}
                    </div>
                </div>
                <Toggle
                    checked={activeVideo === 1}
                    onChange={() => onSwitchVideo(activeVideo === 1 ? 0 : 1)}
                    color="primary"
                    size={isTablet ? 'small' : 'default'}
                />
            </div>
            <video
                ref={video1Ref}
                src={video1.secure_url}
                loop
                autoPlay={false}
                playsInline
                muted={fullMuted || activeVideo !== 0}
                className={cn('h-full w-full', {
                    'opacity-0': activeVideo !== 0
                })}
            >
                <track kind="captions" />
                <source src={video1.secure_url} type="video/mp4" />
            </video>
            <video
                ref={video2Ref}
                src={video2.secure_url}
                loop
                playsInline
                autoPlay={false}
                muted={fullMuted || activeVideo !== 1}
                className={cn('absolute inset-0 h-full w-full', {
                    'opacity-0': activeVideo !== 1
                })}
            >
                <track kind="captions" />
                <source src={video2.secure_url} type="video/mp4" />
            </video>
        </div>
    )
}
