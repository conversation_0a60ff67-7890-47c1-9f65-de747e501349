import s from './SmallTextIconGroup.module.scss'

import { FC } from 'react'
import cn from 'classnames'
import {
    SmallTextIcon,
    SmallTextIconProps
} from '@components/atoms/SmallTextIcon/SmallTextIcon'
import { HorizontalAlignmentEnum } from '@components/molecules/PrimaryText/PrimaryText'

export type SmallTextIconGroupProps = {
    smallTextIcons?: SmallTextIconProps[]
    text?: string
    headline?: string
    textColor?: 'dark' | 'light'
    textAlignment?: HorizontalAlignmentEnum
    className?: string
    nOfRowsDesktop?: number
    nOfRowsMobile?: number
}
export const SmallTextIconGroup: FC<SmallTextIconGroupProps> = (props) => {
    const {
        className,
        smallTextIcons,
        nOfRowsDesktop = 3,
        nOfRowsMobile = 3
    } = props
    return (
        <div
            className={cn(s['small-text-icon-group'], {
                'grid-rows-1': nOfRowsMobile === 1,
                'grid-rows-2': nOfRowsMobile === 2,
                'grid-rows-3': nOfRowsMobile === 3,
                'grid-rows-4': nOfRowsMobile === 4,
                'md:grid-rows-1': nOfRowsDesktop === 1,
                'md:grid-rows-2': nOfRowsDesktop === 2,
                'md:grid-rows-3': nOfRowsDesktop === 3,
                'md:grid-rows-4': nOfRowsDesktop === 4
            })}
        >
            {smallTextIcons &&
                smallTextIcons.map((textIcon, index) => (
                    <SmallTextIcon
                        key={index}
                        label={textIcon.label}
                        preHeadline={textIcon.preHeadline}
                        text={textIcon.text}
                        textColor={textIcon.textColor}
                        textAlignment={textIcon.textAlignment}
                        icon={textIcon.icon}
                        image={textIcon.image}
                        className={className}
                    />
                ))}
        </div>
    )
}

export default SmallTextIconGroup
