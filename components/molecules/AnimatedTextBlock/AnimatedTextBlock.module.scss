.text-block {
    align-self: stretch;
    padding-right: 25px;
    padding-left: 0;
    @media only screen and (max-width:767px) {
        padding-right: 20px;
    }

    &--light {
        @apply text-white;
    }

    &--small, &--xs {
        .text-block__title + .text-block__text {
            margin-top: 7px;

            @screen md {
                margin-top: 12px;
            }
        }

        * + .text-block__button {
            margin-top: 13px;

            @screen md {
                margin-top: 19px;
            }
        }
    }

    &--medium {
        .text-block__title + .text-block__text {
            @apply mt-4px;

            @screen md {
                @apply mt-12px;
            }
        }

        * + .text-block__button {
            @apply mt-18px;

            @screen md {
                @apply mt-24px;
            }
        }

        .text-block__text {
            font-weight: 400;
            font-size: 16px;
        }
    }

    &--large {
        .text-block__title + .text-block__text {
            @apply mt-12px;
        }

        * + .text-block__button {
            @apply mt-8;

            @screen md {
                @apply mt-24px;
            }
        }
    }

    &--alignment-center {
        @apply text-center;
    }

    &__text {
        a {
            display: block;
        }
    }
}
.animate-product-content {

    &_link {
      padding: 5px 7px;
      margin: 5px 10px 0 0px;
      opacity: 0;
      transition: all 0.4s ease-out;

      &.active {
        opacity: 1;
      }

      &.dark {
        background-color: #333;

        svg {
          color: #fff;
        }
      }

      &.light {
        background-color: #fff;

        svg {
          color: #333;
        }
      }
      svg {
        margin-top: 0 !important;
      }
    }

    &_content {
      transform: translateX(-55px);
      transition: all 0.3s ease-out;
      margin-bottom: 5px;

      &.active {
        transform: translateX(0);
      }
    }

    &__mobile {
        transform: translateX(0);
        transition: all 0.3s ease-out;
        //margin-bottom: 5px;
        &.active {
            transform: translateX(0);
        }
        @media only screen and(max-width:767px) {
            transform: translateX(0) !important;
        }
    }
    &__mobile-info, &__mobile-video {
        transform: translateX(-46px);
        transition: all 0.3s ease-out;
        &.active {
            transform: translateX(0);
        }
        @media only screen and(max-width:767px) {
            transform: translateX(-40px) !important;
        }
    }
  }
