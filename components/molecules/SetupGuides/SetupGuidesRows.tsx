import s from './SetupGuidesRows.module.scss'
import React, { FC, useCallback, useRef, useState } from 'react'
import cn from 'classnames'
import {
    SetupGuidesRow,
    SetupGuidesRowProps
} from '@components/molecules/SetupGuides/SetupGuidesRow'
import { Button } from '@components/molecules/Button/Button'
import { useTranslation } from 'next-i18next'
import dynamic from 'next/dynamic'
const SetupGuidesRowToggleContent = dynamic(
    () =>
        import('@components/molecules/SetupGuides/SetupGuidesRowToggleContent'),
    { ssr: false }
)

export type SetupGuidesRowsProps = {
    rows: SetupGuidesRowProps[]
    meta?: { contentType: 'templateSetupGuides' }
    className?: string
    linkLabel?: string
}

export const SetupGuidesRows: FC<SetupGuidesRowsProps> = (props) => {
    const { rows, className, linkLabel } = props
    const { t } = useTranslation(['common'])
    const [isOpen, setIsOpen] = useState(rows.length < 2)
    const wrapperRef = useRef<HTMLDivElement>(null)
    const showAll = useCallback(() => {
        setIsOpen(true)
    }, [])
    return (
        <div className={cn(s['setup-guides-rows'], className)}>
            {rows.length && (
                <div
                    ref={wrapperRef}
                    className={cn(s['setup-guides-rows__wrapper'], {
                        [s['setup-guides-rows__wrapper--open']]: isOpen
                    })}
                >
                    {rows.map((row, i) => {
                        if (i <= 2) {
                            return (
                                <SetupGuidesRow
                                    key={i}
                                    image={row.image}
                                    rowTitle={row.rowTitle}
                                    variant={
                                        i % 2 == 0 ? 'transparent' : 'light'
                                    }
                                    downloadUrl={row.downloadUrl}
                                    newTab={row.newTab}
                                    text={row.text}
                                    linkLabel={linkLabel}
                                    className={s['setup-guides-rows__row']}
                                />
                            )
                        }
                    })}
                    {rows.length > 3 && (
                        <SetupGuidesRowToggleContent
                            rows={rows}
                            linkLabel={linkLabel}
                            toggled={isOpen}
                        />
                    )}
                </div>
            )}
            {rows.length > 3 && !isOpen && (
                <div className={s['setup-guides-rows__footer']}>
                    <Button
                        variant="tertiary-underlined"
                        onClick={showAll}
                        onKeyPress={showAll}
                        className="text-content-blue"
                        label={t('view more')}
                    >
                        {t('view more')}
                    </Button>
                </div>
            )}
        </div>
    )
}
