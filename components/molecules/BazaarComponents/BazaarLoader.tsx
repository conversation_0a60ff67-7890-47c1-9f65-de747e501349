import { defaultLocale } from '@config/hooks/useStoreConfig'
import Head from 'next/head'
import { useRouter } from 'next/router'
import React from 'react'

export const SUPPORTED_BAZAARVOICE_LANGS = {
    en: 'en_US',
    es: 'es_ES',
    de: 'de_DE',
    fr: 'fr_FR',
    it: 'it_IT',
    nl: 'nl_NL',
    sv: 'sv_SE',
    pl: 'pl_PL'
}

const BazaarLoader = (): React.ReactElement => {
    const router = useRouter()
    const { locale = defaultLocale } = router
    const language =
        locale && locale.includes('-') ? locale.split('-')[0] : 'en'
    let env = 'production'
    const site = 'main_site'
    if (
        typeof window !== 'undefined' &&
        window.location.host &&
        (window.location.host.includes('staging') ||
            window.location.host.includes('dev') ||
            window.location.host.includes('localhost'))
    ) {
        env = 'staging'
    }
    let lang = 'en_US'
    if (Object.keys(SUPPORTED_BAZAARVOICE_LANGS).includes(language)) {
        // @ts-ignore
        lang = SUPPORTED_BAZAARVOICE_LANGS[language]
    }
    // https://apps.bazaarvoice.com/deployments/elgato/dev_site/staging/en_US/bv.js
    return (
        <Head>
            <script
                id="bazaarvoice"
                async
                src={`https://apps.bazaarvoice.com/deployments/elgato/${site}/${env}/${lang}/bv.js`}
            />
        </Head>
    )
}

export default React.memo(BazaarLoader)
