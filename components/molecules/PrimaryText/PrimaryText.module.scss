.primary-text {
    :global(.h1) {
        @media screen and (min-width: 768px) {
            font-size: 3.5vw;
        }

        @media screen and (min-width: 1920px) {
            font-size: 6.7rem;
        }
    }

    :global(.h2) {
        @media screen and (min-width: 768px) {
            font-size: 2.1vw;
        }

        @media screen and (min-width: 1920px) {
            font-size: 4.1rem;
        }
    }

    :global(.h3) {
        @media screen and (min-width: 768px) {
            font-size: max(1.6vw, 3.2rem);
        }

        @media screen and (min-width: 1920px) {
            font-size: 3.2rem;
        }
    }

    &__hidden {
        display: none;
    }

    &--text-center {
        @apply text-center mx-auto;

        .primary-text__footer,
        .primary-text__logos {
            @apply justify-center;
        }

        &--mobile {
            @screen md-max {
                @apply text-center mx-auto;

                .primary-text__footer,
                .primary-text__logos {
                    @apply justify-center;
                }
            }
        }
    }

    &--text-right {
        @apply text-right;

        .primary-text__footer,
        .primary-text__logos {
            @apply justify-end;
        }

        &--mobile {
            @screen md-max {
                @apply text-right;

                .primary-text__footer,
                .primary-text__logos {
                    @apply justify-end;
                }
            }
        }
    }

    &__wave-neo {
        .primary-text__body {
            @apply font-univers55Roman;
            color: var(--greyscale-black-50);
            margin-top: 3.6rem;
            line-height: 3.6rem;

            @screen md-max {
                font-size: 20px;
                margin-top: 2.4rem;
                line-height: 3.2rem;
            }

            @screen md {
                line-height: 3.6rem;
                font-size: 2.4rem;
            }

            @screen lg2 {
                margin-top: 4rem;
                line-height: 4.4rem;
                font-size: 28px;
            }
        }

        .primary-text__headline {
            @apply font-univers55Roman;
            // font-size: 32px;
            text-transform: initial;
            // line-height: 4.8rem;
            padding-top: 80px;

            @screen md {
                // font-size: 48px;
                // line-height: 6.4rem;
            }

            @screen lg {
                padding-top: 0;
            }

            @screen lg2 {
                // font-size: 72px;
                // line-height: 9.6rem;
            }
        }
    }

    * + &__callout {
        @apply my-16px;

        @screen md {
            @apply my-16px;
        }
    }

    * + &__subheader {
        @apply mt-16px;
    }

    &__subheader > a {
        @apply underline;
    }

    * + &__body,
    * + &__disclaimer {
        @apply mt-16px;

        @screen md {
            @apply mt-24px;
        }
    }

    &__body-animation {
        font-size: 2.8rem !important;
        color: lightgray;
        @apply font-univers55Roman;
        line-height: 150% !important;

        @screen lg {
            font-size: min(4rem, 2vw) !important;
        }

        @screen lg2 {
            font-size: min(4.8rem, 2.55vw) !important;
        }

        span {
            transition: all 0.3s ease-out;
        }
    }

    &__disclaimer {
        > a {
            @apply underline;
        }
    }

    &__footer {
        @apply flex flex-wrap gap-16px items-end;

        &.no-animation a {
            animation: none !important;
            transition: none !important;
        }
    }

    * + &__footer {
        @apply mt-24px;

        @screen md {
            @apply mt-16;
        }
    }

    &--color-light {
        @apply text-white;
    }

    &--color-white {
        @apply text-white;
    }

    &--color-dark {
        @apply text-black;
    }

    &--text-horizontally-align {
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100%;
    }

    &__logos + * {
        margin-top: 24px !important;

        @screen md {
            @apply mt-16;
        }
    }

    &__logos.primary-text__logos--md-no-wrap {
        @screen md {
            flex-wrap: nowrap;
        }
    }

    &__logo {
        position: relative;
        max-height: 72px !important;
        width: auto !important;
        height: auto !important;
        max-width: 72px;

        @screen md {
            max-height: 72px !important;
        }
    }

    &__logo-container {
        width: 100%;
        max-height: 72x;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;

        &--left {
            justify-content: flex-start;
        }
    }

    &__label {
        @apply flex justify-center items-center;
        padding-bottom: 16px;

        &--left {
            @apply justify-center md:justify-start;
        }

        &--right {
            @apply justify-end;
        }
    }

    &--color-mobile {
        &-light {
            @screen md-max {
                @apply text-white;
            }
        }

        &-dark {
            @screen md-max {
                @apply text-black;
            }
        }
    }
}
