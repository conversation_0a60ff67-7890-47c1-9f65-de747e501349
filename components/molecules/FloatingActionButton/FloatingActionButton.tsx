import s from './FloatingActionButton.module.scss'
import cn from 'classnames'
import React, { FC, useCallback, useEffect, useState } from 'react'
import ChevronUpIcon from '@components/atoms/Icon/general/ChevronUpIcon'
import Timeout = NodeJS.Timeout

export type FloatingActionButtonProps = {
    fixed?: boolean
}

export const FloatingActionButton: FC<FloatingActionButtonProps> = (props) => {
    const { fixed = true } = props
    const [isVisible, setIsVisible] = useState(!fixed)
    const [timer, setTimer] = useState<null | Timeout>(null)

    const scrollToTop = useCallback(() => {
        window.scrollTo(0, 0)
    }, [])

    useEffect(() => {
        const scrollHandler = () => {
            if (timer) {
                clearTimeout(timer)
            }
            const newTimer = setTimeout(() => {
                if (
                    window.pageYOffset >= window.innerHeight * 2 &&
                    !isVisible
                ) {
                    setIsVisible(true)
                } else {
                    setIsVisible(false)
                }
            }, 100)
            setTimer(newTimer)
        }
        window.addEventListener('scroll', scrollHandler)
        return () => {
            window.removeEventListener('scroll', scrollHandler)
        }
    }, [])

    return (
        <button
            className={cn(
                'floating-action-button',
                s['floating-action-button'],
                {
                    [s['floating-action-button--fixed']]: fixed,
                    [s['floating-action-button--active']]: isVisible
                }
            )}
            onClick={scrollToTop}
            onKeyPress={scrollToTop}
        >
            <ChevronUpIcon className={s['floating-action-button__icon']} />
        </button>
    )
}
