
.expandingDropdown__expanded .expandingDropdown__section-title  {
    @screen md-max {
        transform: translateX(30px);
    }
}
.expandingDropdown {
    @apply max-w-full;
    width: 286px;
    @screen md-max {
        width: 100%;
    }
    &__right-icon {
        @screen md {
            position: absolute;
            left: 80%;
            top: 35%;
        }
        @screen md-max {
            position: absolute;
            left: 90%;
            top: 30%;
        }
    }
    &__right-icon-hidden {
        @screen md-max {
            opacity: 0;
            display: none;
        }
    }
    &__left-icon {
        @screen md {
            display: none;
        }
        @screen md-max {
            position: absolute;
            right: 90%;
            top: 30%;
            opacity: 1;
        }
    }

    &__left-icon-hidden {
            display: none;
    }
    &__icon {
        @apply transition-transform duration-300;
    }

    &__list {
        width: 286px;
        padding: 0 20px;
        box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.06);
        &-hidden{
            opacity: 0;
            visibility: hidden;
            transition: all .3s;
        }
        @screen md {
            margin-bottom: 24px;
        }
        @media only screen and (max-width:767px) {
            width: 100%;
        }
        @screen md-max {
            margin-top: 16px;
        }
        &--dark {
            background-color: var(--white) !important;
        }
    }

    &__button {
        @apply w-full justify-between;

        &--white {
            background-color: var(--white) !important;
            color: var(--charcoal) !important;
        }
    }

    &__option {
        @apply w-full text-left py-16px;
        @screen md {
            padding-bottom: 0;
            margin-bottom: 25px;
        }

        &:focus, &:active {
            @apply outline-none text-content-blue;
        }
    }

    &__item {
        &--selected {
            font-weight: 700;
        }

        &:first-child {
            @apply pt-6px;
        }

        &:last-child {
            padding-bottom: 3px;
            .expandingDropdown__section{
                &-title {
                    border-bottom: 0
                }
            }
        }
    }

    &__item-open-mobile {
        @screen md-max {
            display: block;
        }
    }
    &__item-close-mobile {
        @screen md-max {
            display: none;
        }
    }

    &__item + &__item > .expandingDropdown__option {
        @apply border-t border-light-grey-1;
    }

    &__section {
        position: relative;
        @apply pt-16px;
        &:hover {
            color: var(--primitive-blue-90);
        }
        .expandingDropdown__item {
            @apply px-8;
        }
        &-title {
            &:hover {
                color: var(--primitive-blue-90);
                @screen md {
                    transform: translateX(16px);
                    transition: transform .2s ease-in-out;
                }
            }

        }
        &-title-open {
            @screen md-max {
                transform: translateX(35px);
            }
        }
        &-line:not(:first-child) {
            @apply relative border-b border-light-grey-1 pb-4;
        }
        &-title--dark {
            @apply text-small-copy;
        }
    }

    li:last-of-type &__section-line {
        @apply border-b-0;
    }
    li:first-of-type &__section-line {
        @screen md-max {
            margin-top: 10px;
        }
    }

    &__expanded {
        vertical-align: top;
        margin-left: 2px;
        display: inline-block;
        background-color: var(--white);
        height: auto;
        border-radius: 6px;
        @screen md {
            padding: 24px;
            width: calc(100% - 286px);
        }
        @screen md-max {
            max-height: 370px;
            height: 100%;
            overflow-y: auto;
        }
    }

    &__expanded-close {
        height: 0;
    }

    &__dropdown {
        line-height: 0;
        border-radius: 6px;
        background: var(--primitives-white-white, #FFF);
        box-shadow: 0 0 50px 0 rgba(0, 0, 0, 0.15);
        @media only screen and (max-width:767px) {
            width: 100%;
        }
        @screen md {
            max-width: 926px;
            width: 100vw;
            display: flex;
        }

    }
    &__dropdown-close {
        display: none;
    }

    .expandingDropdown__list-expanded {
        border-width: 0 !important ;
        box-shadow: none !important;
    }

    &__product {
        background: var(--primitives-gray-gray-20, #EAEAEA);
        box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.10);
        display: flex;
        width: 140px;
        min-height: 110px;
        padding: 8px 16px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        @screen md-max {
            display: flex;
            flex-direction: row;
            background: none;
            box-shadow: none;
            width: 100%;
            min-height: 52px;
            justify-content: flex-start;
            border-bottom: 1px solid #eaeaea;
            border-radius: 0 ;
        }
    }
    &__product-mobile  {
        @screen md-max {
            display: flex;
            font-size: 14px;
            font-style: normal !important;
            width: 100% !important;
            @apply font-univers55Roman text-base;
            color: var(--primitive-gray-110);
            text-transform: none;
        }
    }

    &--open {
        .expandingDropdown__list {
            @apply border-0 border-light-grey-1 rounded-lg bg-white;
        }

        .expandingDropdown__icon {
            @apply transform -rotate-180;
        }

    }

    &--full_width {
        width: 100%;
    }
}
