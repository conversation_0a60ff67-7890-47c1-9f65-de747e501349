.geolocation-banner {
    @apply text-dark-grey-1;
    padding: 16px;
    gap: 80px;
    max-width: 100%;
    z-index: 21;
    position: relative;
    margin: 0;
    max-height: 100%;
    @apply px-0;

    &__label {
        @apply flex items-start;
        max-width: 550px;
        @screen md-max {
            margin-right: 24px;
        }
    }

    &__country {
        @screen md-max {
            width: 100%;
            height: auto;
        }
    }

    &__dropdown {
        justify-content: space-between;
        align-items: center;
        @screen md-max {
            width: 207px;
            max-width: 100%;
        }
    }

    .continue-button {
        display: flex;
        align-items: center;
        @screen md-max {
            justify-content: center;
            max-width: 100%;
            align-items: center;
        }
    }

    &__icon {
        width: 24px;
        height: 24px;
        justify-content: center;
        align-items: center;
        display: flex;
        @screen md-max {
            position: absolute;
            top: 16px;
            right: 16px;
        }
    }

    &__icon-wrapper {
        @apply flex items-center relative justify-center;
        @apply text-white;
        gap: 10px;
        left: 100px;
    }
}
