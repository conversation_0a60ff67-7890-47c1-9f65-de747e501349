import s from './ScrollDownIndicator.module.scss'
import cn from 'classnames'
import { FC } from 'react'
import { Icon } from '@components/atoms/Icon/Icon'

type ScrollDownIndicatorProps = {
    onClick?: () => void
}

export const ScrollDownIndicator: FC<ScrollDownIndicatorProps> = ({
    onClick
}) => {
    return (
        <div
            className={cn(
                'absolute w-full bottom-0 flex justify-center items-center pb-20'
            )}
        >
            <div
                onClick={onClick}
                role="button"
                onKeyPress={onClick}
                tabIndex={0}
                className={cn(
                    s['scrolldownindicator'],
                    'z-2 flex justify-center items-center bg-white rounded-full p-8px'
                )}
            >
                <Icon name="arrowDown" className="w-24px h-24px" />
            </div>
        </div>
    )
}
