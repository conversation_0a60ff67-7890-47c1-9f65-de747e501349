import React, { FC } from 'react'
import s from './SliderPagination.module.scss'

export type SliderPaginationProps = {
    dots: JSX.Element[]
    activeIndex?: number
}

const SliderPagination: FC<SliderPaginationProps> = (props) => {
    const { dots, activeIndex } = props
    return (
        <ul className={s['slider-pagination']}>
            {dots &&
                dots.map((item, index) => {
                    const child =
                        item.props && item.props.children
                            ? item.props.children
                            : item
                    if (child.props) {
                        child.props.active = activeIndex === index
                    }
                    return (
                        <li
                            className={s['slider-pagination__item']}
                            key={index}
                        >
                            {child}
                        </li>
                    )
                })}
        </ul>
    )
}

export default SliderPagination
