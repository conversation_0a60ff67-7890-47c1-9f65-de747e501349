.view-ar {
    @apply relative flex flex-col justify-start items-center;
    @apply bg-charcoal text-white;
    border-radius: 8px;
    padding: 8px 12px;
    border: 2px solid var(--mid-grey-1);

    &__qr-transition {
        overflow: hidden;
        transition-property: all;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 0.3s;
        height: 132px;

        opacity: 0;
        max-height: 0;

        &--visible {
            opacity: 1;
            max-height: 132px;
        }
    }

    &__qr {
        @apply bg-white p-4px;
        padding: 4px;
        border-radius: 4px;
        width: 124px;
        height: 124px;

        &__svg {
            width: 100%;
        }
    }

    &__button {
        @apply flex flex-row items-center gap-8px pr-8px;
    }
}

.view-ar-android {
    @apply flex flex-row items-center justify-center gap-4px;
    @apply bg-charcoal text-white;
    padding: 4px 16px 4px 8px;
    border-radius: 8px;
}

.view-ar-ios {
    @apply flex flex-row items-center justify-center;
    @apply bg-charcoal text-white;
    padding: 4px 16px 4px 8px;
    border-radius: 8px;
}

.view-ar-overlay {
    &__popup {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40px 40px 32px;
        gap: 10px;
        background-color: var(--black);
        border: 2px solid var(--white);
        color: var(--white);
        box-shadow: 0 0 16px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        position: relative;
    }

    button.view-ar-overlay__close {
        position: absolute;
        top: 8px;
        right: 8px;

        svg {
            width: 24px;
            height: 24px;
            color: var(--white)
        }
    }

    &__video {
        width: 120px;
        height: 120px;
    }
}

