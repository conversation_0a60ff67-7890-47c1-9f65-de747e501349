import s from './ViewAr.module.scss'
import cn from 'classnames'
import { FC, useCallback, useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/router'
import { QRCodeSVG } from 'qrcode.react'
import ViewAR from '@components/atoms/Icon/general/ViewAR'
import { ViewArOverlay } from '@components/molecules/ViewArBanner/ViewArOverlay'
import {
    ImageType,
    VideoType
} from '@pylot-data/hooks/contentful/use-content-json'
import Image from 'next/image'
import { useTranslation } from 'next-i18next'

type ViewArProps = {
    usdzFileUrl: string
    glTfFileUrl: string
    buttonText: string
    video?: VideoType
    qrCodeImage?: ImageType
    videoDescription?: string
}

export const ViewAr: FC<ViewArProps> = ({
    usdzFileUrl,
    glTfFileUrl,
    buttonText,
    video,
    qrCodeImage,
    videoDescription
}) => {
    const { query, asPath } = useRouter()
    const [isQrCodeVisible, setIsQrCodeVisible] = useState(false)
    const [state, setState] = useState<'' | 'android' | 'ios'>('')
    const androidButtonRef = useRef<HTMLAnchorElement>(null)
    const iOSButtonRef = useRef<HTMLAnchorElement>(null)
    const { t } = useTranslation(['common'])

    const origin =
        typeof window !== 'undefined' && window.location.origin
            ? window.location.origin
            : ''

    const qrCodeValue = `${origin}${asPath}?ar`

    useEffect(() => {
        const isAndroid = /android/i.test(navigator.userAgent)
        if (isAndroid) {
            setState('android')
            return
        }

        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
        if (isIOS) {
            setState('ios')
        }
    }, [])

    useEffect(() => {
        if (state === 'ios' && query.ar === '' && iOSButtonRef.current) {
            iOSButtonRef.current?.focus()
            iOSButtonRef.current?.click()
            return
        }
    }, [query.ar, state])

    const handleClick = useCallback(() => {
        setIsQrCodeVisible((prevState) => !prevState)
    }, [])

    if (origin && state === 'android') {
        const href = `intent://arvr.google.com/scene-viewer/1.0?file=${glTfFileUrl}&mode=ar_only#Intent;scheme=https;package=com.google.android.googlequicksearchbox;action=android.intent.action.VIEW;S.browser_fallback_url=${origin}${asPath};end;`

        return (
            <>
                <a
                    href={href}
                    className={s['view-ar-android']}
                    ref={androidButtonRef}
                    aria-label={t('ada|Opens in the current Tab')}
                >
                    <ViewAR />
                    <span className="button-text">{buttonText}</span>
                </a>
                {query.ar === '' && (
                    <ViewArOverlay
                        buttonHref={href}
                        buttonText={buttonText}
                        video={video}
                        videoDescription={videoDescription}
                    />
                )}
            </>
        )
    }

    if (origin && state === 'ios') {
        return (
            <div className={s['view-ar-ios']}>
                <div className="relative">
                    <a
                        download
                        rel="ar"
                        href={usdzFileUrl}
                        ref={iOSButtonRef}
                        className="flex flex-row items-center justify-center gap-4px"
                        aria-label={t('ada|Opens in the current Tab')}
                    >
                        <img
                            alt={qrCodeImage?.description || ''}
                            className="w-32px h-32px invisible"
                        />
                        <span className="button-text">{buttonText}</span>
                    </a>
                    <ViewAR className="absolute top-0 left-0" />
                </div>
            </div>
        )
    }

    return (
        <button
            className={cn(s['view-ar'], 'md-max:hidden')}
            onClick={handleClick}
        >
            <div
                className={cn(s['view-ar__qr-transition'], {
                    [s['view-ar__qr-transition--visible']]: isQrCodeVisible
                })}
            >
                <div className={s['view-ar__qr']}>
                    {qrCodeImage && (
                        <Image
                            src={qrCodeImage.file.url}
                            width={116}
                            height={116}
                            alt={qrCodeImage?.description || ''}
                        />
                    )}
                    {!qrCodeImage && origin && (
                        <QRCodeSVG
                            className={s['view-ar__qr__svg']}
                            value={qrCodeValue}
                            size={116}
                            // eslint-disable-next-line i18next/no-literal-string
                            bgColor="#ffffff"
                            fgColor="#111111"
                        />
                    )}
                </div>
            </div>
            <div className={s['view-ar__button']}>
                <ViewAR />
                {buttonText}
            </div>
        </button>
    )
}
