import s from './ViewAr.module.scss'
import React, { FC, useEffect, useMemo, useRef, useState } from 'react'
import { Overlay } from '@components/organisms/Overlay/Overlay'
import { OverlayCloseButton } from '@components/organisms/Overlay/OverlayCloseButton'
import { Icon } from '@components/atoms/Icon/Icon'
import { Button } from '@components/molecules/Button/Button'
import { VideoType } from '@pylot-data/hooks/contentful/use-content-json'
import { nanoid } from 'nanoid'
import { useAnimationAndVideosToggle } from '@components/common/AnimationAndVideosToggle/AnimationAndVideoContext'

type ViewArOverlayProps = {
    buttonText: string
    buttonHref: string
    video?: VideoType
    videoDescription?: string
}

export const ViewArOverlay: FC<ViewArOverlayProps> = ({
    buttonText,
    buttonHref,
    video,
    videoDescription
}) => {
    const [isOpen, setIsOpen] = useState(true)
    const videoRef = useRef<HTMLVideoElement>(null)
    const { isAnimationStopped } = useAnimationAndVideosToggle()

    useEffect(() => {
        if (!isAnimationStopped) {
            videoRef?.current?.play()
        } else {
            videoRef?.current?.pause()
        }
    }, [isAnimationStopped])

    const onClose = () => {
        setIsOpen(false)
    }
    const videoDescriptionId = useMemo(() => nanoid(), [])

    let videoDescAttributes
    if (videoDescription) {
        videoDescAttributes = {
            tabIndex: 0,
            role: 'img',
            'aria-describedby': `video-description-${videoDescriptionId}`
        }
    }

    return (
        <Overlay
            className={s['view-ar-overlay']}
            // eslint-disable-next-line i18next/no-literal-string
            theme="dark"
            // eslint-disable-next-line i18next/no-literal-string
            closeButtonStyle="hidden"
            closable
            isOpen={isOpen}
            onClose={onClose}
        >
            <div className={s['view-ar-overlay__popup']}>
                <OverlayCloseButton
                    onClick={onClose}
                    className={s['view-ar-overlay__close']}
                />
                {!!video && (
                    <div {...videoDescAttributes}>
                        <video
                            autoPlay
                            playsInline
                            loop
                            muted
                            className={s['view-ar-overlay__video']}
                            controls={false}
                            ref={videoRef}
                        >
                            <track kind="captions" />
                            <source src={video.file.url} type="video/mp4" />
                        </video>
                        {videoDescription && videoDescAttributes && (
                            <p
                                className="sr-only"
                                id={`video-description-${videoDescriptionId}`}
                            >
                                {videoDescription}
                            </p>
                        )}
                    </div>
                )}
                <Button
                    className={s['hardware-hotspot-content__link']}
                    href={buttonHref}
                    variant="tertiary"
                    // eslint-disable-next-line i18next/no-literal-string
                    color="light"
                    // eslint-disable-next-line i18next/no-literal-string
                    iconAlignment="right"
                    label={buttonText}
                >
                    {buttonText}
                    {/* eslint-disable-next-line i18next/no-literal-string */}
                    <Icon name="chevronRight" />
                </Button>
            </div>
        </Overlay>
    )
}
