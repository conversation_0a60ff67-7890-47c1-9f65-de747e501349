.product-hotspot {
    @apply flex gap-12px items-center;

    &--small {
        @apply gap-8px;
    }

    &--left {
        .product-hotspot__hotspot {
            order: 1;
        }
    }

    &__popup {
        @apply absolute hidden opacity-0 -z-1 duration-300;

        &--active {
            @apply block opacity-100 z-1 -top-16px -left-16px;

            @screen md {
                @apply -top-32px -left-32px;
            }
        }
    }
}
