import React, {
    FC,
    HTMLAttributes,
    ReactNode,
    useState,
    useRef,
    useEffect
} from 'react'
import s from './ProductHotspot.module.scss'
import cn from 'classnames'
import { Hotspot } from '@components/atoms/Hotspot/Hotspot'
import { HighlightCallout } from '@components/atoms/HighlightCallout/HighlightCallout'

export type ProductHotspotProps = {
    text: string
    color?: 'dark' | 'light'
    size?: 'large' | 'small'
    direction?: 'left' | 'right'
    children?: ReactNode
    position?: HTMLAttributes<HTMLSelectElement>['style']
    mobilePosition?: HTMLAttributes<HTMLSelectElement>['style']
    className?: string
    mobileVariant?:
        | 'overlay-top'
        | 'overlay-bottom'
        | 'product-hotspot'
        | 'animated-product-hotspot'
}

export const ProductHotspot: FC<ProductHotspotProps> = (props) => {
    const {
        text,
        color = 'dark',
        size = 'large',
        direction = 'right',
        children,
        position,
        mobilePosition,
        className,
        mobileVariant
    } = props
    const [isActive, setIsActive] = useState(false)
    const popupRef = useRef<HTMLDivElement | null>(null)
    const hotspotIcon = size === 'small' ? null : 'add'
    const togglePopup = () => {
        setIsActive(!isActive)
    }
    const childrenWithProps = React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
            return React.cloneElement(child, { onClose: togglePopup })
        }
        return child
    })

    useEffect(() => {
        if (!isActive) return
        function handleClick(event: { target: any }) {
            if (
                popupRef.current &&
                !popupRef.current.contains(event.target as Node)
            ) {
                setIsActive(false)
            }
        }
        window.addEventListener('click', handleClick)
        return () => window.removeEventListener('click', handleClick)
    }, [isActive])
    /* eslint i18next/no-literal-string: ["error", { "ignoreAttribute": [ "mobileColor", "mobileSize", "color" ] }] */
    return (
        <div
            className={cn(
                s['product-hotspot'],
                s[`product-hotspot--${direction}`],
                s[`product-hotspot--${size}`],
                { ['absolute']: position },
                className
            )}
            style={position}
        >
            <Hotspot
                icon={childrenWithProps ? hotspotIcon : undefined}
                size={size}
                color={size === 'small' ? 'blue' : color}
                className={cn(s['product-hotspot__hotspot'])}
                onClick={childrenWithProps ? togglePopup : undefined}
                mobileColor="blue"
                mobileSize="small"
            />
            <HighlightCallout
                className={className}
                text={text}
                color={color}
                mobileVariant={mobileVariant}
            />
            {/*{className === 'product-callout__animated-product' ? (*/}
            {/*    <div className="hidden">*/}
            {/*        <HighlightCallout*/}
            {/*            className={className}*/}
            {/*            text={text}*/}
            {/*            color={color}*/}
            {/*        />*/}
            {/*    </div>*/}
            {/*) : (*/}
            {/*    <HighlightCallout text={text} color={color} />*/}
            {/*)}*/}
            {childrenWithProps && (
                <div
                    className={cn(s['product-hotspot__popup'], {
                        [s['product-hotspot__popup--active']]: isActive
                    })}
                    ref={popupRef}
                >
                    {childrenWithProps}
                </div>
            )}
        </div>
    )
}
