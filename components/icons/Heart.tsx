const Heart = ({ ...props }) => {
    return (
        <svg
            width="18"
            height="16"
            viewBox="0 0 18 16"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g
                id="Symbols"
                stroke="none"
                strokeWidth="1"
                fill="none"
                fillRule="evenodd"
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <g
                    id="d/header.utility"
                    transform="translate(-144.000000, -1.000000)"
                    stroke="currentColor"
                    strokeWidth="1.7"
                >
                    <path
                        d="M145.930671,8.05841492 L152.68325,15.8992801 C152.799654,16.0344928 152.989996,16.0326507 153.104883,15.8992801 L159.857083,8.05841492 L159.587875,8.39368357 C159.985241,7.91067566 160.269996,7.32856087 160.40005,6.68860303 C160.451996,6.43254621 160.479675,6.16727871 160.479675,5.89574796 C160.479675,3.74597594 158.766221,2.00294742 156.652366,2.00294742 C154.908579,2.00294742 153.437412,3.18928262 152.975966,4.81146706 L152.816716,4.80851964 C152.354891,3.18596677 150.883725,2 149.139937,2 C147.026462,2 145.313008,3.74266009 145.313008,5.89280054 C145.313008,6.1643313 145.340308,6.42923037 145.392254,6.68528719 C145.522308,7.32524503 145.807062,7.90772825 146.204808,8.39073615"
                        id="Wishlist"
                    />
                </g>
            </g>
        </svg>
    )
}

export default Heart
