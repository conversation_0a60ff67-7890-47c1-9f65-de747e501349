import { useTranslation } from 'next-i18next'

const CloseFullScreen = ({ ...props }) => {
    const { t } = useTranslation(['common'])

    return (
        <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            role="img"
            aria-labelledby="CloseFullScreen-Icon-title"
        >
            <title id="CloseFullScreen-Icon-title">
                {t('Close Full Screen')}
            </title>
            <path
                d="M17.4706 0.529412C17.763 0.821798 17.763 1.29585 17.4706 1.58824L10.0599 8.99894L17.4707 16.4118C17.7631 16.7042 17.763 17.1782 17.4707 17.4705C17.1782 17.7629 16.7041 17.7629 16.4118 17.4704L9.00106 10.0578L1.58823 17.4706C1.29585 17.763 0.821798 17.763 0.529412 17.4706C0.237026 17.1782 0.237026 16.7042 0.529412 16.4118L7.94224 8.99894L0.529552 1.58823C0.2371 1.29586 0.237069 0.821755 0.529482 0.529341C0.821841 0.236983 1.29584 0.236951 1.58824 0.529271L9.00106 7.94012L16.4118 0.529411C16.7042 0.237025 17.1782 0.237026 17.4706 0.529412Z"
                fill="currentColor"
            />
        </svg>
    )
}

export default CloseFullScreen
